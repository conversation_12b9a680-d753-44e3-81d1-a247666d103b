import { useState, useRef } from 'react'
import { Artifact } from '../../../types'

interface CodeArtifactViewerProps {
  artifact: Artifact
}

export function CodeArtifactViewer({ artifact }: CodeArtifactViewerProps) {
  const [copied, setCopied] = useState(false)
  const [isEditing, setIsEditing] = useState(false)
  const [editedContent, setEditedContent] = useState(artifact.content)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  const language = artifact.metadata.language || 'text'
  const isExecutable = isExecutableLanguage(language)

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(artifact.content)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy code:', error)
    }
  }

  const handleEdit = () => {
    setIsEditing(true)
    setEditedContent(artifact.content)
    setTimeout(() => {
      textareaRef.current?.focus()
    }, 0)
  }

  const handleSaveEdit = () => {
    // TODO: Update artifact content in store
    setIsEditing(false)
    console.log('Saving edited content:', editedContent)
  }

  const handleCancelEdit = () => {
    setIsEditing(false)
    setEditedContent(artifact.content)
  }

  const handleRun = () => {
    // TODO: Implement code execution
    console.log('Running code:', artifact.content)
  }

  const handleDownload = () => {
    const blob = new Blob([artifact.content], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${artifact.title.replace(/[^a-zA-Z0-9]/g, '_')}.${getFileExtension(language)}`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  return (
    <div className="h-full flex flex-col bg-neutral-900">
      {/* Minimal info bar */}
      <div className="flex-shrink-0 flex items-center justify-between p-2 bg-neutral-800/30 border-b border-neutral-700/50">
        <div className="flex items-center space-x-3">
          <span className="text-xs font-mono text-neutral-400">{language}</span>
          <span className="text-xs text-neutral-500">
            {artifact.content.split('\n').length} lines
          </span>
        </div>

        {/* Action buttons */}
        <div className="flex items-center space-x-1">
          {isEditing ? (
            <>
              <button
                onClick={handleSaveEdit}
                className="px-2 py-1 text-xs bg-primary hover:bg-primary/80 text-gray-900 rounded transition-colors"
                title="Save changes"
              >
                Save
              </button>
              <button
                onClick={handleCancelEdit}
                className="px-2 py-1 text-xs bg-neutral-700 hover:bg-neutral-600 text-white rounded transition-colors"
                title="Cancel editing"
              >
                Cancel
              </button>
            </>
          ) : (
            <>
              <button
                onClick={handleCopy}
                className="px-2 py-1 text-xs bg-neutral-700 hover:bg-neutral-600 text-white rounded transition-colors"
                title={copied ? "Copied!" : "Copy code"}
              >
                {copied ? "✓" : "Copy"}
              </button>
              <button
                onClick={handleEdit}
                className="px-2 py-1 text-xs bg-neutral-700 hover:bg-neutral-600 text-white rounded transition-colors"
                title="Edit code"
              >
                Edit
              </button>
              {isExecutable && (
                <button
                  onClick={handleRun}
                  className="px-2 py-1 text-xs bg-green-600 hover:bg-green-500 text-white rounded transition-colors"
                  title="Run code"
                >
                  Run
                </button>
              )}
              <button
                onClick={handleDownload}
                className="px-2 py-1 text-xs bg-neutral-700 hover:bg-neutral-600 text-white rounded transition-colors"
                title="Download file"
              >
                Download
              </button>
            </>
          )}
        </div>
      </div>

      {/* Code content */}
      <div className="flex-1 overflow-hidden">
        {isEditing ? (
          <textarea
            ref={textareaRef}
            value={editedContent}
            onChange={(e) => setEditedContent(e.target.value)}
            className="w-full h-full p-4 bg-neutral-900 text-neutral-100 font-mono text-sm resize-none border-none outline-none"
            spellCheck={false}
          />
        ) : (
          <div className="h-full overflow-auto">
            <pre className="p-4 text-sm font-mono text-neutral-100 whitespace-pre-wrap">
              <code className={`language-${language}`}>
                {artifact.content}
              </code>
            </pre>
          </div>
        )}
      </div>

      {/* Status bar */}
      <div className="flex-shrink-0 flex items-center justify-between px-4 py-2 bg-neutral-800 border-t border-neutral-700 text-xs text-neutral-400">
        <div>
          {artifact.content.length} characters • {artifact.content.split('\n').length} lines
        </div>
        <div>
          {language.toUpperCase()}
        </div>
      </div>
    </div>
  )
}

// Helper functions
function isExecutableLanguage(language: string): boolean {
  const executableLanguages = [
    'javascript', 'js', 'typescript', 'ts', 'python', 'py',
    'bash', 'sh', 'powershell', 'ps1', 'sql'
  ]
  return executableLanguages.includes(language.toLowerCase())
}

function getFileExtension(language: string): string {
  const extensions: Record<string, string> = {
    javascript: 'js',
    typescript: 'ts',
    python: 'py',
    java: 'java',
    cpp: 'cpp',
    c: 'c',
    csharp: 'cs',
    php: 'php',
    ruby: 'rb',
    go: 'go',
    rust: 'rs',
    swift: 'swift',
    kotlin: 'kt',
    scala: 'scala',
    sql: 'sql',
    bash: 'sh',
    powershell: 'ps1',
    yaml: 'yml',
    json: 'json',
    xml: 'xml',
    css: 'css',
    scss: 'scss',
    less: 'less',
    html: 'html'
  }
  
  return extensions[language.toLowerCase()] || 'txt'
}
