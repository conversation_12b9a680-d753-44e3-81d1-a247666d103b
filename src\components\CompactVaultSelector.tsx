import React, { useState, useEffect, useRef } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faFolder, faChevronDown, faPlus, faTimes } from '@fortawesome/free-solid-svg-icons'
import { ContextVault, ContextFolder } from '../types'
import { contextVaultService } from '../services/contextVaultService'
import { intelligenceAnalytics } from '../services/intelligenceAnalytics'

interface CompactVaultSelectorProps {
  selectedContextId?: string
  onContextChange?: (contextId: string | null) => void
  className?: string
}

export const CompactVaultSelector: React.FC<CompactVaultSelectorProps> = ({
  selectedContextId,
  onContextChange,
  className = ''
}) => {
  const [vaults, setVaults] = useState<ContextVault[]>([])
  const [selectedContext, setSelectedContext] = useState<ContextFolder | null>(null)
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [_loading] = useState(false)

  const dropdownRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    // Subscribe to context vault service
    const unsubscribe = contextVaultService.subscribe((updatedVaults, updatedSelectedContextId) => {
      setVaults(updatedVaults)

      // Update selected context if provided
      if (updatedSelectedContextId) {
        const result = contextVaultService.findContextById(updatedSelectedContextId)
        if (result) {
          setSelectedContext(result.context)
        }
      } else {
        setSelectedContext(null)
      }
    })

    // Initialize the service
    contextVaultService.initialize()

    return unsubscribe
  }, [])

  useEffect(() => {
    // Update selected context when prop changes
    if (selectedContextId) {
      const result = contextVaultService.findContextById(selectedContextId)
      if (result) {
        setSelectedContext(result.context)
      }
    } else {
      setSelectedContext(null)
    }
  }, [selectedContextId])

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const selectContext = (context: ContextFolder | null) => {
    setSelectedContext(context)
    setIsDropdownOpen(false)

    // Track analytics for context selection
    intelligenceAnalytics.trackContextSelection(
      context?.id || null,
      context ? 'manual' : 'cleared'
    )

    if (context) {
      contextVaultService.setSelectedContext(context.id)
      onContextChange?.(context.id)
    } else {
      contextVaultService.clearSelectedContext()
      onContextChange?.(null)
    }
  }

  const clearSelection = (e: React.MouseEvent) => {
    e.stopPropagation()
    selectContext(null)
  }

  // Get all contexts from all vaults for the dropdown
  const getAllAvailableContexts = (): ContextFolder[] => {
    const allContexts: ContextFolder[] = []
    for (const vault of vaults) {
      allContexts.push(...vault.contexts)
    }
    return allContexts
  }

  const availableContexts = getAllAvailableContexts()

  return (
    <div ref={dropdownRef} className={`relative ${className}`}>
      {/* Compact Selector Button */}
      <button
        type="button"
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        className={`
          flex items-center gap-2 px-3 py-2 rounded-lg border transition-colors text-sm
          ${selectedContext 
            ? 'border-primary/50 bg-primary/10 text-primary hover:bg-primary/20' 
            : 'border-tertiary/50 bg-gray-700 text-gray-300 hover:bg-gray-600'
          }
        `}
        title={selectedContext ? `Selected: ${selectedContext.name}` : 'Select context vault'}
      >
        <FontAwesomeIcon 
          icon={faFolder} 
          className="text-xs"
          style={{ color: selectedContext?.color }}
        />
        
        <span className="truncate max-w-32">
          {selectedContext ? selectedContext.name : 'No Context'}
        </span>
        
        {selectedContext && (
          <div
            onClick={(e) => {
              e.stopPropagation()
              clearSelection(e)
            }}
            className="ml-1 p-0.5 hover:bg-primary/30 rounded cursor-pointer"
            title="Clear selection"
          >
            <FontAwesomeIcon icon={faTimes} className="text-xs" />
          </div>
        )}
        
        <FontAwesomeIcon 
          icon={faChevronDown} 
          className={`text-xs transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`}
        />
      </button>

      {/* Dropdown Menu */}
      {isDropdownOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-neutral-900 border border-neutral-700 rounded-lg shadow-xl z-50 max-h-80 overflow-y-auto min-w-80">
          {/* No Context Option */}
          <button
            onClick={() => selectContext(null)}
            className={`w-full text-left p-4 hover:bg-neutral-800 transition-colors flex items-center gap-3 border-b border-neutral-800 ${
              !selectedContext ? 'bg-primary/20 text-primary border-primary/30' : 'text-neutral-300'
            }`}
          >
            <div className="w-8 h-8 rounded-lg bg-neutral-700 flex items-center justify-center">
              <FontAwesomeIcon icon={faFolder} className="text-sm text-neutral-400" />
            </div>
            <div className="flex-1">
              <div className="font-medium">No Context Selected</div>
              <div className="text-xs text-neutral-500 mt-0.5">Files will be saved to shared dropbox</div>
            </div>
          </button>

          {/* Available Contexts grouped by vault */}
          {vaults.map((vault) => (
            <div key={vault.id}>
              {vault.contexts.length > 0 && (
                <>
                  {/* Vault header */}
                  <div className="px-4 py-3 text-xs font-semibold text-neutral-400 bg-neutral-800/50 border-b border-neutral-800 flex items-center gap-2">
                    <FontAwesomeIcon
                      icon={vault.icon ? vault.icon as any : faFolder}
                      className="text-sm"
                      style={{ color: vault.color }}
                    />
                    <span className="uppercase tracking-wider">{vault.name}</span>
                  </div>

                  {/* Vault contexts */}
                  {vault.contexts.map((context) => (
                    <button
                      key={context.id}
                      onClick={() => selectContext(context)}
                      className={`w-full text-left p-4 hover:bg-neutral-800 transition-colors flex items-center gap-3 ${
                        selectedContext?.id === context.id ? 'bg-primary/20 text-primary border-l-2 border-l-primary' : 'text-neutral-300 border-l-2 border-l-transparent'
                      }`}
                    >
                      <div
                        className="w-8 h-8 rounded-lg flex items-center justify-center"
                        style={{ backgroundColor: `${context.color}20`, border: `1px solid ${context.color}40` }}
                      >
                        <FontAwesomeIcon
                          icon={faFolder}
                          className="text-sm"
                          style={{ color: context.color }}
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="font-medium truncate">{context.name}</div>
                        {context.description && (
                          <div className="text-xs text-neutral-500 truncate mt-0.5">{context.description}</div>
                        )}
                        <div className="text-xs text-neutral-600 mt-1">
                          {context.path?.split('/').pop() || 'Context folder'}
                        </div>
                      </div>
                      
                      {/* Context status indicator */}
                      <div className={`w-2 h-2 rounded-full ${
                        context.status === 'active' ? 'bg-green-400' :
                        context.status === 'growing' ? 'bg-yellow-400' :
                        context.status === 'archived' ? 'bg-gray-400' :
                        'bg-gray-600'
                      }`} />
                    </button>
                  ))}
                </>
              )}
            </div>
          ))}

          {/* No contexts available */}
          {availableContexts.length === 0 && (
            <div className="p-3 text-center text-gray-500 text-sm">
              No context vaults available
            </div>
          )}

          {/* Create new context option */}
          <div className="border-t border-tertiary/30 mt-1">
            <button
              onClick={() => {
                setIsDropdownOpen(false)
                // TODO: Implement create new context workflow
                console.log('Create new context clicked')
              }}
              className="w-full text-left p-3 hover:bg-gray-700/50 transition-colors flex items-center gap-2 text-gray-400"
            >
              <FontAwesomeIcon icon={faPlus} className="text-xs" />
              <span>Create New Context</span>
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
