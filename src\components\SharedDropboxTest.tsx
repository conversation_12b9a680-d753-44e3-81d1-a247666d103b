import React, { useState, useEffect } from 'react'
import { sharedDropboxService, SharedDropboxFile } from '../services/sharedDropboxService'
import { contextVaultService } from '../services/contextVaultService'

export const SharedDropboxTest: React.FC = () => {
  const [files, setFiles] = useState<SharedDropboxFile[]>([])
  const [selectedContext, setSelectedContext] = useState<any>(null)
  const [uploadDestination, setUploadDestination] = useState<any>(null)
  const [testStatus, setTestStatus] = useState<string>('')

  useEffect(() => {
    // Subscribe to file changes
    const unsubscribe = sharedDropboxService.subscribe(setFiles)
    
    // Get current context selection
    const context = contextVaultService.getSelectedContext()
    setSelectedContext(context)
    
    // Get upload destination
    sharedDropboxService.getUploadDestination().then(setUploadDestination)
    
    return unsubscribe
  }, [])

  const handleTestUpload = async () => {
    try {
      setTestStatus('Creating test file...')
      
      // Create a test file
      const testContent = `Test file created at ${new Date().toISOString()}\n\nThis is a test of the shared dropbox service.`
      const testBlob = new Blob([testContent], { type: 'text/plain' })
      const testFile = new File([testBlob], `test-${Date.now()}.txt`, { type: 'text/plain' })
      
      setTestStatus('Uploading file...')
      
      // Upload using shared dropbox service
      const result = await sharedDropboxService.uploadFile(testFile)
      
      if (result.success) {
        setTestStatus(`✅ Upload successful! File ID: ${result.fileRecord?.id}`)
        
        // Refresh destination info
        const destination = await sharedDropboxService.getUploadDestination()
        setUploadDestination(destination)
      } else {
        setTestStatus(`❌ Upload failed: ${result.error}`)
      }
    } catch (error) {
      setTestStatus(`❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  const handleRemoveFile = async (fileId: string) => {
    try {
      const result = await sharedDropboxService.removeFile(fileId)
      if (result.success) {
        setTestStatus(`✅ File removed successfully`)
      } else {
        setTestStatus(`❌ Remove failed: ${result.error}`)
      }
    } catch (error) {
      setTestStatus(`❌ Error removing file: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  return (
    <div className="p-6 bg-gray-800 rounded-lg border border-gray-700 max-w-4xl mx-auto">
      <h2 className="text-xl font-semibold text-white mb-4">Shared Dropbox Service Test</h2>
      
      {/* Current Status */}
      <div className="mb-6 space-y-2">
        <div className="text-sm text-gray-300">
          <strong>Selected Context:</strong> {selectedContext ? selectedContext.name : 'None (will use shared dropbox)'}
        </div>
        <div className="text-sm text-gray-300">
          <strong>Upload Destination:</strong> {uploadDestination ? (
            uploadDestination.type === 'context' 
              ? `Context Vault: ${uploadDestination.contextName}` 
              : 'Shared Dropbox'
          ) : 'Loading...'}
        </div>
        <div className="text-sm text-gray-300">
          <strong>Shared Files Count:</strong> {files.length}
        </div>
      </div>

      {/* Test Controls */}
      <div className="mb-6">
        <button
          onClick={handleTestUpload}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
        >
          Test Upload
        </button>
        {testStatus && (
          <div className="mt-2 p-2 bg-gray-700 rounded text-sm text-gray-300">
            {testStatus}
          </div>
        )}
      </div>

      {/* Files List */}
      <div>
        <h3 className="text-lg font-medium text-white mb-3">Shared Dropbox Files</h3>
        {files.length === 0 ? (
          <div className="text-gray-400 text-sm">No files in shared dropbox</div>
        ) : (
          <div className="space-y-2">
            {files.map(file => (
              <div key={file.id} className="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                <div className="flex-1">
                  <div className="text-white font-medium">{file.filename}</div>
                  <div className="text-sm text-gray-400">
                    Size: {(file.fileSize / 1024).toFixed(1)} KB | 
                    Uploaded: {new Date(file.uploadedAt).toLocaleString()} |
                    Processed: {file.processed ? '✅' : '⏳'}
                  </div>
                  <div className="text-xs text-gray-500 font-mono">{file.filepath}</div>
                </div>
                <button
                  onClick={() => handleRemoveFile(file.id)}
                  className="ml-4 px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded transition-colors"
                >
                  Remove
                </button>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Debug Info */}
      <details className="mt-6">
        <summary className="text-gray-400 cursor-pointer hover:text-gray-300">Debug Information</summary>
        <div className="mt-2 p-3 bg-gray-900 rounded text-xs text-gray-400 font-mono">
          <div><strong>Upload Destination:</strong></div>
          <pre>{JSON.stringify(uploadDestination, null, 2)}</pre>
          <div className="mt-2"><strong>Files Data:</strong></div>
          <pre>{JSON.stringify(files, null, 2)}</pre>
        </div>
      </details>
    </div>
  )
}

export default SharedDropboxTest
