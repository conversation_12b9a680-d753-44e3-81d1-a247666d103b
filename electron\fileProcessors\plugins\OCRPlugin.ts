/**
 * OCR (Optical Character Recognition) Plugin
 * Optional plugin for extracting text from images using Tesseract.js
 */

import * as fs from 'fs'
import * as path from 'path'
import * as mime from 'mime-types'
import { FileProcessorPlugin, ProcessedFileContent } from '../types'

export default class OCRPlugin implements FileProcessorPlugin {
  name = 'OCRPlugin'
  version = '1.0.0'
  description = 'Optional plugin for extracting text from images using OCR'
  author = 'ChatLo Team'
  dependencies = ['tesseract.js']
  optional = true

  supportedTypes = ['image']
  supportedExtensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']

  private tesseract: any = null

  async initialize(): Promise<void> {
    try {
      this.tesseract = require('tesseract.js')
      console.log('OCRPlugin (Tesseract.js) initialized successfully')
    } catch (error) {
      console.warn('tesseract.js not available, OCRPlugin will not be functional')
      throw error
    }
  }

  canProcess(filePath: string, fileType: string): boolean {
    if (!this.tesseract) return false
    
    const extension = path.extname(filePath).toLowerCase()
    
    // Only process images that are likely to contain text
    // Skip very large images or formats that are unlikely to have readable text
    return fileType === 'image' && this.supportedExtensions.includes(extension)
  }

  async process(filePath: string): Promise<ProcessedFileContent> {
    if (!this.tesseract) {
      return {
        error: 'OCR processing not available - tesseract.js module not loaded'
      }
    }

    try {
      const stats = await fs.promises.stat(filePath)
      const extension = path.extname(filePath).toLowerCase()
      const mimeType = mime.lookup(filePath) || 'unknown'
      const filename = path.basename(filePath)

      // Check file size (limit to 20MB for OCR processing)
      const maxSize = 20 * 1024 * 1024 // 20MB
      if (stats.size > maxSize) {
        return {
          error: `Image too large for OCR: ${Math.round(stats.size / 1024 / 1024)}MB (max: 20MB)`
        }
      }

      console.log(`Starting OCR processing for: ${filename}`)
      
      // Perform OCR with Tesseract.js
      const { data } = await this.tesseract.recognize(filePath, 'eng', {
        logger: (m: any) => {
          if (m.status === 'recognizing text') {
            console.log(`OCR Progress: ${Math.round(m.progress * 100)}%`)
          }
        }
      })

      const extractedText = data.text?.trim() || ''
      const confidence = data.confidence || 0

      // Analyze the OCR results
      const analysis = this.analyzeOCRResults(data)

      const description = `Image with OCR Text Extraction: ${filename}
File size: ${Math.round(stats.size / 1024)} KB
MIME type: ${mimeType}
OCR Confidence: ${Math.round(confidence)}%
Text Length: ${extractedText.length} characters
Words Detected: ${analysis.wordCount}

${extractedText ? `Extracted Text:\n${extractedText}` : 'No text detected in image'}`

      return {
        text: description,
        metadata: {
          filename,
          extension,
          mimeType,
          fileSize: stats.size,
          lastModified: stats.mtime,
          processor: this.name,
          processingLevel: 'ocr',
          
          // OCR-specific metadata
          ocrConfidence: Math.round(confidence),
          extractedText,
          textLength: extractedText.length,
          wordCount: analysis.wordCount,
          lineCount: analysis.lineCount,
          
          // OCR analysis
          hasText: extractedText.length > 0,
          textQuality: this.assessTextQuality(confidence, extractedText),
          
          // Tesseract data
          ocrData: {
            meanConfidence: data.confidence,
            symbols: data.symbols?.length || 0,
            words: data.words?.length || 0,
            lines: data.lines?.length || 0,
            paragraphs: data.paragraphs?.length || 0,
            blocks: data.blocks?.length || 0
          },
          
          processingTime: Date.now()
        }
      }

    } catch (error: any) {
      console.error('Error during OCR processing:', error)
      
      // Provide helpful error messages
      if (error.message?.includes('Invalid image')) {
        return {
          error: 'Invalid image format for OCR processing'
        }
      }
      
      if (error.message?.includes('timeout')) {
        return {
          error: 'OCR processing timed out - image may be too complex'
        }
      }

      return {
        error: `OCR processing failed: ${error?.message || 'Unknown error'}`
      }
    }
  }

  // Analyze OCR results for quality assessment
  private analyzeOCRResults(data: any) {
    const text = data.text || ''
    const words = text.split(/\s+/).filter((word: string) => word.length > 0)
    const lines = text.split('\n').filter((line: string) => line.trim().length > 0)

    return {
      wordCount: words.length,
      lineCount: lines.length,
      averageWordLength: words.length > 0 ? 
        Math.round(words.reduce((sum: number, word: string) => sum + word.length, 0) / words.length) : 0,
      hasNumbers: /\d/.test(text),
      hasSpecialChars: /[!@#$%^&*(),.?":{}|<>]/.test(text),
      languageDetected: this.detectLanguage(text)
    }
  }

  // Simple language detection based on character patterns
  private detectLanguage(text: string): string {
    if (!text || text.length < 10) return 'unknown'
    
    // Very basic language detection
    const englishPattern = /^[a-zA-Z0-9\s.,!?;:'"()-]+$/
    const hasEnglishWords = /\b(the|and|or|but|in|on|at|to|for|of|with|by)\b/i.test(text)
    
    if (englishPattern.test(text) && hasEnglishWords) {
      return 'english'
    }
    
    return 'unknown'
  }

  // Assess the quality of extracted text
  private assessTextQuality(confidence: number, text: string): string {
    if (!text || text.length === 0) return 'no-text'
    
    if (confidence >= 80) return 'high'
    if (confidence >= 60) return 'medium'
    if (confidence >= 40) return 'low'
    return 'very-low'
  }

  async cleanup(): Promise<void> {
    this.tesseract = null
    console.log('OCRPlugin cleaned up')
  }
}
