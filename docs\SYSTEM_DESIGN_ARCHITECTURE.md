# ChatLo Unified IPC System - System Design & Architecture

## Table of Contents
1. [System Overview](#system-overview)
2. [Application Design System](#application-design-system)
3. [Core Architecture](#core-architecture)
4. [Data Flow Diagrams](#data-flow-diagrams)
5. [Middleware Architecture](#middleware-architecture)
6. [Plugin System Design](#plugin-system-design)
7. [Frontend-Backend Integration](#frontend-backend-integration)
8. [Security Architecture](#security-architecture)
9. [Monitoring & Error Handling](#monitoring--error-handling)
10. [Performance Considerations](#performance-considerations)

## System Overview

The ChatLo Unified IPC System is a comprehensive inter-process communication framework built on Electron's IPC mechanism, providing a centralized, secure, and extensible API system.

### Key Design Principles
- **Centralization**: Single point of API registration and management
- **Security**: Built-in validation, authentication, and authorization
- **Extensibility**: Plugin system for custom API endpoints
- **Observability**: Comprehensive monitoring and error tracking
- **Type Safety**: Full TypeScript support throughout the system
- **Performance**: Optimized for high-throughput API operations

## Application Design System

### Component Hierarchy

```mermaid
graph TB
    subgraph "Application Layer"
        A[ChatLo Application] --> B[Main Process]
        A --> C[Renderer Process]
    end
    
    subgraph "Main Process Architecture"
        B --> D[APIRegistry Core]
        D --> E[Middleware Stack]
        D --> F[Plugin Manager]
        D --> G[Core Services]
        
        E --> H[Security Layer]
        E --> I[Validation Layer]
        E --> J[Monitoring Layer]
        
        G --> K[Database Manager]
        G --> L[File System Manager]
        G --> M[Vault Manager]
    end
    
    subgraph "Renderer Process Architecture"
        C --> N[UnifiedAPIClient]
        N --> O[React Hooks]
        N --> P[Component Layer]
        
        O --> Q[useAPI Hook]
        O --> R[useAPIMutation Hook]
        O --> S[Custom Hooks]
    end
    
    subgraph "Plugin Ecosystem"
        F --> T[Plugin Loader]
        T --> U[API Extension Plugins]
        T --> V[Standard Plugins]
        
        U --> W[Custom Endpoints]
        U --> X[Plugin Middleware]
    end
```

### Design System Colors & Styling

```typescript
// Design system constants used throughout the application
export const DesignSystem = {
  colors: {
    primary: '#2563eb',      // Blue-600
    secondary: '#64748b',    // Slate-500
    success: '#059669',      // Emerald-600
    warning: '#d97706',      // Amber-600
    error: '#dc2626',        // Red-600
    background: '#f8fafc',   // Slate-50
    surface: '#ffffff',      // White
    text: {
      primary: '#0f172a',    // Slate-900
      secondary: '#475569',  // Slate-600
      muted: '#94a3b8'       // Slate-400
    }
  },
  spacing: {
    xs: '0.25rem',   // 4px
    sm: '0.5rem',    // 8px
    md: '1rem',      // 16px
    lg: '1.5rem',    // 24px
    xl: '2rem',      // 32px
    xxl: '3rem'      // 48px
  },
  typography: {
    fontFamily: 'Inter, system-ui, sans-serif',
    fontSize: {
      xs: '0.75rem',   // 12px
      sm: '0.875rem',  // 14px
      base: '1rem',    // 16px
      lg: '1.125rem',  // 18px
      xl: '1.25rem',   // 20px
      '2xl': '1.5rem'  // 24px
    }
  }
}
```

## Core Architecture

### APIRegistry Core Design

```mermaid
classDiagram
    class APIRegistry {
        -categories: Map~string, APICategory~
        -globalMiddleware: Function[]
        -middlewareStack: DefaultMiddlewareStack
        -monitor: APIMonitor
        -securityManager: SecurityManager
        
        +registerCategory(name: string, middleware?: Function[]): void
        +registerEndpoint(category: string, name: string, handler: Function, options?: EndpointOptions): void
        +initialize(): void
        +getAllEndpoints(): APIEndpoint[]
        +getErrorStatistics(): ErrorStatistics
    }
    
    class APICategory {
        -endpoints: Map~string, APIEndpoint~
        -middleware: Function[]
        
        +addEndpoint(name: string, endpoint: APIEndpoint): void
        +getEndpoint(name: string): APIEndpoint
    }
    
    class APIEndpoint {
        +handler: Function
        +validator?: Function
        +validationSchema?: ValidationSchema
        +middleware?: Function[]
        +description?: string
        +requiresAuth?: boolean
        +requiredPermission?: string
        +rateLimit?: RateLimit
    }
    
    class DefaultMiddlewareStack {
        -requestMiddleware: MiddlewareFunction[]
        -responseMiddleware: MiddlewareFunction[]
        -errorHandler: ErrorHandler
        
        +getRequestMiddleware(): MiddlewareFunction[]
        +getResponseMiddleware(): MiddlewareFunction[]
        +getErrorHandler(): ErrorHandler
    }
    
    APIRegistry --> APICategory
    APICategory --> APIEndpoint
    APIRegistry --> DefaultMiddlewareStack
```

### Core Module Structure

```
electron/
├── api/
│   ├── APIRegistry.ts              # Core registry system
│   │   ├── class APIRegistry
│   │   ├── interface APIEndpoint
│   │   ├── interface APICategory
│   │   └── interface EndpointOptions
│   │
│   ├── middleware.ts               # Middleware system
│   │   ├── interface MiddlewareContext
│   │   ├── class DefaultMiddlewareStack
│   │   ├── RequestLoggingMiddleware
│   │   ├── RateLimitingMiddleware
│   │   ├── SecurityMiddleware
│   │   ├── ErrorHandlingMiddleware
│   │   ├── PerformanceMiddleware
│   │   └── ValidationMiddleware
│   │
│   ├── monitoring.ts               # Performance monitoring
│   │   ├── class APIMonitor
│   │   ├── interface APIMetrics
│   │   ├── interface SystemHealth
│   │   └── interface AlertConfig
│   │
│   ├── validation.ts               # Security & validation
│   │   ├── class APIValidator
│   │   ├── class SecurityManager
│   │   ├── interface ValidationSchema
│   │   ├── interface SecurityContext
│   │   └── interface ValidationResult
│   │
│   └── errorHandling.ts            # Error management
│       ├── class StructuredAPIError
│       ├── class ErrorHandler
│       ├── enum ErrorCode
│       ├── enum ErrorSeverity
│       ├── interface StructuredError
│       └── interface APIResponse
│
├── plugins/
│   ├── PluginManager.ts            # Plugin management
│   ├── extensionPoints.ts          # Plugin interfaces
│   └── examples/                   # Example plugins
│
└── main.ts                         # Main process entry
```

## Data Flow Diagrams

### Complete API Request Flow

```mermaid
sequenceDiagram
    participant FC as Frontend Component
    participant UC as UnifiedAPIClient
    participant EA as electronAPI
    participant AR as APIRegistry
    participant MS as MiddlewareStack
    participant SM as SecurityManager
    participant V as Validator
    participant M as Monitor
    participant H as Handler
    participant EH as ErrorHandler

    FC->>UC: apiClient.getConversations()
    UC->>EA: invoke('db:getConversations', args)
    EA->>AR: Route to registered endpoint
    
    AR->>MS: Execute request middleware
    MS->>SM: Security validation
    SM-->>MS: Security context
    MS->>V: Input validation
    V-->>MS: Validation result
    MS->>M: Start performance monitoring
    
    alt Validation Success
        MS->>H: Call endpoint handler
        H-->>MS: Handler result
        MS->>M: Record success metrics
        MS->>AR: Return success response
        AR->>EA: Structured success response
        EA->>UC: Response data
        UC->>FC: Typed result
    else Validation Failure
        MS->>EH: Handle validation error
        EH->>M: Record error metrics
        EH-->>AR: Structured error response
        AR->>EA: Error response
        EA->>UC: Error data
        UC->>FC: Throw APIError
    end
```

### Plugin API Registration Flow

```mermaid
sequenceDiagram
    participant PM as PluginManager
    participant P as Plugin
    participant AR as APIRegistry
    participant IPC as IPC Main

    PM->>P: Load plugin with API_EXTENSION capability
    PM->>P: Call getNamespace()
    P-->>PM: Return 'plugin_${pluginId}'
    
    PM->>P: Call registerEndpoints(apiRegistry)
    P->>AR: Register plugin endpoints
    
    loop For each endpoint
        P->>AR: registerEndpoint(category, name, handler, options)
        AR->>IPC: Create IPC handler for 'plugin_${pluginId}:${endpoint}'
    end
    
    P-->>PM: Return PluginAPINamespace
    PM->>AR: Store plugin namespace mapping
    
    Note over PM,IPC: Plugin APIs now available to frontend
```

### Error Handling Flow

```mermaid
flowchart TD
    A[API Request] --> B{Middleware Validation}
    B -->|Pass| C[Execute Handler]
    B -->|Fail| D[Create StructuredAPIError]
    
    C --> E{Handler Success}
    E -->|Success| F[Return Success Response]
    E -->|Error| G[Catch Handler Error]
    
    D --> H[ErrorHandler.handleError()]
    G --> H
    
    H --> I[Determine Error Code]
    I --> J[Set Error Severity]
    J --> K[Log Error]
    K --> L[Update Error Statistics]
    L --> M[Return Structured Error Response]
    
    F --> N[Frontend Receives Response]
    M --> N
```

## Middleware Architecture

### Middleware Execution Pipeline

```mermaid
graph LR
    A[API Request] --> B[Request Logging]
    B --> C[Rate Limiting]
    C --> D[Security Checks]
    D --> E[Input Validation]
    E --> F[Performance Start]
    F --> G[Handler Execution]
    G --> H[Performance End]
    H --> I[Response Processing]
    I --> J[Error Handling]
    J --> K[Response Logging]
    K --> L[API Response]
    
    subgraph "Global Middleware"
        B
        C
        D
        E
        F
        H
        I
        J
        K
    end
    
    subgraph "Plugin Middleware"
        M[Plugin Auth]
        N[Plugin Validation]
        O[Plugin Logging]
    end
    
    D --> M
    M --> N
    N --> O
    O --> E
```

### Middleware Context Structure

```typescript
interface MiddlewareContext {
  event: IpcMainInvokeEvent        // Electron IPC event
  category: string                 // API category (e.g., 'db', 'vault')
  endpoint: string                 // Endpoint name (e.g., 'getConversations')
  args: any[]                      // Request arguments
  startTime: number                // Request start timestamp
  metadata: Record<string, any>    // Additional context data
}

// Middleware function signature
type MiddlewareFunction = (context: MiddlewareContext) => Promise<void>
```

## Plugin System Design

### Plugin Architecture

```mermaid
graph TB
    subgraph "Plugin Ecosystem"
        A[Plugin Loader] --> B[Plugin Registry]
        B --> C[Capability Detection]
        C --> D{Has API_EXTENSION?}
        
        D -->|Yes| E[API Extension Plugin]
        D -->|No| F[Standard Plugin]
        
        E --> G[APIExtension Interface]
        G --> H[Namespace Generation]
        H --> I[Endpoint Registration]
        I --> J[IPC Handler Creation]
        
        F --> K[Standard Plugin Interface]
        K --> L[Plugin Activation]
    end
    
    subgraph "Plugin API Namespace"
        M[plugin_${pluginId}] --> N[Endpoint 1]
        M --> O[Endpoint 2]
        M --> P[Endpoint N]
        
        N --> Q[Handler Function]
        N --> R[Validation Schema]
        N --> S[Middleware]
    end
    
    I --> M
```

### Plugin Interface Design

```typescript
// Core plugin interface
interface Plugin {
  id: string
  name: string
  version: string
  description?: string
  capabilities: PluginCapability[]
  
  activate?(): Promise<void>
  deactivate?(): Promise<void>
}

// API extension capability
interface APIExtension {
  getNamespace(): string
  registerEndpoints(apiRegistry: APIRegistry): PluginAPINamespace
  provideMiddleware?(): Function[]
  discoverEndpoints?(): Promise<PluginEndpoint[]>
  validateEndpoint?(endpoint: string, args: any[]): boolean
}

// Plugin namespace structure
interface PluginAPINamespace {
  namespace: string                           // e.g., 'plugin_dataProcessor'
  endpoints: Map<string, PluginEndpoint>     // Endpoint definitions
  middleware: Function[]                      // Plugin-specific middleware
}
```

## Frontend-Backend Integration

### UnifiedAPIClient Architecture

```mermaid
classDiagram
    class UnifiedAPIClient {
        -timeout: number
        -retryAttempts: number
        -retryDelay: number
        
        +call~T~(category: string, endpoint: string, args: any[], options?: APICallOptions): Promise~T~
        +getConversations(): Promise~any[]~
        +createConversation(title: string): Promise~any~
        +callPluginAPI(pluginId: string, endpoint: string, ...args: any[]): Promise~any~
        +batchCall(calls: BatchCall[]): Promise~any[]~
        +healthCheck(): Promise~HealthStatus~
    }
    
    class APICallOptions {
        +timeout?: number
        +retries?: number
        +retryDelay?: number
        +validateResponse?: boolean
    }
    
    class APIError {
        +code: string
        +message: string
        +details?: any
        +timestamp: string
        +category?: string
        +endpoint?: string
    }
    
    UnifiedAPIClient --> APICallOptions
    UnifiedAPIClient --> APIError
```

### React Hooks Integration

```typescript
// Core API hook
export function useAPI<T = any>(
  apiCall: () => Promise<T>,
  options: UseAPIOptions = {}
): UseAPIState<T> {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Implementation with automatic retry, error handling, and cleanup
  // ...
}

// Specialized hooks for common operations
export function useConversations() {
  return useAPI(() => apiClient.getConversations())
}

export function useCreateConversation() {
  return useAPIMutation((title: string) => apiClient.createConversation(title))
}

// Plugin-specific hooks
export function usePluginAPI(pluginId: string, endpoint: string) {
  return useAPI(() => apiClient.callPluginAPI(pluginId, endpoint))
}
```

## Security Architecture

### Security Layer Design

```mermaid
graph TB
    subgraph "Security Architecture"
        A[API Request] --> B[Authentication Check]
        B --> C[Permission Validation]
        C --> D[Rate Limiting]
        D --> E[Input Sanitization]
        E --> F[Validation Schema]
        F --> G[Handler Execution]
        
        subgraph "Security Manager"
            H[User Context]
            I[Permission Store]
            J[Rate Limit Store]
            K[Sanitization Rules]
        end
        
        B --> H
        C --> I
        D --> J
        E --> K
    end
    
    subgraph "Security Context"
        L[User ID]
        M[Session ID]
        N[Permissions]
        O[Rate Limits]
    end
    
    H --> L
    H --> M
    I --> N
    J --> O
```

### Permission System

```typescript
// Permission structure
interface Permission {
  id: string                    // e.g., 'db.read', 'file.write'
  name: string                  // Human-readable name
  description: string           // Permission description
  category: string              // Permission category
  level: 'read' | 'write' | 'admin'
}

// Security context
interface SecurityContext {
  userId?: string
  sessionId?: string
  permissions: string[]
  rateLimit: {
    requests: number
    windowStart: number
  }
}

// Permission checking
class SecurityManager {
  hasPermission(context: SecurityContext, permissionId: string): boolean
  checkRateLimit(identifier: string, maxRequests: number, windowMs: number): boolean
  sanitizeInput(input: any): any
  validateSecurityContext(context: SecurityContext): ValidationResult
}
```

## Monitoring & Error Handling

### Monitoring Architecture

```mermaid
graph TB
    subgraph "Monitoring System"
        A[API Calls] --> B[Performance Metrics]
        A --> C[Error Tracking]
        A --> D[System Health]
        
        B --> E[Response Times]
        B --> F[Throughput]
        B --> G[Success Rates]
        
        C --> H[Error Codes]
        C --> I[Error Frequency]
        C --> J[Error Patterns]
        
        D --> K[Memory Usage]
        D --> L[CPU Usage]
        D --> M[Active Connections]
    end
    
    subgraph "Alerting System"
        N[Threshold Monitoring]
        O[Alert Generation]
        P[Notification System]
    end
    
    E --> N
    F --> N
    G --> N
    H --> N
    I --> N
    
    N --> O
    O --> P
```

### Error Code System

```typescript
enum ErrorCode {
  // General errors
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  INVALID_INPUT = 'INVALID_INPUT',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  TIMEOUT = 'TIMEOUT',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  
  // Authentication/Authorization
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  
  // Database errors
  DATABASE_ERROR = 'DATABASE_ERROR',
  RECORD_NOT_FOUND = 'RECORD_NOT_FOUND',
  DUPLICATE_RECORD = 'DUPLICATE_RECORD',
  
  // File system errors
  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
  FILE_ACCESS_DENIED = 'FILE_ACCESS_DENIED',
  
  // Plugin errors
  PLUGIN_NOT_FOUND = 'PLUGIN_NOT_FOUND',
  PLUGIN_EXECUTION_ERROR = 'PLUGIN_EXECUTION_ERROR'
}

enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}
```

## Performance Considerations

### Optimization Strategies

1. **Connection Pooling**: Reuse database connections
2. **Caching**: Cache frequently accessed data
3. **Batch Operations**: Group multiple operations
4. **Lazy Loading**: Load data on demand
5. **Compression**: Compress large responses
6. **Rate Limiting**: Prevent system overload

### Performance Metrics

```typescript
interface APIMetrics {
  totalCalls: number
  successfulCalls: number
  failedCalls: number
  averageResponseTime: number
  p95ResponseTime: number
  p99ResponseTime: number
  requestsPerSecond: number
  errorRate: number
  
  // Per-endpoint metrics
  endpointMetrics: Map<string, EndpointMetrics>
}

interface EndpointMetrics {
  calls: number
  averageTime: number
  minTime: number
  maxTime: number
  errors: number
  lastCalled: Date
}
```

## Implementation Code Examples

### 1. Core API Registration Example

```typescript
// In main.ts - Complete core API registration
private registerCoreAPIs(): void {
  // Database APIs
  this.apiRegistry.registerCategory('db', [
    this.middlewareStack.getRequestMiddleware()[0] // Logging middleware
  ])

  this.apiRegistry.registerEndpoint('db', 'getConversations',
    async () => {
      const conversations = await this.db.getConversations()
      return { success: true, data: conversations }
    },
    {
      description: 'Retrieve all conversations',
      validationSchema: {}, // No parameters
      requiredPermission: 'db.read',
      rateLimit: { maxRequests: 100, windowMs: 60000 }
    }
  )

  this.apiRegistry.registerEndpoint('db', 'createConversation',
    async (title: string) => {
      const conversation = await this.db.createConversation(title)
      return { success: true, data: conversation }
    },
    {
      description: 'Create a new conversation',
      validationSchema: {
        title: { type: 'string', min: 1, max: 200 }
      },
      requiredPermission: 'db.write'
    }
  )

  // Vault APIs
  this.apiRegistry.registerCategory('vault')

  this.apiRegistry.registerEndpoint('vault', 'readFile',
    async (filePath: string) => {
      const content = await this.vault.readFile(filePath)
      return { success: true, data: content }
    },
    {
      description: 'Read file content from vault',
      validationSchema: {
        filePath: { type: 'string', min: 1, pattern: /^[a-zA-Z0-9\-_\/\\\.]+$/ }
      },
      requiredPermission: 'vault.read'
    }
  )

  // Plugin APIs
  this.apiRegistry.registerCategory('plugins')

  this.apiRegistry.registerEndpoint('plugins', 'getAllPlugins',
    async () => {
      const plugins = await this.pluginManager.getAllPlugins()
      return { success: true, data: plugins }
    },
    {
      description: 'Get all available plugins',
      requiredPermission: 'plugins.read'
    }
  )
}
```

### 2. Complete Plugin Implementation Example

```typescript
// Complete plugin with API extension
import { Plugin, PluginCapability } from '../types'
import { APIExtension, PluginAPINamespace } from '../extensionPoints'
import { APIRegistry } from '../../api/APIRegistry'
import { StructuredAPIError, ErrorCode } from '../../api/errorHandling'

export class AdvancedDataPlugin implements Plugin, APIExtension {
  id = 'advancedData'
  name = 'Advanced Data Processor'
  version = '2.0.0'
  description = 'Advanced data processing with ML capabilities'
  capabilities = [PluginCapability.API_EXTENSION]

  private processingJobs: Map<string, ProcessingJob> = new Map()
  private mlModel: any = null

  async activate(): Promise<void> {
    // Initialize ML model
    this.mlModel = await this.loadMLModel()
    console.log(`[${this.name}] Plugin activated with ML model`)
  }

  async deactivate(): Promise<void> {
    // Cleanup resources
    this.processingJobs.clear()
    this.mlModel = null
    console.log(`[${this.name}] Plugin deactivated`)
  }

  getNamespace(): string {
    return `plugin_${this.id}`
  }

  registerEndpoints(apiRegistry: APIRegistry): PluginAPINamespace {
    const namespace: PluginAPINamespace = {
      namespace: this.getNamespace(),
      endpoints: new Map(),
      middleware: [this.authMiddleware.bind(this)]
    }

    // Data processing endpoint
    namespace.endpoints.set('processData', {
      handler: this.processData.bind(this),
      description: 'Process data using advanced ML algorithms',
      validationSchema: {
        data: { type: 'object' },
        algorithm: {
          type: 'string',
          enum: ['classification', 'regression', 'clustering']
        },
        options: { type: 'object', allowEmpty: true }
      },
      requiresAuth: true,
      requiredPermission: 'data.process',
      rateLimit: { maxRequests: 10, windowMs: 60000 }
    })

    // Batch processing endpoint
    namespace.endpoints.set('processBatch', {
      handler: this.processBatch.bind(this),
      description: 'Process multiple data items in batch',
      validationSchema: {
        items: { type: 'array', min: 1, max: 100 },
        algorithm: { type: 'string', enum: ['classification', 'regression', 'clustering'] },
        options: { type: 'object', allowEmpty: true }
      },
      requiresAuth: true,
      requiredPermission: 'data.process.batch',
      rateLimit: { maxRequests: 5, windowMs: 300000 } // 5 requests per 5 minutes
    })

    // Job status endpoint
    namespace.endpoints.set('getJobStatus', {
      handler: this.getJobStatus.bind(this),
      description: 'Get processing job status',
      validationSchema: {
        jobId: { type: 'string', min: 1 }
      },
      requiresAuth: true,
      requiredPermission: 'data.read'
    })

    // Model info endpoint
    namespace.endpoints.set('getModelInfo', {
      handler: this.getModelInfo.bind(this),
      description: 'Get ML model information',
      validationSchema: {},
      requiresAuth: false
    })

    return namespace
  }

  provideMiddleware(): Function[] {
    return [
      this.authMiddleware.bind(this),
      this.loggingMiddleware.bind(this)
    ]
  }

  async discoverEndpoints(): Promise<any[]> {
    return [
      {
        name: 'processData',
        description: 'Process data using ML',
        parameters: ['data', 'algorithm', 'options?'],
        returnType: 'ProcessingResult'
      },
      {
        name: 'processBatch',
        description: 'Batch process multiple items',
        parameters: ['items[]', 'algorithm', 'options?'],
        returnType: 'BatchProcessingResult'
      }
    ]
  }

  // Endpoint implementations
  private async processData(data: any, algorithm: string, options: any = {}): Promise<any> {
    try {
      if (!this.mlModel) {
        throw new StructuredAPIError(
          ErrorCode.PLUGIN_EXECUTION_ERROR,
          'ML model not initialized',
          { category: 'plugin', endpoint: 'processData' }
        )
      }

      const result = await this.runMLAlgorithm(data, algorithm, options)

      return {
        success: true,
        data: result,
        algorithm: algorithm,
        processingTime: result.processingTime,
        confidence: result.confidence
      }
    } catch (error) {
      throw new StructuredAPIError(
        ErrorCode.PLUGIN_EXECUTION_ERROR,
        `Data processing failed: ${error.message}`,
        { category: 'plugin', endpoint: 'processData', details: { algorithm, error: error.message } }
      )
    }
  }

  private async processBatch(items: any[], algorithm: string, options: any = {}): Promise<any> {
    const jobId = `batch_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`

    // Create processing job
    const job: ProcessingJob = {
      id: jobId,
      status: 'started',
      totalItems: items.length,
      processedItems: 0,
      results: [],
      startTime: new Date(),
      algorithm: algorithm,
      options: options
    }

    this.processingJobs.set(jobId, job)

    // Start background processing
    this.processBatchInBackground(jobId, items, algorithm, options)

    return {
      success: true,
      jobId: jobId,
      status: 'started',
      totalItems: items.length,
      estimatedTime: this.estimateProcessingTime(items.length, algorithm)
    }
  }

  private async getJobStatus(jobId: string): Promise<any> {
    const job = this.processingJobs.get(jobId)

    if (!job) {
      throw new StructuredAPIError(
        ErrorCode.RECORD_NOT_FOUND,
        `Job not found: ${jobId}`,
        { category: 'plugin', endpoint: 'getJobStatus' }
      )
    }

    return {
      success: true,
      job: {
        id: job.id,
        status: job.status,
        progress: {
          total: job.totalItems,
          processed: job.processedItems,
          percentage: Math.round((job.processedItems / job.totalItems) * 100)
        },
        results: job.results,
        startTime: job.startTime,
        endTime: job.endTime,
        processingTime: job.endTime ? job.endTime.getTime() - job.startTime.getTime() : null
      }
    }
  }

  private async getModelInfo(): Promise<any> {
    return {
      success: true,
      model: {
        name: 'Advanced ML Model v2.0',
        version: '2.0.0',
        algorithms: ['classification', 'regression', 'clustering'],
        loaded: !!this.mlModel,
        capabilities: [
          'Real-time processing',
          'Batch processing',
          'Confidence scoring',
          'Multi-algorithm support'
        ]
      }
    }
  }

  // Middleware implementations
  private async authMiddleware(context: any): Promise<void> {
    // Custom authentication logic for this plugin
    if (!context.metadata.userId) {
      throw new StructuredAPIError(
        ErrorCode.AUTHENTICATION_REQUIRED,
        'User authentication required for data processing',
        { category: 'plugin', endpoint: context.endpoint }
      )
    }
  }

  private async loggingMiddleware(context: any): Promise<void> {
    console.log(`[${this.name}] API call: ${context.endpoint} by user ${context.metadata.userId}`)
  }

  // Helper methods
  private async loadMLModel(): Promise<any> {
    // Simulate ML model loading
    return new Promise(resolve => {
      setTimeout(() => {
        resolve({ loaded: true, version: '2.0.0' })
      }, 1000)
    })
  }

  private async runMLAlgorithm(data: any, algorithm: string, options: any): Promise<any> {
    const startTime = Date.now()

    // Simulate ML processing
    await new Promise(resolve => setTimeout(resolve, 100))

    const endTime = Date.now()

    return {
      result: `Processed with ${algorithm}`,
      confidence: Math.random() * 0.3 + 0.7, // 0.7-1.0
      processingTime: endTime - startTime,
      algorithm: algorithm,
      options: options
    }
  }

  private async processBatchInBackground(jobId: string, items: any[], algorithm: string, options: any): Promise<void> {
    const job = this.processingJobs.get(jobId)
    if (!job) return

    try {
      for (let i = 0; i < items.length; i++) {
        const item = items[i]
        const result = await this.runMLAlgorithm(item, algorithm, options)

        job.results.push({
          index: i,
          item: item,
          result: result,
          success: true
        })

        job.processedItems = i + 1

        // Update job status
        if (i === items.length - 1) {
          job.status = 'completed'
          job.endTime = new Date()
        }
      }
    } catch (error) {
      job.status = 'failed'
      job.error = error.message
      job.endTime = new Date()
    }
  }

  private estimateProcessingTime(itemCount: number, algorithm: string): number {
    const baseTime = 100 // ms per item
    const algorithmMultiplier = {
      'classification': 1.0,
      'regression': 1.2,
      'clustering': 1.5
    }

    return itemCount * baseTime * (algorithmMultiplier[algorithm] || 1.0)
  }
}

// Supporting interfaces
interface ProcessingJob {
  id: string
  status: 'started' | 'processing' | 'completed' | 'failed'
  totalItems: number
  processedItems: number
  results: any[]
  startTime: Date
  endTime?: Date
  algorithm: string
  options: any
  error?: string
}
```

### 3. Frontend Integration Example

```typescript
// Complete React component using the unified API system
import React, { useState, useEffect } from 'react'
import {
  useConversations,
  useCreateConversation,
  usePluginOperation,
  useAPIHealth,
  useMonitoringData
} from '../hooks/useAPI'
import { apiClient } from '../api/UnifiedAPIClient'

export const ComprehensiveAPIExample: React.FC = () => {
  const [selectedPlugin, setSelectedPlugin] = useState<string>('')
  const [processingData, setProcessingData] = useState<any>(null)
  const [jobStatus, setJobStatus] = useState<any>(null)

  // Core API hooks
  const { data: conversations, loading: conversationsLoading, error: conversationsError, refetch } = useConversations()
  const createConversation = useCreateConversation()

  // Plugin operation hook
  const pluginOperation = usePluginOperation()

  // System monitoring hooks
  const apiHealth = useAPIHealth(10000) // Check every 10 seconds
  const { data: monitoringData } = useMonitoringData()

  // Plugin data processing example
  const handleDataProcessing = async () => {
    if (!selectedPlugin) return

    try {
      // Single data processing
      const singleResult = await apiClient.callPluginAPI(
        selectedPlugin,
        'processData',
        { value: 42, type: 'number' },
        'classification',
        { confidence: 0.8 }
      )

      console.log('Single processing result:', singleResult)

      // Batch processing
      const batchResult = await apiClient.callPluginAPI(
        selectedPlugin,
        'processBatch',
        [
          { value: 1, type: 'number' },
          { value: 2, type: 'number' },
          { value: 3, type: 'number' }
        ],
        'regression',
        { batchSize: 10 }
      )

      setProcessingData(batchResult)

      // Monitor job status
      if (batchResult.jobId) {
        monitorJobStatus(batchResult.jobId)
      }

    } catch (error) {
      console.error('Plugin API call failed:', error)
    }
  }

  // Monitor batch job status
  const monitorJobStatus = async (jobId: string) => {
    const checkStatus = async () => {
      try {
        const status = await apiClient.callPluginAPI(selectedPlugin, 'getJobStatus', jobId)
        setJobStatus(status.job)

        if (status.job.status === 'processing' || status.job.status === 'started') {
          setTimeout(checkStatus, 2000) // Check again in 2 seconds
        }
      } catch (error) {
        console.error('Failed to check job status:', error)
      }
    }

    checkStatus()
  }

  // Batch API operations example
  const handleBatchOperations = async () => {
    try {
      const results = await apiClient.batchCall([
        { category: 'db', endpoint: 'getConversations' },
        { category: 'db', endpoint: 'getFiles' },
        { category: 'vault', endpoint: 'getRegistry' },
        { category: 'plugins', endpoint: 'getAllPlugins' },
        { category: 'system', endpoint: 'getMonitoringData' }
      ])

      console.log('Batch operation results:', results)
    } catch (error) {
      console.error('Batch operations failed:', error)
    }
  }

  // Error handling and retry example
  const handleRetryableOperation = async () => {
    try {
      const result = await apiClient.call('db', 'getConversations', [], {
        timeout: 5000,
        retries: 3,
        retryDelay: 1000
      })

      console.log('Retryable operation result:', result)
    } catch (error) {
      console.error('Operation failed after retries:', error)
    }
  }

  return (
    <div className="comprehensive-api-example p-6 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold mb-8">ChatLo Unified API System Demo</h1>

      {/* System Health Status */}
      <div className="mb-8 p-4 border rounded-lg bg-gray-50">
        <h2 className="text-xl font-semibold mb-4">System Health</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className={`p-3 rounded ${apiHealth?.healthy ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
            <div className="font-semibold">API Health</div>
            <div>{apiHealth?.healthy ? 'Healthy' : 'Unhealthy'}</div>
            <div className="text-sm">Latency: {apiHealth?.latency || 0}ms</div>
          </div>

          <div className="p-3 rounded bg-blue-100 text-blue-800">
            <div className="font-semibold">Total API Calls</div>
            <div>{monitoringData?.data?.totalCalls || 0}</div>
            <div className="text-sm">Success Rate: {monitoringData?.data?.successRate || 0}%</div>
          </div>

          <div className="p-3 rounded bg-purple-100 text-purple-800">
            <div className="font-semibold">Avg Response Time</div>
            <div>{monitoringData?.data?.averageResponseTime || 0}ms</div>
            <div className="text-sm">P95: {monitoringData?.data?.p95ResponseTime || 0}ms</div>
          </div>
        </div>
      </div>

      {/* Core API Operations */}
      <div className="mb-8 p-4 border rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Core API Operations</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Conversations */}
          <div>
            <h3 className="text-lg font-medium mb-2">Conversations</h3>
            {conversationsLoading ? (
              <div>Loading conversations...</div>
            ) : conversationsError ? (
              <div className="text-red-600">Error: {conversationsError}</div>
            ) : (
              <div className="space-y-2">
                {conversations?.slice(0, 3).map((conv: any) => (
                  <div key={conv.id} className="p-2 bg-gray-100 rounded">
                    {conv.title}
                  </div>
                ))}
                <button
                  onClick={() => createConversation.mutate('New Test Conversation')}
                  disabled={createConversation.loading}
                  className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
                >
                  {createConversation.loading ? 'Creating...' : 'Create New'}
                </button>
              </div>
            )}
          </div>

          {/* Batch Operations */}
          <div>
            <h3 className="text-lg font-medium mb-2">Batch Operations</h3>
            <div className="space-y-2">
              <button
                onClick={handleBatchOperations}
                className="px-4 py-2 bg-green-500 text-white rounded"
              >
                Execute Batch Operations
              </button>
              <button
                onClick={handleRetryableOperation}
                className="px-4 py-2 bg-orange-500 text-white rounded"
              >
                Test Retry Logic
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Plugin API Operations */}
      <div className="mb-8 p-4 border rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Plugin API Operations</h2>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Select Plugin:</label>
            <select
              value={selectedPlugin}
              onChange={(e) => setSelectedPlugin(e.target.value)}
              className="px-3 py-2 border rounded w-full max-w-xs"
            >
              <option value="">Select a plugin...</option>
              <option value="advancedData">Advanced Data Processor</option>
              <option value="fileManager">File Manager</option>
              <option value="dataProcessor">Data Processor</option>
            </select>
          </div>

          {selectedPlugin && (
            <div className="space-y-4">
              <button
                onClick={handleDataProcessing}
                disabled={pluginOperation.loading}
                className="px-4 py-2 bg-purple-500 text-white rounded disabled:opacity-50"
              >
                {pluginOperation.loading ? 'Processing...' : 'Process Data'}
              </button>

              {processingData && (
                <div className="p-4 bg-gray-100 rounded">
                  <h4 className="font-medium mb-2">Processing Result:</h4>
                  <pre className="text-sm overflow-auto">
                    {JSON.stringify(processingData, null, 2)}
                  </pre>
                </div>
              )}

              {jobStatus && (
                <div className="p-4 bg-blue-100 rounded">
                  <h4 className="font-medium mb-2">Job Status:</h4>
                  <div className="space-y-1">
                    <div>Status: <span className="font-medium">{jobStatus.status}</span></div>
                    <div>Progress: {jobStatus.progress?.percentage || 0}%</div>
                    <div>Processed: {jobStatus.progress?.processed || 0} / {jobStatus.progress?.total || 0}</div>
                    {jobStatus.processingTime && (
                      <div>Processing Time: {jobStatus.processingTime}ms</div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Error Handling Example */}
      <div className="p-4 border rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Error Handling</h2>
        <div className="space-y-2">
          {createConversation.error && (
            <div className="p-3 bg-red-100 text-red-800 rounded">
              <strong>Conversation Creation Error:</strong> {createConversation.error}
            </div>
          )}

          {pluginOperation.error && (
            <div className="p-3 bg-red-100 text-red-800 rounded">
              <strong>Plugin Operation Error:</strong> {pluginOperation.error}
            </div>
          )}

          {apiHealth && !apiHealth.healthy && (
            <div className="p-3 bg-yellow-100 text-yellow-800 rounded">
              <strong>System Health Warning:</strong> API system is experiencing issues
              <ul className="mt-1 text-sm">
                {apiHealth.errors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default ComprehensiveAPIExample
```

This comprehensive system design provides a robust, scalable, and maintainable architecture for the ChatLo unified IPC system, ensuring excellent performance, security, and developer experience.
