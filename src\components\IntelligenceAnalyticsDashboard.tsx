import React, { useState, useEffect } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faChartLine, faUsers, faCog, faLightbulb, faDownload, faTrash } from '@fortawesome/free-solid-svg-icons'
import { intelligenceAnalytics } from '../services/intelligenceAnalytics'

/**
 * Analytics Dashboard for Intelligence System
 * Displays comprehensive metrics and insights
 */
export const IntelligenceAnalyticsDashboard: React.FC = () => {
  const [analytics, setAnalytics] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadAnalytics()
  }, [])

  const loadAnalytics = () => {
    setLoading(true)
    try {
      const data = intelligenceAnalytics.getAnalyticsSummary()
      setAnalytics(data)
    } catch (error) {
      console.error('Error loading analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleExportAnalytics = () => {
    const data = intelligenceAnalytics.exportAnalytics()
    const blob = new Blob([data], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `chatlo-intelligence-analytics-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const handleClearAnalytics = () => {
    if (confirm('Are you sure you want to clear all analytics data? This action cannot be undone.')) {
      intelligenceAnalytics.clearAnalytics()
      loadAnalytics()
    }
  }

  if (loading) {
    return (
      <div className="p-6 bg-gray-800 rounded-lg border border-tertiary/50">
        <div className="flex items-center justify-center h-32">
          <div className="text-gray-400">Loading analytics...</div>
        </div>
      </div>
    )
  }

  if (!analytics) {
    return (
      <div className="p-6 bg-gray-800 rounded-lg border border-tertiary/50">
        <div className="flex items-center justify-center h-32">
          <div className="text-gray-400">No analytics data available</div>
        </div>
      </div>
    )
  }

  const { userEngagement, technicalPerformance, intelligenceQuality } = analytics

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <FontAwesomeIcon icon={faChartLine} className="text-primary text-lg" />
          <h3 className="text-lg font-semibold text-white">Intelligence System Analytics</h3>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={loadAnalytics}
            className="px-3 py-1 bg-primary/20 text-primary text-xs rounded hover:bg-primary/30 transition-colors"
          >
            Refresh
          </button>
          <button
            onClick={handleExportAnalytics}
            className="px-3 py-1 bg-secondary/20 text-secondary text-xs rounded hover:bg-secondary/30 transition-colors"
            title="Export analytics data"
          >
            <FontAwesomeIcon icon={faDownload} className="mr-1" />
            Export
          </button>
          <button
            onClick={handleClearAnalytics}
            className="px-3 py-1 bg-red-900/20 text-red-400 text-xs rounded hover:bg-red-900/30 transition-colors"
            title="Clear all analytics data"
          >
            <FontAwesomeIcon icon={faTrash} className="mr-1" />
            Clear
          </button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="p-4 bg-gray-700/50 rounded-lg">
          <div className="text-xs text-gray-400 uppercase tracking-wide">Total Events</div>
          <div className="text-2xl font-bold text-white">{analytics.totalEvents}</div>
        </div>
        <div className="p-4 bg-gray-700/50 rounded-lg">
          <div className="text-xs text-gray-400 uppercase tracking-wide">Session ID</div>
          <div className="text-sm font-mono text-white truncate">{analytics.sessionId}</div>
        </div>
        <div className="p-4 bg-gray-700/50 rounded-lg">
          <div className="text-xs text-gray-400 uppercase tracking-wide">User Level</div>
          <div className="text-lg font-semibold text-primary capitalize">
            {userEngagement.featureAdoptionProgression.replace('_', ' ')}
          </div>
        </div>
        <div className="p-4 bg-gray-700/50 rounded-lg">
          <div className="text-xs text-gray-400 uppercase tracking-wide">Last Updated</div>
          <div className="text-sm text-white">
            {new Date(analytics.lastUpdated).toLocaleString()}
          </div>
        </div>
      </div>

      {/* User Engagement Metrics */}
      <div className="p-6 bg-gray-700/50 rounded-lg">
        <div className="flex items-center gap-2 mb-4">
          <FontAwesomeIcon icon={faUsers} className="text-primary" />
          <h4 className="text-md font-semibold text-white">User Engagement</h4>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <div className="text-xs text-gray-400 mb-1">Message Pin Rate</div>
            <div className="text-lg font-bold text-white">
              {(userEngagement.messagePinRate * 100).toFixed(1)}%
            </div>
          </div>
          <div>
            <div className="text-xs text-gray-400 mb-1">Suggestion Acceptance</div>
            <div className="text-lg font-bold text-white">
              {(userEngagement.vaultSuggestionAcceptanceRate * 100).toFixed(1)}%
            </div>
          </div>
          <div>
            <div className="text-xs text-gray-400 mb-1">Context Selections</div>
            <div className="text-lg font-bold text-white">
              {userEngagement.contextVaultSelectionFrequency}
            </div>
          </div>
          <div>
            <div className="text-xs text-gray-400 mb-1">New Vault Rate</div>
            <div className="text-lg font-bold text-white">
              {(userEngagement.userCreatedVaultRate * 100).toFixed(1)}%
            </div>
          </div>
        </div>
      </div>

      {/* Technical Performance */}
      <div className="p-6 bg-gray-700/50 rounded-lg">
        <div className="flex items-center gap-2 mb-4">
          <FontAwesomeIcon icon={faCog} className="text-secondary" />
          <h4 className="text-md font-semibold text-white">Technical Performance</h4>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <div className="text-xs text-gray-400 mb-2">Processing Latency (avg)</div>
            <div className="space-y-1">
              <div className="flex justify-between text-sm">
                <span className="text-gray-300">Immediate:</span>
                <span className="text-white">
                  {technicalPerformance.processingLatency.immediate.length > 0
                    ? `${(technicalPerformance.processingLatency.immediate.reduce((a: number, b: number) => a + b, 0) / technicalPerformance.processingLatency.immediate.length).toFixed(1)}ms`
                    : 'N/A'
                  }
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-300">Quick:</span>
                <span className="text-white">
                  {technicalPerformance.processingLatency.quick.length > 0
                    ? `${(technicalPerformance.processingLatency.quick.reduce((a: number, b: number) => a + b, 0) / technicalPerformance.processingLatency.quick.length).toFixed(1)}ms`
                    : 'N/A'
                  }
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-300">Batch:</span>
                <span className="text-white">
                  {technicalPerformance.processingLatency.batch.length > 0
                    ? `${(technicalPerformance.processingLatency.batch.reduce((a: number, b: number) => a + b, 0) / technicalPerformance.processingLatency.batch.length).toFixed(1)}ms`
                    : 'N/A'
                  }
                </span>
              </div>
            </div>
          </div>
          
          <div>
            <div className="text-xs text-gray-400 mb-2">Cache Hit Rates</div>
            <div className="space-y-1">
              <div className="flex justify-between text-sm">
                <span className="text-gray-300">Entity:</span>
                <span className="text-white">{(technicalPerformance.cacheHitRates.entityCache * 100).toFixed(1)}%</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-300">Topic:</span>
                <span className="text-white">{(technicalPerformance.cacheHitRates.topicCache * 100).toFixed(1)}%</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-300">Suggestion:</span>
                <span className="text-white">{(technicalPerformance.cacheHitRates.vaultSuggestionCache * 100).toFixed(1)}%</span>
              </div>
            </div>
          </div>
          
          <div>
            <div className="text-xs text-gray-400 mb-2">System Health</div>
            <div className="space-y-1">
              <div className="flex justify-between text-sm">
                <span className="text-gray-300">Operations:</span>
                <span className="text-white">
                  {technicalPerformance.processingLatency.immediate.length + 
                   technicalPerformance.processingLatency.quick.length + 
                   technicalPerformance.processingLatency.batch.length}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-300">Status:</span>
                <span className="text-green-400">Healthy</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Intelligence Quality */}
      <div className="p-6 bg-gray-700/50 rounded-lg">
        <div className="flex items-center gap-2 mb-4">
          <FontAwesomeIcon icon={faLightbulb} className="text-yellow-400" />
          <h4 className="text-md font-semibold text-white">Intelligence Quality</h4>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <div className="text-xs text-gray-400 mb-1">Extraction Accuracy</div>
            <div className="text-lg font-bold text-white">
              {(intelligenceQuality.entityExtractionAccuracy * 100).toFixed(1)}%
            </div>
          </div>
          <div>
            <div className="text-xs text-gray-400 mb-1">Suggestion Relevance</div>
            <div className="text-lg font-bold text-white">
              {(intelligenceQuality.vaultSuggestionRelevance * 100).toFixed(1)}%
            </div>
          </div>
          <div>
            <div className="text-xs text-gray-400 mb-1">User Overrides</div>
            <div className="text-sm text-white">
              <div>Accepted: {intelligenceQuality.userOverridePatterns.acceptedSuggestions}</div>
              <div>Modified: {intelligenceQuality.userOverridePatterns.modifiedSuggestions}</div>
              <div>Rejected: {intelligenceQuality.userOverridePatterns.rejectedSuggestions}</div>
            </div>
          </div>
          <div>
            <div className="text-xs text-gray-400 mb-1">Master.md Quality</div>
            <div className="text-lg font-bold text-white">
              {(intelligenceQuality.masterMdUpdateQuality * 100).toFixed(1)}%
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
