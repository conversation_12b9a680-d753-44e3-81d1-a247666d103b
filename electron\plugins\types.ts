/**
 * Core Plugin Framework
 * Universal plugin system for all ChatLo features
 */

export interface BasePlugin {
  // Plugin identification
  id: string
  name: string
  version: string
  description?: string
  author?: string
  
  // Plugin lifecycle
  initialize(): Promise<void>
  cleanup?(): Promise<void>
  
  // Plugin capabilities
  getCapabilities(): PluginCapability[]
  
  // Plugin configuration
  getDefaultConfig(): Record<string, any>
  validateConfig?(config: Record<string, any>): boolean
}

export interface PluginManifest {
  id: string
  name: string
  version: string
  description?: string
  author?: string
  main: string
  dependencies?: string[]
  optionalDependencies?: string[]
  capabilities: PluginCapability[]
  apiVersion: string
  entryPoints?: Record<string, string>
}

export enum PluginCapability {
  FILE_PROCESSING = 'file_processing',
  CHAT_ENHANCEMENT = 'chat_enhancement',
  MODEL_INTEGRATION = 'model_integration',
  UI_EXTENSION = 'ui_extension',
  API_EXTENSION = 'api_extension',
  INTELLIGENCE_PROCESSING = 'intelligence_processing'
}

export interface PluginRegistry {
  plugins: Map<string, BasePlugin>
  capabilityMap: Map<PluginCapability, BasePlugin[]>
  configs: Map<string, Record<string, any>>
  states: Map<string, PluginState>
}

export enum PluginState {
  UNLOADED = 'unloaded',
  LOADING = 'loading',
  LOADED = 'loaded',
  ACTIVE = 'active',
  ERROR = 'error',
  DISABLED = 'disabled'
}