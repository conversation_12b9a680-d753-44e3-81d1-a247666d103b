/**
 * Advanced Image Processing Plugin
 * Optional plugin for advanced image processing using Sharp
 */

import * as fs from 'fs'
import * as path from 'path'
import * as mime from 'mime-types'
import { FileProcessorPlugin, ProcessedFileContent } from '../types'

export default class ImagePlugin implements FileProcessorPlugin {
  name = 'ImagePlugin'
  version = '1.0.0'
  description = 'Advanced image processing plugin using Sharp'
  author = 'ChatLo Team'
  dependencies = ['sharp']
  optional = true

  supportedTypes = ['image']
  supportedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.svg']

  private sharp: any = null

  async initialize(): Promise<void> {
    try {
      this.sharp = require('sharp')
      console.log('ImagePlugin (Sharp) initialized successfully')
    } catch (error) {
      console.warn('sharp not available, ImagePlugin will not be functional')
      throw error
    }
  }

  canProcess(filePath: string, fileType: string): boolean {
    if (!this.sharp) return false
    
    const extension = path.extname(filePath).toLowerCase()
    return fileType === 'image' || this.supportedExtensions.includes(extension)
  }

  async process(filePath: string): Promise<ProcessedFileContent> {
    if (!this.sharp) {
      return {
        error: 'Advanced image processing not available - sharp module not loaded'
      }
    }

    try {
      const stats = await fs.promises.stat(filePath)
      const extension = path.extname(filePath).toLowerCase()
      const mimeType = mime.lookup(filePath) || 'unknown'
      const filename = path.basename(filePath)

      // Check file size (limit to 100MB for images)
      const maxSize = 100 * 1024 * 1024 // 100MB
      if (stats.size > maxSize) {
        return {
          error: `Image file too large: ${Math.round(stats.size / 1024 / 1024)}MB (max: 100MB)`
        }
      }

      // SVG files need special handling
      if (extension === '.svg') {
        return this.processSVG(filePath, stats, filename, mimeType)
      }

      // Use Sharp for other image formats
      const image = this.sharp(filePath)
      const metadata = await image.metadata()

      // Generate image description
      const description = this.generateAdvancedImageDescription(
        filename, 
        extension, 
        stats.size, 
        mimeType, 
        metadata
      )

      const processedMetadata = {
        filename,
        extension,
        mimeType,
        fileSize: stats.size,
        lastModified: stats.mtime,
        processor: this.name,
        processingLevel: 'advanced',
        
        // Image dimensions and properties
        width: metadata.width,
        height: metadata.height,
        channels: metadata.channels,
        depth: metadata.depth,
        density: metadata.density,
        hasAlpha: metadata.hasAlpha,
        hasProfile: metadata.hasProfile,
        isProgressive: metadata.isProgressive,
        
        // Color space and format info
        space: metadata.space,
        chromaSubsampling: metadata.chromaSubsampling,
        format: metadata.format,
        
        // Calculated properties
        aspectRatio: metadata.width && metadata.height ? 
          Math.round((metadata.width / metadata.height) * 100) / 100 : null,
        megapixels: metadata.width && metadata.height ? 
          Math.round((metadata.width * metadata.height) / 1000000 * 100) / 100 : null,
        
        // Quality assessment
        qualityAssessment: this.assessImageQuality(metadata, stats.size)
      }

      return {
        text: description,
        metadata: processedMetadata
      }

    } catch (error: any) {
      console.error('Error processing image with Sharp:', error)
      
      // Provide helpful error messages
      if (error.message?.includes('Input file is missing')) {
        return {
          error: 'Image file not found or inaccessible'
        }
      }
      
      if (error.message?.includes('Input file contains unsupported image format')) {
        return {
          error: 'Unsupported or corrupted image format'
        }
      }

      return {
        error: `Failed to process image: ${error?.message || 'Unknown error'}`
      }
    }
  }

  // Process SVG files (Sharp doesn't handle SVG metadata well)
  private async processSVG(filePath: string, stats: fs.Stats, filename: string, mimeType: string): Promise<ProcessedFileContent> {
    try {
      const svgContent = await fs.promises.readFile(filePath, 'utf8')
      const textContent = this.extractSVGText(svgContent)
      const svgInfo = this.analyzeSVG(svgContent)
      
      const description = `SVG Vector Image: ${filename}
Dimensions: ${svgInfo.width || 'unknown'} × ${svgInfo.height || 'unknown'}
File size: ${Math.round(stats.size / 1024)} KB
MIME type: ${mimeType}
Elements: ${Object.values(svgInfo.elements).reduce((a, b) => a + b, 0)}
${textContent ? `Text content: ${textContent}` : ''}`

      return {
        text: description,
        metadata: {
          filename,
          extension: '.svg',
          mimeType,
          fileSize: stats.size,
          lastModified: stats.mtime,
          processor: this.name,
          processingLevel: 'advanced',
          
          // SVG-specific metadata
          width: svgInfo.width,
          height: svgInfo.height,
          viewBox: svgInfo.viewBox,
          elements: svgInfo.elements,
          hasText: textContent.length > 0,
          textContent: textContent.substring(0, 500) // Limit text content
        }
      }
    } catch (error) {
      console.warn('Failed to process SVG with advanced features:', error)
      return {
        error: `Failed to process SVG: ${(error as Error)?.message || 'Unknown error'}`
      }
    }
  }

  // Extract text from SVG
  private extractSVGText(svgContent: string): string {
    const textElements = svgContent.match(/<text[^>]*>(.*?)<\/text>/gi) || []
    const titleElements = svgContent.match(/<title[^>]*>(.*?)<\/title>/gi) || []
    const descElements = svgContent.match(/<desc[^>]*>(.*?)<\/desc>/gi) || []

    return [...textElements, ...titleElements, ...descElements]
      .map(element => element.replace(/<[^>]*>/g, '').trim())
      .filter(text => text.length > 0)
      .join(' ')
  }

  // Analyze SVG structure
  private analyzeSVG(svgContent: string) {
    // Extract dimensions
    const widthMatch = svgContent.match(/width=["']([^"']+)["']/i)
    const heightMatch = svgContent.match(/height=["']([^"']+)["']/i)
    const viewBoxMatch = svgContent.match(/viewBox=["']([^"']+)["']/i)

    // Count elements
    const elements = ['rect', 'circle', 'ellipse', 'line', 'polyline', 'polygon', 'path', 'text', 'image', 'g']
    const elementCounts: Record<string, number> = {}

    for (const element of elements) {
      const regex = new RegExp(`<${element}[^>]*>`, 'gi')
      const matches = svgContent.match(regex) || []
      elementCounts[element] = matches.length
    }

    return {
      width: widthMatch ? widthMatch[1] : null,
      height: heightMatch ? heightMatch[1] : null,
      viewBox: viewBoxMatch ? viewBoxMatch[1] : null,
      elements: elementCounts
    }
  }

  // Generate advanced image description
  private generateAdvancedImageDescription(
    filename: string, 
    extension: string, 
    fileSize: number, 
    mimeType: string, 
    metadata: any
  ): string {
    const sizeKB = Math.round(fileSize / 1024)
    const sizeMB = Math.round(fileSize / 1024 / 1024 * 100) / 100
    const sizeText = sizeKB < 1024 ? `${sizeKB} KB` : `${sizeMB} MB`

    const dimensions = metadata.width && metadata.height ? 
      `${metadata.width} × ${metadata.height}` : 'unknown'
    
    const megapixels = metadata.width && metadata.height ? 
      Math.round((metadata.width * metadata.height) / 1000000 * 100) / 100 : null

    return `Image: ${filename}
Dimensions: ${dimensions}${megapixels ? ` (${megapixels}MP)` : ''}
Format: ${metadata.format?.toUpperCase() || extension.substring(1).toUpperCase()}
Color space: ${metadata.space || 'unknown'}
Channels: ${metadata.channels || 'unknown'}
Bit depth: ${metadata.depth || 'unknown'}
File size: ${sizeText}
MIME type: ${mimeType}
${metadata.hasAlpha ? 'Has transparency' : 'No transparency'}
${metadata.isProgressive ? 'Progressive encoding' : 'Standard encoding'}`
  }

  // Assess image quality
  private assessImageQuality(metadata: any, fileSize: number) {
    const { width, height, format } = metadata
    if (!width || !height) return 'unknown'

    const pixels = width * height
    const bytesPerPixel = fileSize / pixels

    let quality = 'unknown'
    
    if (format === 'jpeg') {
      if (bytesPerPixel > 2) quality = 'high'
      else if (bytesPerPixel > 1) quality = 'medium'
      else quality = 'low'
    } else if (format === 'png') {
      if (bytesPerPixel > 4) quality = 'high'
      else if (bytesPerPixel > 2) quality = 'medium'
      else quality = 'low'
    }

    return {
      quality,
      bytesPerPixel: Math.round(bytesPerPixel * 100) / 100,
      compressionRatio: format === 'jpeg' ? Math.round((pixels * 3) / fileSize * 100) / 100 : null
    }
  }

  async cleanup(): Promise<void> {
    this.sharp = null
    console.log('ImagePlugin cleaned up')
  }
}
