import { useCallback } from 'react'
import { useAppStore } from '../store'
import { detectArtifacts, Artifact } from '../types'

export function useArtifactDetection() {
  const { addArtifact, clearArtifacts } = useAppStore()

  const processMessageForArtifacts = useCallback((
    messageContent: string,
    messageId: string,
    clearExisting = false
  ) => {
    // Clear existing artifacts if requested (e.g., for new conversation)
    if (clearExisting) {
      clearArtifacts()
    }

    // Detect artifacts in the message content
    const { artifacts, processedContent } = detectArtifacts(messageContent, messageId)

    // Check for existing artifacts to prevent duplicates
    const existingArtifacts = useAppStore.getState().artifacts.artifacts
    const newArtifacts = artifacts.filter(artifact =>
      !existingArtifacts.some(existing => existing.id === artifact.id)
    )

    // Save new artifacts to database and add to store
    newArtifacts.forEach(artifact => {
      // Add to store immediately
      addArtifact(artifact)

      // Save to database asynchronously if available
      if (window.electronAPI?.db?.addArtifact) {
        window.electronAPI.db.addArtifact(messageId, {
          type: artifact.type,
          title: artifact.title,
          content: artifact.content,
          metadata: JSON.stringify(artifact.metadata),
          original_index: artifact.metadata.originalIndex || 0
        }).catch(error => {
          console.error('Failed to save artifact to database:', error)
        })
      }
    })

    return {
      artifacts,
      processedContent,
      hasArtifacts: artifacts.length > 0
    }
  }, [addArtifact, clearArtifacts])

  const detectImageArtifacts = useCallback((
    messageId: string,
    imageUrls: string[]
  ) => {
    const artifacts: Artifact[] = imageUrls.map((url, index) => ({
      id: `artifact-${messageId}-image-${index}`,
      type: 'image' as const,
      title: `Generated Image ${index + 1}`,
      content: url,
      metadata: {
        mimeType: 'image/png', // Default, could be detected
        createdAt: new Date().toISOString(),
        messageId,
        originalIndex: index
      },
      isActive: false
    }))

    // Add to store
    artifacts.forEach(artifact => {
      addArtifact(artifact)
    })

    return artifacts
  }, [addArtifact])

  const detectCodeArtifacts = useCallback((
    messageContent: string,
    messageId: string
  ) => {
    const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g
    const artifacts: Artifact[] = []
    let match
    let index = 0

    while ((match = codeBlockRegex.exec(messageContent)) !== null) {
      const language = match[1] || 'text'
      const code = match[2]
      
      // Only create artifacts for substantial code blocks
      if (code.trim().length > 50) {
        const artifact: Artifact = {
          id: `artifact-${messageId}-code-${index}`,
          type: 'code',
          title: `Code (${language})`,
          content: code,
          metadata: {
            language,
            createdAt: new Date().toISOString(),
            messageId,
            originalIndex: index
          },
          isActive: false
        }
        
        artifacts.push(artifact)
        addArtifact(artifact)
        index++
      }
    }

    return artifacts
  }, [addArtifact])

  const detectMermaidArtifacts = useCallback((
    messageContent: string,
    messageId: string
  ) => {
    const mermaidRegex = /```mermaid\n([\s\S]*?)```/g
    const artifacts: Artifact[] = []
    let match
    let index = 0

    while ((match = mermaidRegex.exec(messageContent)) !== null) {
      const diagram = match[1]
      
      if (diagram.trim().length > 10) {
        const artifact: Artifact = {
          id: `artifact-${messageId}-mermaid-${index}`,
          type: 'mermaid',
          title: 'Mermaid Diagram',
          content: diagram,
          metadata: {
            createdAt: new Date().toISOString(),
            messageId,
            originalIndex: index
          },
          isActive: false
        }
        
        artifacts.push(artifact)
        addArtifact(artifact)
        index++
      }
    }

    return artifacts
  }, [addArtifact])

  const detectMarkdownArtifacts = useCallback((
    messageContent: string,
    messageId: string
  ) => {
    // Detect substantial markdown content (multiple sections with headers)
    const sections = messageContent.split(/^#{1,6}\s+/gm).filter(section => 
      section.trim().length > 200 // Only substantial content
    )

    if (sections.length >= 3) { // Multiple sections indicate structured document
      const artifact: Artifact = {
        id: `artifact-${messageId}-markdown`,
        type: 'markdown',
        title: 'Structured Document',
        content: messageContent,
        metadata: {
          createdAt: new Date().toISOString(),
          messageId,
          originalIndex: 0
        },
        isActive: false
      }
      
      addArtifact(artifact)
      return [artifact]
    }

    return []
  }, [addArtifact])

  const detectHtmlArtifacts = useCallback((
    messageContent: string,
    messageId: string
  ) => {
    const htmlRegex = /<html[\s\S]*?<\/html>|<!DOCTYPE html[\s\S]*?<\/html>/gi
    const artifacts: Artifact[] = []
    let match
    let index = 0

    while ((match = htmlRegex.exec(messageContent)) !== null) {
      const html = match[0]
      
      if (html.length > 100) {
        const artifact: Artifact = {
          id: `artifact-${messageId}-html-${index}`,
          type: 'html',
          title: 'HTML Document',
          content: html,
          metadata: {
            createdAt: new Date().toISOString(),
            messageId,
            originalIndex: index
          },
          isActive: false
        }
        
        artifacts.push(artifact)
        addArtifact(artifact)
        index++
      }
    }

    return artifacts
  }, [addArtifact])

  const processStreamingMessage = useCallback((
    partialContent: string,
    messageId: string
  ) => {
    // For streaming, we can detect artifacts as they come in
    // This is useful for real-time artifact detection
    const { artifacts } = detectArtifacts(partialContent, messageId)
    
    // Only add new artifacts that haven't been detected yet
    const existingArtifacts = useAppStore.getState().artifacts.artifacts
    const newArtifacts = artifacts.filter(artifact => 
      !existingArtifacts.some(existing => existing.id === artifact.id)
    )

    newArtifacts.forEach(artifact => {
      addArtifact(artifact)
    })

    return {
      newArtifacts,
      totalArtifacts: artifacts.length
    }
  }, [addArtifact])

  const loadArtifactsFromDatabase = useCallback(async (messageId: string) => {
    try {
      if (window.electronAPI?.db) {
        const dbArtifacts = await window.electronAPI.db.getArtifacts(messageId)

        // Convert database artifacts to frontend format
        const artifacts = dbArtifacts.map((dbArtifact: any) => ({
          id: dbArtifact.id,
          type: dbArtifact.type,
          title: dbArtifact.title,
          content: dbArtifact.content,
          metadata: JSON.parse(dbArtifact.metadata || '{}'),
          isActive: false
        }))

        // Add to store
        artifacts.forEach(artifact => {
          addArtifact(artifact)
        })

        return artifacts
      }
    } catch (error) {
      console.error('Failed to load artifacts from database:', error)
    }
    return []
  }, [addArtifact])

  const loadConversationArtifacts = useCallback(async (conversationId: string) => {
    try {
      if (window.electronAPI?.db) {
        const dbArtifacts = await window.electronAPI.db.getConversationArtifacts(conversationId)

        // Convert and group by message
        const artifactsByMessage = new Map()

        dbArtifacts.forEach((dbArtifact: any) => {
          const artifact = {
            id: dbArtifact.id,
            type: dbArtifact.type,
            title: dbArtifact.title,
            content: dbArtifact.content,
            metadata: JSON.parse(dbArtifact.metadata || '{}'),
            isActive: false
          }

          if (!artifactsByMessage.has(dbArtifact.message_id)) {
            artifactsByMessage.set(dbArtifact.message_id, [])
          }
          artifactsByMessage.get(dbArtifact.message_id).push(artifact)
        })

        // Add all artifacts to store
        dbArtifacts.forEach((dbArtifact: any) => {
          const artifact = {
            id: dbArtifact.id,
            type: dbArtifact.type,
            title: dbArtifact.title,
            content: dbArtifact.content,
            metadata: JSON.parse(dbArtifact.metadata || '{}'),
            isActive: false
          }
          addArtifact(artifact)
        })

        return artifactsByMessage
      }
    } catch (error) {
      console.error('Failed to load conversation artifacts:', error)
    }
    return new Map()
  }, [addArtifact])

  return {
    processMessageForArtifacts,
    detectImageArtifacts,
    detectCodeArtifacts,
    detectMermaidArtifacts,
    detectMarkdownArtifacts,
    detectHtmlArtifacts,
    processStreamingMessage,
    loadArtifactsFromDatabase,
    loadConversationArtifacts
  }
}
