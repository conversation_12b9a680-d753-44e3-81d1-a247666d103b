# Master.md Intelligence Architecture

## 1. Temporal Context Timeline
- **Micro-events**: Every file access, edit, chat interaction
- **Context snapshots**: Project states at key moments
- **Cross-reference links**: Implicit connections between artifacts
- **Usage patterns**: Frequency and timing of context access

> <PERSON><PERSON>'s comment: The Temporal Context Timeline section outlines a comprehensive system for tracking micro-events, context snapshots, cross-reference links, and usage patterns. However, the current codebase only implements basic timestamp tracking for conversations and messages. There is no mechanism for capturing micro-events like file access or edits, creating context snapshots, establishing cross-reference links between artifacts, or analyzing usage patterns. This represents a significant gap between the architecture vision and current implementation.

## 2. Hierarchical Storage Strategy
```
.context/
├── master.md                 # Living index + AI-readable summary
├── timeline/
│   ├── 2024-01-15/
│   │   ├── events.jsonl      # Micro-events (lightweight)
│   │   ├── snapshots/        # Full context states (selective)
│   │   └── connections.md    # Cross-reference notes
│   └── daily-index.md        # Daily digest for quick scanning
├── vaults/
│   ├── personal/
│   └── work/
└── intelligence/
    ├── semantic-map.json     # Topic clusters and relationships
    ├── usage-patterns.json   # Behavioral insights
    └── query-index.json      # Pre-computed common queries
```

> <PERSON><PERSON>'s comment: The Hierarchical Storage Strategy specifies a detailed directory structure with timeline, vaults, and intelligence directories. While the codebase creates context directories with documents, images, artifacts, and .context subdirectories through the contextVaultService, it does not implement the timeline or intelligence directories. The specified storage hierarchy with events.jsonl, snapshots, connections.md, and intelligence files (semantic-map.json, usage-patterns.json, query-index.json) is not present in the current implementation.

## 3. Resource-Conscious Collection
- **Differential storage**: Only store changes, not full copies
- **Smart compression**: Text diffs, image thumbnails, file hashes
- **Tiered retention**:
  - Hot (7 days): Full context
  - Warm (30 days): Summaries + links
  - Cold (1 year): Metadata only
- **Local model assistance**: Use 7B models for summarization, not storage

> Qwen's comment: The Resource-Conscious Collection section proposes differential storage, smart compression, tiered retention, and local model assistance. The current codebase does not implement any of these features. There is no differential storage mechanism, no smart compression of text diffs or image thumbnails, no tiered retention policy (hot/warm/cold), and no use of local 7B models for summarization. The model update service does cache models, but this is unrelated to the resource-conscious collection strategy for user context.

## 4. Query Interface Design
```markdown
## Master.md Query Patterns

### Temporal Queries
- "What was I working on last Tuesday?"
- "Show me all changes to Project X this week"

### Semantic Queries
- "Find all documents mentioning 'API design'"
- "Connect this email thread to related code files"

### Pattern Queries
- "I always check email after editing this file"
- "Suggest context for my current task"
```

> Qwen's comment: The Query Interface Design specifies temporal, semantic, and pattern queries. The current codebase only provides basic CRUD operations through the database interface and lacks any sophisticated query capabilities. There is no implementation of temporal queries ("What was I working on last Tuesday?"), semantic queries ("Find all documents mentioning 'API design'"), or pattern queries ("I always check email after editing this file"). The database schema and service layers would need significant enhancement to support these query types.

## 5. Innovative Features Beyond Timeline

### Context DNA
Each artifact gets a "DNA string" - compressed semantic signature that enables:
- **Similarity matching** without full content analysis
- **Relationship mapping** across vaults
- **Anomaly detection** (unusual access patterns)

> Qwen's comment: The Innovative Features section proposes Context DNA, predictive context loading, micro-summarization network, and privacy-aware indexing. None of these features are implemented in the current codebase. There is no mechanism for creating compressed semantic signatures (DNA strings) for similarity matching, no predictive loading based on time-of-day patterns or external triggers, no network of one-sentence insights and three-word tags, and no privacy-aware indexing with selective visibility or encrypted connections.

### Predictive Context Loading
Based on:
- Time-of-day patterns
- File type correlations
- Project phase indicators
- External triggers (calendar, email)

### Micro-Summarization Network
Instead of storing full summaries, create a network of:
- **One-sentence insights** per artifact
- **Three-word tags** for quick scanning
- **Connection strength scores** between items

### Privacy-Aware Indexing
- **Selective visibility**: User controls what gets indexed
- **Encrypted connections**: Relationships stored without exposing content
- **Local-only processing**: No cloud dependencies

## 6. Master.md Format Specification

```markdown
# Master Context Index
Generated: 2024-01-15 09:30:00
Model: local-7b-v2

## Today's Context Pulse
- **Active Projects**: 3 (Project-Alpha, Client-Work, Research)
- **Key Changes**: 12 files modified, 2 new artifacts
- **Connections Found**: Email thread → Design doc → Code implementation

## Semantic Clusters
### API Design (confidence: 0.87)
- Files: 5 documents, 3 code files
- Last activity: 2 hours ago
- Related emails: Q4 planning thread

### Performance Optimization (confidence: 0.92)
- Recent focus shift detected
- Connected artifacts: profiling data, optimization notes

## Temporal Insights
### Morning Pattern (08:00-10:00)
- Usually review emails → check project docs → code
- Suggestion: Pre-load Project-Alpha context

### Afternoon Deep Work (14:00-16:00)
- Minimal context switching
- Recommend: Enable focus mode

## Quick Actions
- [ ] Review yesterday's snapshots
- [ ] Connect new email attachments to Project-Alpha
- [ ] Archive completed tasks
```

> Qwen's comment: The Master.md Format Specification details a rich structure with context pulse, semantic clusters, temporal insights, and quick actions. While the contextVaultService creates a master.md file, it only includes basic information like name and objective, not the comprehensive structure specified. The current implementation lacks the "Today's Context Pulse" with active projects and key changes, "Semantic Clusters" with confidence scores, "Temporal Insights" with usage patterns, and "Quick Actions" with actionable items.

## 7. Implementation Phases

### Phase 1: Foundation (Week 1-2)
- Basic timeline events
- Simple master.md generation
- File change tracking

> Qwen's comment: The Implementation Phases outline a clear progression from foundation to predictive features. The current codebase appears to be at the foundation phase (basic timeline events, simple master.md generation, file change tracking) but has not progressed to the intelligence phase (semantic clustering, pattern recognition, query interface), optimization phase (compression algorithms, retention policies), or predictive phase (context prediction, proactive loading). The implementation is aligned with Phase 1 but lags behind on subsequent phases.

### Phase 2: Intelligence (Week 3-4)
- Semantic clustering
- Pattern recognition
- Query interface

### Phase 3: Optimization (Week 5-6)
- Compression algorithms
- Retention policies
- Performance tuning

### Phase 4: Predictive (Week 7-8)
- Context prediction
- Proactive loading
- Smart suggestions

## 8. Resource Usage Targets
- **Storage**: <100MB for 1 year of context
- **Memory**: <50MB resident for background processing
- **CPU**: <5% during idle, spikes during indexing
- **Battery**: Optimized for laptop use (defer heavy processing)

> Qwen's comment: The Resource Usage Targets specify strict limits for storage (<100MB for 1 year), memory (<50MB), CPU (<5% during idle), and battery optimization. The current codebase does not appear to have any explicit mechanisms for monitoring or optimizing these resource usage metrics. There are no evident constraints or optimizations in place to meet these targets, suggesting that resource efficiency is not currently a focus of the implementation.
```
