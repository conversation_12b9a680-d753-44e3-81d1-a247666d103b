/**
 * Universal Plugin Manager
 * Handles all plugin types and capabilities
 */

import * as fs from 'fs'
import * as path from 'path'
import { BasePlugin, PluginManifest, PluginRegistry, PluginState, PluginCapability } from './types'
import { APIRegistry } from '../api/APIRegistry'
import { APIExtension, PluginAPINamespace, PluginEndpoint, Middleware } from './extensionPoints'

export class UniversalPluginManager {
  private registry: PluginRegistry = {
    plugins: new Map(),
    capabilityMap: new Map(),
    configs: new Map(),
    states: new Map()
  }

  private pluginDirectories: string[] = []
  private apiRegistry: APIRegistry
  private pluginNamespaces: Map<string, PluginAPINamespace> = new Map()
  
  constructor(apiRegistry: APIRegistry) {
    this.apiRegistry = apiRegistry
    this.initializeCapabilityMap()
  }
  
  // Initialize capability map
  private initializeCapabilityMap(): void {
    Object.values(PluginCapability).forEach(capability => {
      this.registry.capabilityMap.set(capability, [])
    })
  }
  
  // Add plugin directory for discovery
  addPluginDirectory(directory: string): void {
    if (!this.pluginDirectories.includes(directory)) {
      this.pluginDirectories.push(directory)
    }
  }
  
  // Discover plugins in all registered directories
  async discoverPlugins(): Promise<PluginManifest[]> {
    const manifests: PluginManifest[] = []
    console.log(`[PLUGIN] Discovering plugins in directories:`, this.pluginDirectories)

    for (const directory of this.pluginDirectories) {
      try {
        console.log(`[PLUGIN] Scanning directory: ${directory}`)
        const dirManifests = await this.discoverPluginsInDirectory(directory)
        console.log(`[PLUGIN] Found ${dirManifests.length} plugins in ${directory}`)
        manifests.push(...dirManifests)
      } catch (error) {
        console.error(`Error discovering plugins in ${directory}:`, error)
      }
    }

    console.log(`[PLUGIN] Total plugins discovered: ${manifests.length}`)
    return manifests
  }
  
  // Discover plugins in a specific directory
  private async discoverPluginsInDirectory(directory: string): Promise<PluginManifest[]> {
    const manifests: PluginManifest[] = []

    if (!fs.existsSync(directory)) {
      console.log(`[PLUGIN] Directory does not exist: ${directory}`)
      return manifests
    }

    const entries = await fs.promises.readdir(directory, { withFileTypes: true })
    console.log(`[PLUGIN] Found ${entries.length} entries in ${directory}`)

    for (const entry of entries) {
      if (entry.isDirectory()) {
        const manifestPath = path.join(directory, entry.name, 'manifest.json')
        console.log(`[PLUGIN] Checking for manifest: ${manifestPath}`)

        if (fs.existsSync(manifestPath)) {
          try {
            console.log(`[PLUGIN] Loading manifest for ${entry.name}`)
            const manifestContent = await fs.promises.readFile(manifestPath, 'utf8')
            const manifest = JSON.parse(manifestContent) as PluginManifest

            if (this.validateManifest(manifest)) {
              console.log(`[PLUGIN] Valid manifest found for ${entry.name}:`, manifest.name)
              manifests.push(manifest)
            } else {
              console.warn(`[PLUGIN] Invalid manifest for ${entry.name}`)
            }
          } catch (error) {
            console.error(`Error loading manifest for ${entry.name}:`, error)
          }
        } else {
          console.log(`[PLUGIN] No manifest found for ${entry.name}`)
        }
      }
    }

    return manifests
  }
  
  // Load plugin from manifest
  async loadPlugin(manifest: PluginManifest): Promise<void> {
    try {
      this.registry.states.set(manifest.id, PluginState.LOADING)
      
      // Resolve plugin path
      const pluginPath = this.resolvePluginPath(manifest)
      
      // Load plugin module
      const pluginModule = require(pluginPath)
      const plugin = new pluginModule.default() as BasePlugin
      
      // Validate plugin
      if (!this.validatePlugin(plugin, manifest)) {
        throw new Error(`Plugin validation failed for ${manifest.id}`)
      }
      
      // Initialize plugin
      await plugin.initialize()
      
      // Register plugin
      await this.registerPlugin(plugin)
      
      this.registry.states.set(manifest.id, PluginState.ACTIVE)
      console.log(`Plugin loaded successfully: ${plugin.name} (${plugin.id}) v${plugin.version}`)
      
    } catch (error) {
      this.registry.states.set(manifest.id, PluginState.ERROR)
      console.error(`Error loading plugin ${manifest.id}:`, error)
      throw error
    }
  }
  
  // Register a plugin
  async registerPlugin(plugin: BasePlugin): Promise<void> {
    // Store plugin
    this.registry.plugins.set(plugin.id, plugin)
    
    // Register by capabilities
    const capabilities = plugin.getCapabilities()
    for (const capability of capabilities) {
      const plugins = this.registry.capabilityMap.get(capability) || []
      plugins.push(plugin)
      this.registry.capabilityMap.set(capability, plugins)
    }
    
    // Initialize plugin config
    this.registry.configs.set(plugin.id, plugin.getDefaultConfig())
    
    // Register API endpoints if plugin supports it
    if (capabilities.includes(PluginCapability.API_EXTENSION)) {
      await this.registerPluginEndpoints(plugin)
    }
  }
  
  // Get plugins by capability
  getPluginsByCapability(capability: PluginCapability): BasePlugin[] {
    return this.registry.capabilityMap.get(capability) || []
  }
  
  // Get plugin by ID
  getPlugin(pluginId: string): BasePlugin | undefined {
    return this.registry.plugins.get(pluginId)
  }
  
  // Enable/disable plugin
  setPluginEnabled(pluginId: string, enabled: boolean): void {
    const currentState = this.registry.states.get(pluginId)
    
    if (enabled && currentState === PluginState.DISABLED) {
      this.registry.states.set(pluginId, PluginState.ACTIVE)
    } else if (!enabled && currentState === PluginState.ACTIVE) {
      this.registry.states.set(pluginId, PluginState.DISABLED)
    }
  }
  
  // Get plugin state
  getPluginState(pluginId: string): PluginState {
    return this.registry.states.get(pluginId) || PluginState.UNLOADED
  }
  
  // Get all plugins info
  getAllPluginsInfo(): Array<{id: string, name: string, version: string, state: string, capabilities: string[]}> {
    const info: Array<{id: string, name: string, version: string, state: string, capabilities: string[]}> = []
    
    for (const [id, plugin] of this.registry.plugins.entries()) {
      info.push({
        id,
        name: plugin.name,
        version: plugin.version,
        state: this.getPluginState(id), // PluginState enum values are already strings
        capabilities: plugin.getCapabilities() // PluginCapability enum values are already strings
      })
    }
    
    return info
  }

  // Unload a plugin
  async unloadPlugin(pluginId: string): Promise<void> {
    const plugin = this.registry.plugins.get(pluginId)
    if (!plugin) {
      throw new Error(`Plugin not found: ${pluginId}`)
    }

    try {
      // Set state to unloading
      this.registry.states.set(pluginId, PluginState.LOADING)

      // Call plugin cleanup if available
      if (plugin.cleanup) {
        await plugin.cleanup()
      }

      // Remove from capability map
      const capabilities = plugin.getCapabilities()
      for (const capability of capabilities) {
        const plugins = this.registry.capabilityMap.get(capability) || []
        const filteredPlugins = plugins.filter(p => p.id !== pluginId)
        this.registry.capabilityMap.set(capability, filteredPlugins)
      }

      // Remove from registry
      this.registry.plugins.delete(pluginId)
      this.registry.configs.delete(pluginId)
      this.registry.states.set(pluginId, PluginState.UNLOADED)

      console.log(`Plugin unloaded successfully: ${plugin.name} (${pluginId})`)
    } catch (error) {
      this.registry.states.set(pluginId, PluginState.ERROR)
      console.error(`Error unloading plugin ${pluginId}:`, error)
      throw error
    }
  }

  // Get plugin configuration
  getPluginConfig(pluginId: string): Record<string, any> | undefined {
    return this.registry.configs.get(pluginId)
  }

  // Update plugin configuration
  updatePluginConfig(pluginId: string, config: Record<string, any>): void {
    const plugin = this.registry.plugins.get(pluginId)
    if (!plugin) {
      throw new Error(`Plugin not found: ${pluginId}`)
    }

    // Validate config if plugin has validation method
    if (plugin.validateConfig && !plugin.validateConfig(config)) {
      throw new Error(`Invalid configuration for plugin: ${pluginId}`)
    }

    // Merge with existing config
    const existingConfig = this.registry.configs.get(pluginId) || {}
    const mergedConfig = { ...existingConfig, ...config }

    // Store updated config
    this.registry.configs.set(pluginId, mergedConfig)

    console.log(`Plugin configuration updated: ${pluginId}`)
  }

  // Register plugin API endpoints with namespace support
  async registerPluginEndpoints(plugin: BasePlugin): Promise<void> {
    try {
      const apiExtension = plugin as any as APIExtension

      if (!apiExtension.registerEndpoints) {
        console.warn(`Plugin ${plugin.id} claims API_EXTENSION capability but has no registerEndpoints method`)
        return
      }

      // Generate plugin namespace
      const namespace = `plugin_${plugin.id}`

      console.log(`[PLUGIN] Registering API endpoints for plugin: ${plugin.id} with namespace: ${namespace}`)

      // Register the plugin category in APIRegistry
      this.apiRegistry.registerCategory(namespace)

      // Get plugin endpoints
      const pluginNamespace = apiExtension.registerEndpoints(this.apiRegistry)

      // Store namespace information
      const namespaceInfo: PluginAPINamespace = {
        pluginId: plugin.id,
        namespace: namespace,
        endpoints: pluginNamespace.endpoints || [],
        middleware: pluginNamespace.middleware || []
      }

      this.pluginNamespaces.set(plugin.id, namespaceInfo)

      // Register plugin-specific middleware if provided
      if (apiExtension.provideMiddleware) {
        const middleware = apiExtension.provideMiddleware()
        namespaceInfo.middleware.push(...middleware)
      }

      // Discover dynamic endpoints if supported
      if (apiExtension.discoverEndpoints) {
        const dynamicEndpoints = await apiExtension.discoverEndpoints()
        namespaceInfo.endpoints.push(...dynamicEndpoints)

        // Register dynamic endpoints
        for (const endpoint of dynamicEndpoints) {
          this.apiRegistry.registerEndpoint(
            namespace,
            endpoint.name,
            endpoint.handler,
            {
              validator: endpoint.validator,
              description: endpoint.description,
              middleware: endpoint.middleware
            }
          )
        }
      }

      console.log(`[PLUGIN] Successfully registered ${namespaceInfo.endpoints.length} endpoints for plugin: ${plugin.id}`)

    } catch (error) {
      console.error(`[PLUGIN] Error registering endpoints for plugin ${plugin.id}:`, error)
      throw error
    }
  }

  // Get plugin API namespace information
  getPluginNamespace(pluginId: string): PluginAPINamespace | undefined {
    return this.pluginNamespaces.get(pluginId)
  }

  // Get all plugin namespaces
  getAllPluginNamespaces(): PluginAPINamespace[] {
    return Array.from(this.pluginNamespaces.values())
  }

  // Cleanup all plugins
  async cleanup(): Promise<void> {
    for (const plugin of this.registry.plugins.values()) {
      try {
        if (plugin.cleanup) {
          await plugin.cleanup()
        }
      } catch (error) {
        console.error(`Error cleaning up plugin ${plugin.id}:`, error)
      }
    }
    
    this.registry.plugins.clear()
    this.registry.capabilityMap.clear()
    this.registry.configs.clear()
    this.registry.states.clear()
  }
  
  // Validate plugin manifest
  private validateManifest(manifest: any): boolean {
    const required = ['id', 'name', 'version', 'main', 'capabilities', 'apiVersion']
    return required.every(field => manifest[field] !== undefined)
  }
  
  // Validate plugin instance
  private validatePlugin(plugin: any, manifest: PluginManifest): boolean {
    return (
      plugin.id === manifest.id &&
      plugin.name === manifest.name &&
      plugin.version === manifest.version &&
      typeof plugin.initialize === 'function' &&
      typeof plugin.getCapabilities === 'function' &&
      typeof plugin.getDefaultConfig === 'function'
    )
  }
  
  // Resolve plugin path from manifest
  private resolvePluginPath(manifest: PluginManifest): string {
    // Implementation depends on your plugin directory structure
    // This is a simplified version
    for (const directory of this.pluginDirectories) {
      // Clean up the main path - remove leading ./ if present
      const cleanMain = manifest.main.startsWith('./') ? manifest.main.slice(2) : manifest.main

      // Try the exact path from manifest first
      const pluginPath = path.join(directory, manifest.id, cleanMain)
      if (fs.existsSync(pluginPath)) {
        return pluginPath
      }

      // If .js file doesn't exist, try .ts file (for development)
      if (cleanMain.endsWith('.js')) {
        const tsPath = path.join(directory, manifest.id, cleanMain.replace('.js', '.ts'))
        if (fs.existsSync(tsPath)) {
          return tsPath
        }
      }

      // Also try with .js extension if .ts was specified (for compiled plugins)
      if (cleanMain.endsWith('.ts')) {
        const jsPath = path.join(directory, manifest.id, cleanMain.replace('.ts', '.js'))
        if (fs.existsSync(jsPath)) {
          return jsPath
        }
      }
    }
    throw new Error(`Plugin main file not found: ${manifest.main}`)
  }
}