# ChatLo Unified IPC System - API Reference

## Table of Contents
1. [Core API Endpoints](#core-api-endpoints)
2. [Plugin API System](#plugin-api-system)
3. [Frontend API Client](#frontend-api-client)
4. [React Hooks](#react-hooks)
5. [Error Handling](#error-handling)
6. [Middleware System](#middleware-system)
7. [Monitoring & Metrics](#monitoring--metrics)
8. [Security & Validation](#security--validation)

## Core API Endpoints

### Database APIs (`db` category)

#### `db:getConversations`
Retrieve all conversations from the database.

**Parameters:** None

**Response:**
```typescript
{
  success: true,
  data: Conversation[]
}
```

**Example:**
```typescript
const conversations = await apiClient.getConversations()
```

#### `db:createConversation`
Create a new conversation.

**Parameters:**
- `title` (string): Conversation title (1-200 characters)

**Response:**
```typescript
{
  success: true,
  data: Conversation
}
```

**Example:**
```typescript
const conversation = await apiClient.createConversation('My New Chat')
```

#### `db:getConversation`
Get a specific conversation by ID.

**Parameters:**
- `id` (string): Conversation ID

**Response:**
```typescript
{
  success: true,
  data: Conversation | null
}
```

#### `db:updateConversation`
Update an existing conversation.

**Parameters:**
- `id` (string): Conversation ID
- `title` (string): New title

**Response:**
```typescript
{
  success: true,
  data: Conversation
}
```

#### `db:deleteConversation`
Delete a conversation.

**Parameters:**
- `id` (string): Conversation ID

**Response:**
```typescript
{
  success: true,
  message: string
}
```

#### `db:getMessages`
Get messages for a conversation.

**Parameters:**
- `conversationId` (string): Conversation ID

**Response:**
```typescript
{
  success: true,
  data: Message[]
}
```

#### `db:addMessage`
Add a message to a conversation.

**Parameters:**
- `conversationId` (string): Conversation ID
- `message` (object): Message data

**Response:**
```typescript
{
  success: true,
  data: Message
}
```

### Vault APIs (`vault` category)

#### `vault:readFile`
Read file content from the vault.

**Parameters:**
- `filePath` (string): File path

**Response:**
```typescript
{
  success: true,
  data: string
}
```

**Example:**
```typescript
const content = await apiClient.readFile('/documents/notes.txt')
```

#### `vault:writeFile`
Write content to a file in the vault.

**Parameters:**
- `filePath` (string): File path
- `content` (string): File content

**Response:**
```typescript
{
  success: true,
  message: string
}
```

#### `vault:createDirectory`
Create a directory in the vault.

**Parameters:**
- `dirPath` (string): Directory path

**Response:**
```typescript
{
  success: true,
  message: string
}
```

#### `vault:readDirectory`
List directory contents.

**Parameters:**
- `dirPath` (string): Directory path

**Response:**
```typescript
{
  success: true,
  data: {
    files: string[],
    directories: string[]
  }
}
```

#### `vault:removeFile`
Remove a file from the vault.

**Parameters:**
- `filePath` (string): File path

**Response:**
```typescript
{
  success: true,
  message: string
}
```

#### `vault:removeDirectory`
Remove a directory from the vault.

**Parameters:**
- `dirPath` (string): Directory path

**Response:**
```typescript
{
  success: true,
  message: string
}
```

#### `vault:pathExists`
Check if a path exists in the vault.

**Parameters:**
- `path` (string): File or directory path

**Response:**
```typescript
{
  success: true,
  data: boolean
}
```

#### `vault:getRegistry`
Get the vault registry information.

**Parameters:** None

**Response:**
```typescript
{
  success: true,
  data: VaultRegistry
}
```

### Plugin APIs (`plugins` category)

#### `plugins:getAllPlugins`
Get all available plugins.

**Parameters:** None

**Response:**
```typescript
{
  success: true,
  data: Plugin[]
}
```

#### `plugins:enablePlugin`
Enable a plugin.

**Parameters:**
- `pluginId` (string): Plugin ID
- `config` (object, optional): Plugin configuration

**Response:**
```typescript
{
  success: true,
  message: string
}
```

#### `plugins:disablePlugin`
Disable a plugin.

**Parameters:**
- `pluginId` (string): Plugin ID

**Response:**
```typescript
{
  success: true,
  message: string
}
```

#### `plugins:getPluginConfig`
Get plugin configuration.

**Parameters:**
- `pluginId` (string): Plugin ID

**Response:**
```typescript
{
  success: true,
  data: object
}
```

#### `plugins:updatePluginConfig`
Update plugin configuration.

**Parameters:**
- `pluginId` (string): Plugin ID
- `config` (object): New configuration

**Response:**
```typescript
{
  success: true,
  message: string
}
```

#### `plugins:getPluginAPIEndpoints`
Get API endpoints provided by a plugin.

**Parameters:**
- `pluginId` (string): Plugin ID

**Response:**
```typescript
{
  success: true,
  data: PluginEndpoint[]
}
```

### System APIs (`system` category)

#### `system:healthCheck`
Check system health status.

**Parameters:** None

**Response:**
```typescript
{
  success: true,
  data: {
    healthy: boolean,
    latency: number,
    errors: string[]
  }
}
```

#### `system:getAPIRegistry`
Get the complete API registry.

**Parameters:** None

**Response:**
```typescript
{
  success: true,
  data: {
    categories: string[],
    endpoints: APIEndpoint[]
  }
}
```

#### `system:getMonitoringData`
Get API monitoring data and metrics.

**Parameters:** None

**Response:**
```typescript
{
  success: true,
  data: {
    totalCalls: number,
    successfulCalls: number,
    failedCalls: number,
    averageResponseTime: number,
    successRate: number,
    endpointMetrics: Record<string, EndpointMetrics>
  }
}
```

#### `system:getErrorStatistics`
Get error statistics and history.

**Parameters:** None

**Response:**
```typescript
{
  success: true,
  data: {
    totalErrors: number,
    errorsByCode: Record<string, number>,
    recentErrors: StructuredError[],
    topErrors: Array<{code: string, count: number}>
  }
}
```

#### `system:clearErrorHistory`
Clear error history and statistics.

**Parameters:** None

**Response:**
```typescript
{
  success: true,
  message: string
}
```

#### `system:resetMonitoring`
Reset monitoring data and metrics.

**Parameters:** None

**Response:**
```typescript
{
  success: true,
  message: string
}
```

## Plugin API System

### Plugin Namespace Pattern
Plugin APIs are automatically namespaced using the pattern `plugin_${pluginId}:${endpoint}`.

**Example:**
- Plugin ID: `dataProcessor`
- Endpoint: `processData`
- Full channel: `plugin_dataProcessor:processData`

### Calling Plugin APIs

#### Direct Plugin API Call
```typescript
const result = await apiClient.callPluginAPI(
  'dataProcessor',    // Plugin ID
  'processData',      // Endpoint name
  data,              // First argument
  options            // Second argument
)
```

#### Batch Plugin API Calls
```typescript
const results = await apiClient.batchCall([
  { 
    category: 'plugin_dataProcessor', 
    endpoint: 'processData', 
    args: [data1, options1] 
  },
  { 
    category: 'plugin_dataProcessor', 
    endpoint: 'processData', 
    args: [data2, options2] 
  }
])
```

### Plugin Endpoint Discovery
```typescript
// Get all plugins with API extensions
const plugins = await apiClient.getAllPlugins()
const apiPlugins = plugins.filter(p => p.capabilities.includes('API_EXTENSION'))

// Get endpoints for a specific plugin
const endpoints = await apiClient.getPluginAPIEndpoints('dataProcessor')
console.log('Available endpoints:', endpoints.data)
```

## Frontend API Client

### UnifiedAPIClient Class

#### Constructor Options
```typescript
interface APIClientOptions {
  timeout?: number        // Default: 10000ms
  retryAttempts?: number  // Default: 3
  retryDelay?: number     // Default: 1000ms
}

const apiClient = new UnifiedAPIClient(options)
```

#### Core Methods

##### `call<T>(category: string, endpoint: string, args?: any[], options?: APICallOptions): Promise<T>`
Make a direct API call to any registered endpoint.

```typescript
const result = await apiClient.call('db', 'getConversations')
const conversation = await apiClient.call('db', 'createConversation', ['My Chat'])
```

##### `batchCall(calls: BatchCall[]): Promise<any[]>`
Execute multiple API calls in a single batch operation.

```typescript
const results = await apiClient.batchCall([
  { category: 'db', endpoint: 'getConversations' },
  { category: 'vault', endpoint: 'getRegistry' },
  { category: 'plugins', endpoint: 'getAllPlugins' }
])
```

##### `callPluginAPI(pluginId: string, endpoint: string, ...args: any[]): Promise<any>`
Call a plugin-specific API endpoint.

```typescript
const result = await apiClient.callPluginAPI('myPlugin', 'customEndpoint', arg1, arg2)
```

##### `healthCheck(): Promise<HealthStatus>`
Check the health status of the API system.

```typescript
const health = await apiClient.healthCheck()
console.log('System healthy:', health.healthy)
```

#### Convenience Methods

The API client provides convenience methods organized by category:

```typescript
// Database operations
import { db } from '../api/UnifiedAPIClient'
const conversations = await db.conversations.getAll()
const files = await db.files.getAll()

// Vault operations
import { vault } from '../api/UnifiedAPIClient'
const content = await vault.readFile('/path/to/file.txt')
const registry = await vault.getRegistry()

// Plugin operations
import { plugins } from '../api/UnifiedAPIClient'
const allPlugins = await plugins.getAll()
const pluginConfig = await plugins.getConfig('pluginId')

// System operations
import { system } from '../api/UnifiedAPIClient'
const health = await system.healthCheck()
const monitoring = await system.getMonitoringData()
```

### API Call Options

```typescript
interface APICallOptions {
  timeout?: number          // Request timeout in milliseconds
  retries?: number          // Number of retry attempts
  retryDelay?: number       // Delay between retries in milliseconds
  validateResponse?: boolean // Validate response structure
}

// Example with options
const result = await apiClient.call('db', 'getConversations', [], {
  timeout: 5000,
  retries: 2,
  retryDelay: 500
})
```

## React Hooks

### Core Hooks

#### `useAPI<T>(apiCall: () => Promise<T>, options?: UseAPIOptions): UseAPIState<T>`
Generic hook for API calls with loading states and error handling.

```typescript
const { data, loading, error, refetch } = useAPI(
  () => apiClient.getConversations(),
  { immediate: true }
)
```

#### `useAPIMutation<TArgs, TResult>(mutationFn: (...args: TArgs) => Promise<TResult>)`
Hook for API mutations with loading states.

```typescript
const createConversation = useAPIMutation(
  (title: string) => apiClient.createConversation(title)
)

// Usage
const handleCreate = async () => {
  try {
    const result = await createConversation.mutate('New Chat')
    console.log('Created:', result)
  } catch (error) {
    console.error('Failed:', error)
  }
}
```

### Specialized Hooks

#### Database Hooks
```typescript
// Conversations
const { data: conversations, loading, error } = useConversations()
const { data: conversation } = useConversation(conversationId)
const { data: messages } = useMessages(conversationId)

// Files
const { data: files } = useFiles()

// Mutations
const createConversation = useCreateConversation()
const updateConversation = useUpdateConversation()
const deleteConversation = useDeleteConversation()
const addMessage = useAddMessage()
```

#### Plugin Hooks
```typescript
const { data: plugins } = usePlugins()
const { data: endpoints } = usePluginAPIEndpoints(pluginId)
const pluginOperation = usePluginOperation()

// Usage
const handlePluginCall = async () => {
  const result = await pluginOperation.mutate('callAPI', pluginId, endpoint, ...args)
}
```

#### System Hooks
```typescript
const { data: monitoringData } = useMonitoringData()
const apiHealth = useAPIHealth(10000) // Check every 10 seconds
const batchAPI = useBatchAPI()

// Polling hook
const { data: realtimeData } = usePolling(
  () => apiClient.getMonitoringData(),
  5000 // Poll every 5 seconds
)
```

### Hook Options

```typescript
interface UseAPIOptions extends APICallOptions {
  immediate?: boolean    // Execute immediately on mount (default: true)
  dependencies?: any[]   // Dependencies for re-execution
}

// Example with dependencies
const { data: conversation } = useAPI(
  () => conversationId ? apiClient.getConversation(conversationId) : null,
  { 
    dependencies: [conversationId],
    immediate: !!conversationId 
  }
)
```

## Error Handling

### Error Types

#### `APIError`
Standard API error thrown by the UnifiedAPIClient.

```typescript
class APIError extends Error {
  code: string
  details?: any
  timestamp: string
  category?: string
  endpoint?: string
}
```

#### `StructuredAPIError`
Backend error with structured information.

```typescript
interface StructuredError {
  code: ErrorCode
  message: string
  details?: any
  timestamp: string
  severity: ErrorSeverity
  category: string
  endpoint?: string
  context?: Record<string, any>
}
```

### Error Codes

```typescript
enum ErrorCode {
  // General
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  INVALID_INPUT = 'INVALID_INPUT',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  TIMEOUT = 'TIMEOUT',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  
  // Authentication/Authorization
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  
  // Database
  DATABASE_ERROR = 'DATABASE_ERROR',
  RECORD_NOT_FOUND = 'RECORD_NOT_FOUND',
  DUPLICATE_RECORD = 'DUPLICATE_RECORD',
  
  // File System
  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
  FILE_ACCESS_DENIED = 'FILE_ACCESS_DENIED',
  
  // Plugin
  PLUGIN_NOT_FOUND = 'PLUGIN_NOT_FOUND',
  PLUGIN_EXECUTION_ERROR = 'PLUGIN_EXECUTION_ERROR'
}
```

### Error Handling Patterns

#### Try-Catch with Structured Errors
```typescript
try {
  const result = await apiClient.getConversations()
  return result
} catch (error) {
  if (error instanceof APIError) {
    console.error(`API Error [${error.code}]:`, error.message)
    // Handle specific error codes
    switch (error.code) {
      case 'PERMISSION_DENIED':
        // Handle permission error
        break
      case 'RATE_LIMIT_EXCEEDED':
        // Handle rate limiting
        break
      default:
        // Handle general error
    }
  } else {
    console.error('Unexpected error:', error)
  }
}
```

#### Hook Error Handling
```typescript
const { data, loading, error } = useConversations()

if (error) {
  return <div className="error">Error: {error}</div>
}

if (loading) {
  return <div className="loading">Loading...</div>
}

return <ConversationList conversations={data} />
```

#### Retry Logic
```typescript
const retryableOperation = async () => {
  try {
    return await apiClient.call('db', 'getConversations', [], {
      retries: 3,
      retryDelay: 1000
    })
  } catch (error) {
    if (error.code === 'TIMEOUT' || error.code === 'NETWORK_ERROR') {
      // These errors are automatically retried
      console.log('Operation failed after retries')
    }
    throw error
  }
}
```

## Middleware System

### Middleware Types

#### Request Middleware
Executed before the API handler is called.

```typescript
interface MiddlewareContext {
  event: IpcMainInvokeEvent
  category: string
  endpoint: string
  args: any[]
  startTime: number
  metadata: Record<string, any>
}

type MiddlewareFunction = (context: MiddlewareContext) => Promise<void>
```

#### Built-in Middleware
- **RequestLoggingMiddleware**: Logs all API requests
- **RateLimitingMiddleware**: Enforces rate limits
- **SecurityMiddleware**: Validates permissions and authentication
- **ValidationMiddleware**: Validates input parameters
- **PerformanceMiddleware**: Tracks performance metrics
- **ErrorHandlingMiddleware**: Handles and structures errors

### Custom Middleware Example

```typescript
export class CustomMiddleware {
  async execute(context: MiddlewareContext): Promise<void> {
    // Pre-processing logic
    console.log(`Processing ${context.category}:${context.endpoint}`)

    // Add custom metadata
    context.metadata.customData = 'processed'

    // Conditional logic
    if (context.category === 'sensitive') {
      // Additional security checks
      await this.performSecurityCheck(context)
    }
  }

  private async performSecurityCheck(context: MiddlewareContext): Promise<void> {
    // Custom security logic
  }
}
```

## Monitoring & Metrics

### Performance Metrics

#### System-wide Metrics
```typescript
interface APIMetrics {
  totalCalls: number
  successfulCalls: number
  failedCalls: number
  averageResponseTime: number
  p95ResponseTime: number
  p99ResponseTime: number
  requestsPerSecond: number
  errorRate: number
  uptime: number
}
```

#### Endpoint-specific Metrics
```typescript
interface EndpointMetrics {
  calls: number
  averageTime: number
  minTime: number
  maxTime: number
  errors: number
  lastCalled: Date
  successRate: number
}
```

### Monitoring API Usage

```typescript
// Get system-wide monitoring data
const monitoring = await apiClient.call('system', 'getMonitoringData')
console.log('Total API calls:', monitoring.data.totalCalls)
console.log('Success rate:', monitoring.data.successRate)

// Get error statistics
const errorStats = await apiClient.call('system', 'getErrorStatistics')
console.log('Total errors:', errorStats.data.totalErrors)
console.log('Top errors:', errorStats.data.topErrors)

// Health check
const health = await apiClient.healthCheck()
console.log('System healthy:', health.healthy)
console.log('API latency:', health.latency)
```

### Real-time Monitoring with React

```typescript
import { useAPIHealth, useMonitoringData } from '../hooks/useAPI'

export const SystemMonitor: React.FC = () => {
  const apiHealth = useAPIHealth(5000) // Check every 5 seconds
  const { data: monitoring } = useMonitoringData()

  return (
    <div className="system-monitor">
      <div className={`health-status ${apiHealth?.healthy ? 'healthy' : 'unhealthy'}`}>
        <h3>System Health</h3>
        <p>Status: {apiHealth?.healthy ? 'Healthy' : 'Unhealthy'}</p>
        <p>Latency: {apiHealth?.latency}ms</p>
        {apiHealth?.errors.length > 0 && (
          <ul>
            {apiHealth.errors.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        )}
      </div>

      <div className="metrics">
        <h3>API Metrics</h3>
        <p>Total Calls: {monitoring?.data?.totalCalls || 0}</p>
        <p>Success Rate: {monitoring?.data?.successRate || 0}%</p>
        <p>Avg Response Time: {monitoring?.data?.averageResponseTime || 0}ms</p>
      </div>
    </div>
  )
}
```

## Security & Validation

### Input Validation

#### Validation Schema
```typescript
interface ValidationSchema {
  [key: string]: ValidationRule | ValidationRule[]
}

interface ValidationRule {
  type: 'string' | 'number' | 'boolean' | 'object' | 'array'
  min?: number
  max?: number
  pattern?: RegExp
  enum?: any[]
  allowEmpty?: boolean
  sanitize?: boolean
}
```

#### Example Validation Schemas
```typescript
// String validation
const stringSchema = {
  title: {
    type: 'string',
    min: 1,
    max: 200,
    pattern: /^[a-zA-Z0-9\s\-_]+$/,
    sanitize: true
  }
}

// Object validation
const objectSchema = {
  user: {
    type: 'object',
    properties: {
      name: { type: 'string', min: 1 },
      age: { type: 'number', min: 0, max: 150 },
      email: { type: 'string', pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/ }
    }
  }
}

// Array validation
const arraySchema = {
  items: {
    type: 'array',
    min: 1,
    max: 100,
    itemType: 'string'
  }
}
```

### Permission System

#### Permission Structure
```typescript
interface Permission {
  id: string          // e.g., 'db.read', 'vault.write'
  name: string        // Human-readable name
  description: string // Permission description
  category: string    // Permission category
  level: 'read' | 'write' | 'admin'
}
```

#### Security Context
```typescript
interface SecurityContext {
  userId?: string
  sessionId?: string
  permissions: string[]
  rateLimit: {
    requests: number
    windowStart: number
  }
}
```

#### Permission Checking
```typescript
// In endpoint definition
this.apiRegistry.registerEndpoint('db', 'sensitiveOperation',
  async (data: any) => {
    // Handler implementation
    return { success: true, data: processedData }
  },
  {
    description: 'Perform sensitive database operation',
    requiresAuth: true,
    requiredPermission: 'db.admin',
    validationSchema: {
      data: { type: 'object' }
    },
    rateLimit: { maxRequests: 10, windowMs: 60000 }
  }
)
```

### Rate Limiting

#### Rate Limit Configuration
```typescript
interface RateLimit {
  maxRequests: number  // Maximum requests allowed
  windowMs: number     // Time window in milliseconds
}

// Examples
const rateLimits = {
  standard: { maxRequests: 100, windowMs: 60000 },    // 100 per minute
  heavy: { maxRequests: 10, windowMs: 60000 },        // 10 per minute
  burst: { maxRequests: 1000, windowMs: 3600000 }     // 1000 per hour
}
```

#### Rate Limiting in Practice
```typescript
// Endpoint with rate limiting
this.apiRegistry.registerEndpoint('api', 'heavyOperation',
  async (data: any) => {
    // CPU-intensive operation
    return await this.performHeavyComputation(data)
  },
  {
    description: 'CPU-intensive operation',
    rateLimit: { maxRequests: 5, windowMs: 300000 }, // 5 per 5 minutes
    validationSchema: {
      data: { type: 'object' }
    }
  }
)
```

## Best Practices

### API Design
1. **Consistent Naming**: Use clear, consistent naming for endpoints
2. **Proper Validation**: Always validate input parameters
3. **Error Handling**: Use structured errors with appropriate codes
4. **Documentation**: Provide comprehensive descriptions
5. **Versioning**: Consider versioning for breaking changes

### Security
1. **Input Sanitization**: Always sanitize user inputs
2. **Permission Checks**: Implement appropriate permission requirements
3. **Rate Limiting**: Protect against abuse with rate limiting
4. **Audit Logging**: Log important operations for security auditing

### Performance
1. **Async Operations**: Use async/await for all I/O operations
2. **Batch Operations**: Provide batch endpoints for bulk operations
3. **Caching**: Implement caching for expensive operations
4. **Monitoring**: Monitor performance and set up alerts

### Plugin Development
1. **Namespace Isolation**: Use proper plugin namespaces
2. **Error Handling**: Implement comprehensive error handling
3. **Testing**: Write thorough tests for plugin endpoints
4. **Documentation**: Document plugin APIs clearly

This API reference provides comprehensive documentation for all aspects of the ChatLo Unified IPC System, enabling developers to effectively use the system for building robust applications and plugins.
