#!/usr/bin/env node

/**
 * Migration Script: Externalize Model Updater
 * 
 * This script helps migrate the modelUpdate/ directory to an external repository
 * and updates the main ChatLo application to use external manifest URLs.
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

const COLORS = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = 'reset') {
  console.log(`${COLORS[color]}${message}${COLORS.reset}`)
}

function checkPrerequisites() {
  log('\n🔍 Checking prerequisites...', 'blue')
  
  // Check if modelUpdate directory exists
  if (!fs.existsSync('modelUpdate')) {
    log('❌ modelUpdate/ directory not found', 'red')
    process.exit(1)
  }
  
  // Check if git is available
  try {
    execSync('git --version', { stdio: 'ignore' })
    log('✅ Git is available', 'green')
  } catch (error) {
    log('❌ Git is not available', 'red')
    process.exit(1)
  }
  
  // Check if we're in a git repository
  try {
    execSync('git rev-parse --git-dir', { stdio: 'ignore' })
    log('✅ In a git repository', 'green')
  } catch (error) {
    log('❌ Not in a git repository', 'red')
    process.exit(1)
  }
  
  log('✅ All prerequisites met', 'green')
}

function analyzeModelUpdateDirectory() {
  log('\n📊 Analyzing modelUpdate/ directory...', 'blue')
  
  const modelUpdatePath = path.join(process.cwd(), 'modelUpdate')
  const files = fs.readdirSync(modelUpdatePath, { withFileTypes: true })
  
  let totalSize = 0
  let fileCount = 0
  let nodeModulesSize = 0
  
  function calculateSize(dirPath) {
    const items = fs.readdirSync(dirPath, { withFileTypes: true })
    let size = 0
    
    for (const item of items) {
      const itemPath = path.join(dirPath, item.name)
      if (item.isDirectory()) {
        const dirSize = calculateSize(itemPath)
        size += dirSize
        if (item.name === 'node_modules') {
          nodeModulesSize += dirSize
        }
      } else {
        const stats = fs.statSync(itemPath)
        size += stats.size
        fileCount++
      }
    }
    
    return size
  }
  
  totalSize = calculateSize(modelUpdatePath)
  
  log(`📁 Total files: ${fileCount}`, 'cyan')
  log(`📦 Total size: ${(totalSize / 1024 / 1024).toFixed(2)} MB`, 'cyan')
  log(`📦 node_modules size: ${(nodeModulesSize / 1024 / 1024).toFixed(2)} MB`, 'cyan')
  
  // Check for models-manifest.json
  const manifestPath = path.join(modelUpdatePath, 'models-manifest.json')
  if (fs.existsSync(manifestPath)) {
    const manifestStats = fs.statSync(manifestPath)
    log(`📄 models-manifest.json: ${(manifestStats.size / 1024).toFixed(2)} KB`, 'cyan')
  }
  
  return { totalSize, nodeModulesSize, fileCount }
}

function createExternalRepository() {
  log('\n🏗️  Creating external repository structure...', 'blue')
  
  const externalPath = path.join(process.cwd(), '..', 'chatlo-model-updater')
  
  if (fs.existsSync(externalPath)) {
    log('⚠️  External repository directory already exists', 'yellow')
    log(`   Path: ${externalPath}`, 'yellow')
    return externalPath
  }
  
  // Create external directory
  fs.mkdirSync(externalPath, { recursive: true })
  log(`✅ Created directory: ${externalPath}`, 'green')
  
  // Copy modelUpdate contents
  log('📋 Copying modelUpdate/ contents...', 'blue')
  execSync(`cp -r modelUpdate/* "${externalPath}/"`, { stdio: 'inherit' })
  
  // Initialize git repository
  log('🔧 Initializing git repository...', 'blue')
  execSync('git init', { cwd: externalPath, stdio: 'inherit' })
  execSync('git add .', { cwd: externalPath, stdio: 'inherit' })
  execSync('git commit -m "Initial model updater extraction from ChatLo"', { 
    cwd: externalPath, 
    stdio: 'inherit' 
  })
  
  // Create GitHub Actions workflow
  const workflowDir = path.join(externalPath, '.github', 'workflows')
  fs.mkdirSync(workflowDir, { recursive: true })
  
  const workflowContent = fs.readFileSync('.context/external-model-updater-workflow.yml', 'utf8')
  fs.writeFileSync(path.join(workflowDir, 'update-models.yml'), workflowContent)
  
  log('✅ Created GitHub Actions workflow', 'green')
  
  return externalPath
}

function updateMainApplication() {
  log('\n🔧 Updating main application configuration...', 'blue')
  
  // The modelUpdateLogic.ts has already been updated with external URLs
  log('✅ modelUpdateLogic.ts already configured for external URLs', 'green')
  
  // Update package.json to remove any modelUpdate-related scripts
  const packageJsonPath = 'package.json'
  if (fs.existsSync(packageJsonPath)) {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'))
    
    // Remove any modelUpdate-related scripts
    if (packageJson.scripts) {
      const scriptsToRemove = Object.keys(packageJson.scripts).filter(script => 
        script.includes('model') || script.includes('manifest')
      )
      
      if (scriptsToRemove.length > 0) {
        log(`🗑️  Removing scripts: ${scriptsToRemove.join(', ')}`, 'yellow')
        scriptsToRemove.forEach(script => delete packageJson.scripts[script])
        fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2))
      }
    }
  }
  
  log('✅ Main application updated', 'green')
}

function createMigrationSummary(analysis, externalPath) {
  log('\n📋 Creating migration summary...', 'blue')
  
  const summary = `# Model Updater Externalization Summary

## Migration Completed: ${new Date().toISOString()}

### What was moved:
- **Source**: \`./modelUpdate/\`
- **Destination**: \`${externalPath}\`
- **Files**: ${analysis.fileCount} files
- **Total Size**: ${(analysis.totalSize / 1024 / 1024).toFixed(2)} MB
- **node_modules**: ${(analysis.nodeModulesSize / 1024 / 1024).toFixed(2)} MB

### Bundle Size Reduction:
- **Removed from main app**: ~${(analysis.totalSize / 1024 / 1024).toFixed(2)} MB
- **Main benefit**: Cleaner main application structure
- **Secondary benefit**: Independent deployment of model updates

### External Repository:
- **Location**: ${externalPath}
- **Git initialized**: ✅
- **GitHub Actions**: ✅ (.github/workflows/update-models.yml)
- **Ready for**: GitHub repository creation

### Main App Changes:
- **modelUpdateLogic.ts**: Updated with external URLs
- **Fallback URLs**: Configured for redundancy
- **.gitignore**: Updated to exclude heavy modelUpdate files

### Next Steps:

1. **Create GitHub Repository**:
   \`\`\`bash
   cd "${externalPath}"
   gh repo create chatlo-model-updater --public
   git remote add origin https://github.com/YOUR_USERNAME/chatlo-model-updater.git
   git push -u origin main
   \`\`\`

2. **Configure Secrets** (in GitHub repository settings):
   - \`OPENROUTER_API_KEY\`: Your OpenRouter API key
   - \`CDN_DEPLOY_KEY\`: For CDN deployment (optional)

3. **Enable GitHub Pages**:
   - Go to repository Settings > Pages
   - Source: GitHub Actions
   - This will make manifest available at: https://YOUR_USERNAME.github.io/chatlo-model-updater/models-manifest.json

4. **Test External Loading**:
   - Run main ChatLo app
   - Verify it loads models from external URL
   - Check fallback behavior

5. **Schedule Automation**:
   - GitHub Actions will run daily at 6 AM UTC
   - Manual triggers available via GitHub UI
   - Automatic releases on manifest changes

### Rollback Plan:
If issues occur, restore with:
\`\`\`bash
git checkout HEAD~1 -- modelUpdate/
git reset --hard HEAD~1
\`\`\`

### Monitoring:
- Check GitHub Actions for daily runs
- Monitor main app for model loading issues
- Verify manifest URLs are accessible

---
Generated by: externalize-model-updater.js
`

  fs.writeFileSync('.context/model-updater-migration-summary.md', summary)
  log('✅ Migration summary saved to .context/model-updater-migration-summary.md', 'green')
}

function main() {
  log('🚀 ChatLo Model Updater Externalization', 'bright')
  log('==========================================', 'bright')
  
  try {
    checkPrerequisites()
    const analysis = analyzeModelUpdateDirectory()
    const externalPath = createExternalRepository()
    updateMainApplication()
    createMigrationSummary(analysis, externalPath)
    
    log('\n🎉 Migration completed successfully!', 'green')
    log('\n📋 Next steps:', 'bright')
    log('1. Create GitHub repository for the external updater', 'cyan')
    log('2. Configure GitHub secrets (OPENROUTER_API_KEY)', 'cyan')
    log('3. Enable GitHub Pages for manifest hosting', 'cyan')
    log('4. Test main app with external manifest loading', 'cyan')
    log('5. Remove local modelUpdate/ directory when ready', 'cyan')
    
    log(`\n📁 External repository created at: ${externalPath}`, 'magenta')
    log('📄 See .context/model-updater-migration-summary.md for details', 'magenta')
    
  } catch (error) {
    log(`\n❌ Migration failed: ${error.message}`, 'red')
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}
