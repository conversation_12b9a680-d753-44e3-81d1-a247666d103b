/**
 * Performance Monitor Service
 * Optimized for baseline hardware (9th gen i7 + RTX 2060)
 * Implements resource monitoring and automatic degradation
 */

interface PerformanceMetrics {
  cpuUsage: number
  memoryUsage: number
  processingTime: number
  timestamp: number
}

interface ProcessingBudget {
  immediate: number // < 10ms
  quick: number     // < 50ms
  batch: number     // Background processing
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = []
  private readonly MAX_METRICS_HISTORY = 100
  private readonly CPU_THRESHOLD = 50 // 50% CPU usage threshold
  private readonly MEMORY_THRESHOLD = 100 * 1024 * 1024 // 100MB memory threshold
  
  private readonly PROCESSING_BUDGETS: ProcessingBudget = {
    immediate: 10,  // 10ms for immediate operations
    quick: 50,      // 50ms for quick operations
    batch: 200      // 200ms for batch operations
  }

  private isMonitoring = false
  private monitoringInterval: NodeJS.Timeout | null = null

  /**
   * Start performance monitoring
   */
  startMonitoring(): void {
    if (this.isMonitoring) return

    this.isMonitoring = true
    this.monitoringInterval = setInterval(() => {
      this.collectMetrics()
    }, 5000) // Monitor every 5 seconds

    console.log('Performance monitoring started')
  }

  /**
   * Stop performance monitoring
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) return

    this.isMonitoring = false
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
      this.monitoringInterval = null
    }

    console.log('Performance monitoring stopped')
  }

  /**
   * Collect current performance metrics
   */
  private collectMetrics(): void {
    const metric: PerformanceMetrics = {
      cpuUsage: this.estimateCPUUsage(),
      memoryUsage: this.getMemoryUsage(),
      processingTime: 0, // Will be updated by processing operations
      timestamp: Date.now()
    }

    this.metrics.push(metric)

    // Keep only recent metrics
    if (this.metrics.length > this.MAX_METRICS_HISTORY) {
      this.metrics.shift()
    }
  }

  /**
   * Estimate CPU usage (simplified approach for web environment)
   */
  private estimateCPUUsage(): number {
    // In a web environment, we can't directly measure CPU usage
    // We'll use processing time as a proxy
    const recentMetrics = this.metrics.slice(-5)
    if (recentMetrics.length === 0) return 0

    const avgProcessingTime = recentMetrics.reduce((sum, m) => sum + m.processingTime, 0) / recentMetrics.length
    
    // Convert processing time to estimated CPU usage percentage
    // This is a rough approximation
    return Math.min((avgProcessingTime / 100) * 100, 100)
  }

  /**
   * Get current memory usage
   */
  private getMemoryUsage(): number {
    if ('memory' in performance) {
      // @ts-ignore - performance.memory is available in Chrome
      return performance.memory.usedJSHeapSize || 0
    }
    return 0
  }

  /**
   * Check if system can handle processing based on current load
   */
  canProcess(operationType: 'immediate' | 'quick' | 'batch'): boolean {
    const currentCPU = this.getCurrentCPUUsage()
    const currentMemory = this.getCurrentMemoryUsage()

    // Check CPU threshold
    if (currentCPU > this.CPU_THRESHOLD) {
      console.warn(`CPU usage too high (${currentCPU}%) for ${operationType} processing`)
      return false
    }

    // Check memory threshold
    if (currentMemory > this.MEMORY_THRESHOLD) {
      console.warn(`Memory usage too high (${Math.round(currentMemory / 1024 / 1024)}MB) for ${operationType} processing`)
      return false
    }

    return true
  }

  /**
   * Get processing budget for operation type
   */
  getProcessingBudget(operationType: 'immediate' | 'quick' | 'batch'): number {
    return this.PROCESSING_BUDGETS[operationType]
  }

  /**
   * Record processing time for an operation
   */
  recordProcessingTime(operationType: 'immediate' | 'quick' | 'batch', processingTime: number): void {
    const budget = this.getProcessingBudget(operationType)
    const success = processingTime <= budget

    if (!success) {
      console.warn(`${operationType} operation exceeded budget: ${processingTime}ms > ${budget}ms`)
    }

    // Update latest metric with processing time
    if (this.metrics.length > 0) {
      this.metrics[this.metrics.length - 1].processingTime = processingTime
    }

    // Track analytics for processing time
    this.trackProcessingAnalytics(operationType, processingTime, success)
  }

  /**
   * Track processing analytics (async import to avoid circular dependencies)
   */
  private async trackProcessingAnalytics(operationType: 'immediate' | 'quick' | 'batch', processingTime: number, success: boolean): Promise<void> {
    try {
      const { intelligenceAnalytics } = await import('./intelligenceAnalytics')
      intelligenceAnalytics.trackProcessingTime(operationType, processingTime, success)
    } catch (error) {
      // Silently fail to avoid breaking the performance monitor
      console.debug('Analytics tracking failed:', error)
    }
  }

  /**
   * Get current CPU usage estimate
   */
  getCurrentCPUUsage(): number {
    if (this.metrics.length === 0) return 0
    return this.metrics[this.metrics.length - 1].cpuUsage
  }

  /**
   * Get current memory usage
   */
  getCurrentMemoryUsage(): number {
    if (this.metrics.length === 0) return 0
    return this.metrics[this.metrics.length - 1].memoryUsage
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary(): {
    avgCPU: number
    avgMemory: number
    avgProcessingTime: number
    totalOperations: number
  } {
    if (this.metrics.length === 0) {
      return { avgCPU: 0, avgMemory: 0, avgProcessingTime: 0, totalOperations: 0 }
    }

    const avgCPU = this.metrics.reduce((sum, m) => sum + m.cpuUsage, 0) / this.metrics.length
    const avgMemory = this.metrics.reduce((sum, m) => sum + m.memoryUsage, 0) / this.metrics.length
    const avgProcessingTime = this.metrics.reduce((sum, m) => sum + m.processingTime, 0) / this.metrics.length

    return {
      avgCPU: Math.round(avgCPU),
      avgMemory: Math.round(avgMemory),
      avgProcessingTime: Math.round(avgProcessingTime),
      totalOperations: this.metrics.length
    }
  }

  /**
   * Check if system is under stress
   */
  isSystemUnderStress(): boolean {
    const currentCPU = this.getCurrentCPUUsage()
    const currentMemory = this.getCurrentMemoryUsage()

    return currentCPU > this.CPU_THRESHOLD || currentMemory > this.MEMORY_THRESHOLD
  }

  /**
   * Get recommended processing mode based on current performance
   */
  getRecommendedProcessingMode(): 'full' | 'reduced' | 'minimal' {
    const currentCPU = this.getCurrentCPUUsage()
    const currentMemory = this.getCurrentMemoryUsage()

    if (currentCPU > 70 || currentMemory > this.MEMORY_THRESHOLD * 1.5) {
      return 'minimal'
    } else if (currentCPU > this.CPU_THRESHOLD || currentMemory > this.MEMORY_THRESHOLD) {
      return 'reduced'
    } else {
      return 'full'
    }
  }
}

export const performanceMonitor = new PerformanceMonitor()
