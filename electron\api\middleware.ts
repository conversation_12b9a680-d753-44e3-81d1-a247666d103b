/**
 * Comprehensive Middleware System for APIRegistry
 * Provides logging, rate limiting, error handling, and security middleware
 */

import { IpcMainInvokeEvent } from 'electron'

export interface MiddlewareContext {
  event: IpcMainInvokeEvent
  category: string
  endpoint: string
  args: any[]
  startTime: number
  metadata: Record<string, any>
}

export interface MiddlewareFunction {
  (context: MiddlewareContext): Promise<void> | void
}

export interface RateLimitConfig {
  maxRequests: number
  windowMs: number
  skipSuccessfulRequests?: boolean
  skipFailedRequests?: boolean
}

export interface LoggingConfig {
  logRequests: boolean
  logResponses: boolean
  logErrors: boolean
  logPerformance: boolean
  maxLogLength?: number
}

/**
 * Request Logging Middleware
 */
export class RequestLoggingMiddleware {
  private config: LoggingConfig

  constructor(config: LoggingConfig = {
    logRequests: true,
    logResponses: false,
    logErrors: true,
    logPerformance: true,
    maxLogLength: 1000
  }) {
    this.config = config
  }

  middleware(): MiddlewareFunction {
    return (context: MiddlewareContext) => {
      if (this.config.logRequests) {
        const argsStr = this.config.maxLogLength 
          ? JSON.stringify(context.args).substring(0, this.config.maxLogLength)
          : JSON.stringify(context.args)
        
        console.log(`[API] ${context.category}:${context.endpoint} - Request:`, argsStr)
      }

      // Store start time for performance logging
      context.metadata.requestStartTime = Date.now()
    }
  }

  responseMiddleware(): MiddlewareFunction {
    return (context: MiddlewareContext) => {
      if (this.config.logPerformance && context.metadata.requestStartTime) {
        const duration = Date.now() - context.metadata.requestStartTime
        console.log(`[API] ${context.category}:${context.endpoint} - Duration: ${duration}ms`)
      }
    }
  }
}

/**
 * Rate Limiting Middleware
 */
export class RateLimitingMiddleware {
  private requests: Map<string, { count: number; resetTime: number }> = new Map()
  private config: RateLimitConfig

  constructor(config: RateLimitConfig) {
    this.config = config
  }

  middleware(): MiddlewareFunction {
    return (context: MiddlewareContext) => {
      const key = `${context.event.sender.id}:${context.category}:${context.endpoint}`
      const now = Date.now()
      
      let requestData = this.requests.get(key)
      
      // Reset if window has expired
      if (!requestData || now > requestData.resetTime) {
        requestData = {
          count: 0,
          resetTime: now + this.config.windowMs
        }
      }
      
      requestData.count++
      this.requests.set(key, requestData)
      
      if (requestData.count > this.config.maxRequests) {
        throw new Error(`Rate limit exceeded: ${this.config.maxRequests} requests per ${this.config.windowMs}ms`)
      }
    }
  }

  // Clean up expired entries periodically
  cleanup(): void {
    const now = Date.now()
    for (const [key, data] of this.requests.entries()) {
      if (now > data.resetTime) {
        this.requests.delete(key)
      }
    }
  }
}

/**
 * Security Middleware
 */
export class SecurityMiddleware {
  private allowedOrigins: Set<string>
  private requireAuth: boolean

  constructor(options: {
    allowedOrigins?: string[]
    requireAuth?: boolean
  } = {}) {
    this.allowedOrigins = new Set(options.allowedOrigins || [])
    this.requireAuth = options.requireAuth || false
  }

  middleware(): MiddlewareFunction {
    return (context: MiddlewareContext) => {
      // Check sender origin if configured
      if (this.allowedOrigins.size > 0) {
        const senderOrigin = context.event.sender.getURL()
        const isAllowed = Array.from(this.allowedOrigins).some(origin => 
          senderOrigin.startsWith(origin)
        )
        
        if (!isAllowed) {
          throw new Error(`Unauthorized origin: ${senderOrigin}`)
        }
      }

      // Additional security checks can be added here
      if (this.requireAuth) {
        // Implement authentication logic
        // This is a placeholder for actual auth implementation
        context.metadata.authenticated = true
      }
    }
  }
}

/**
 * Error Handling Middleware
 */
export class ErrorHandlingMiddleware {
  private logErrors: boolean
  private sanitizeErrors: boolean

  constructor(options: {
    logErrors?: boolean
    sanitizeErrors?: boolean
  } = {}) {
    this.logErrors = options.logErrors !== false
    this.sanitizeErrors = options.sanitizeErrors !== false
  }

  middleware(): MiddlewareFunction {
    return (context: MiddlewareContext) => {
      // This middleware doesn't do anything during request processing
      // Error handling is done in the catch block of the API call
    }
  }

  handleError(error: any, context: MiddlewareContext): any {
    if (this.logErrors) {
      console.error(`[API Error] ${context.category}:${context.endpoint}:`, error)
    }

    if (this.sanitizeErrors && error instanceof Error) {
      // Return sanitized error for security
      return {
        success: false,
        error: 'An error occurred while processing the request',
        code: 'INTERNAL_ERROR',
        timestamp: new Date().toISOString()
      }
    }

    return {
      success: false,
      error: error.message || 'Unknown error',
      timestamp: new Date().toISOString()
    }
  }
}

/**
 * Performance Monitoring Middleware
 */
export class PerformanceMiddleware {
  private metrics: Map<string, {
    totalCalls: number
    totalTime: number
    averageTime: number
    minTime: number
    maxTime: number
    lastCall: number
  }> = new Map()

  middleware(): MiddlewareFunction {
    return (context: MiddlewareContext) => {
      context.metadata.performanceStartTime = process.hrtime.bigint()
    }
  }

  responseMiddleware(): MiddlewareFunction {
    return (context: MiddlewareContext) => {
      if (context.metadata.performanceStartTime) {
        const endTime = process.hrtime.bigint()
        const duration = Number(endTime - context.metadata.performanceStartTime) / 1000000 // Convert to ms
        
        const key = `${context.category}:${context.endpoint}`
        let metric = this.metrics.get(key)
        
        if (!metric) {
          metric = {
            totalCalls: 0,
            totalTime: 0,
            averageTime: 0,
            minTime: duration,
            maxTime: duration,
            lastCall: Date.now()
          }
        }
        
        metric.totalCalls++
        metric.totalTime += duration
        metric.averageTime = metric.totalTime / metric.totalCalls
        metric.minTime = Math.min(metric.minTime, duration)
        metric.maxTime = Math.max(metric.maxTime, duration)
        metric.lastCall = Date.now()
        
        this.metrics.set(key, metric)
      }
    }
  }

  getMetrics(): Record<string, any> {
    const result: Record<string, any> = {}
    for (const [key, metric] of this.metrics.entries()) {
      result[key] = { ...metric }
    }
    return result
  }

  resetMetrics(): void {
    this.metrics.clear()
  }
}

/**
 * Validation Middleware
 */
export class ValidationMiddleware {
  middleware(): MiddlewareFunction {
    return (context: MiddlewareContext) => {
      // Basic argument validation
      if (!Array.isArray(context.args)) {
        throw new Error('Invalid arguments: expected array')
      }

      // Add custom validation logic here
      // This can be extended based on specific requirements
    }
  }
}

/**
 * Default Middleware Stack
 */
export class DefaultMiddlewareStack {
  private logging: RequestLoggingMiddleware
  private rateLimiting: RateLimitingMiddleware
  private security: SecurityMiddleware
  private errorHandling: ErrorHandlingMiddleware
  private performance: PerformanceMiddleware
  private validation: ValidationMiddleware

  constructor(options: {
    logging?: LoggingConfig
    rateLimiting?: RateLimitConfig
    security?: { allowedOrigins?: string[]; requireAuth?: boolean }
    errorHandling?: { logErrors?: boolean; sanitizeErrors?: boolean }
  } = {}) {
    this.logging = new RequestLoggingMiddleware(options.logging)
    this.rateLimiting = new RateLimitingMiddleware(options.rateLimiting || {
      maxRequests: 100,
      windowMs: 60000 // 1 minute
    })
    this.security = new SecurityMiddleware(options.security)
    this.errorHandling = new ErrorHandlingMiddleware(options.errorHandling)
    this.performance = new PerformanceMiddleware()
    this.validation = new ValidationMiddleware()
  }

  getGlobalMiddleware(): MiddlewareFunction[] {
    return [
      this.security.middleware(),
      this.validation.middleware(),
      this.rateLimiting.middleware(),
      this.logging.middleware(),
      this.performance.middleware()
    ]
  }

  getResponseMiddleware(): MiddlewareFunction[] {
    return [
      this.performance.responseMiddleware(),
      this.logging.responseMiddleware()
    ]
  }

  getErrorHandler(): ErrorHandlingMiddleware {
    return this.errorHandling
  }

  getPerformanceMetrics(): Record<string, any> {
    return this.performance.getMetrics()
  }

  // Cleanup method to be called periodically
  cleanup(): void {
    this.rateLimiting.cleanup()
  }
}
