/**
 * Basic Image Processing Plugin
 * Core plugin for basic image file processing without external dependencies
 */

import * as fs from 'fs'
import * as path from 'path'
import * as mime from 'mime-types'
import { FileProcessorPlugin, ProcessedFileContent } from '../types'

export default class BasicImagePlugin implements FileProcessorPlugin {
  name = 'BasicImagePlugin'
  version = '1.0.0'
  description = 'Core plugin for basic image file processing'
  author = 'ChatLo Team'
  optional = false

  supportedTypes = ['image']
  supportedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.ico']

  canProcess(filePath: string, fileType: string): boolean {
    const extension = path.extname(filePath).toLowerCase()
    return fileType === 'image' || this.supportedExtensions.includes(extension)
  }

  async process(filePath: string): Promise<ProcessedFileContent> {
    try {
      const stats = await fs.promises.stat(filePath)
      const extension = path.extname(filePath).toLowerCase()
      const mimeType = mime.lookup(filePath) || 'unknown'
      const filename = path.basename(filePath)

      // Check file size (limit to 50MB for images)
      const maxSize = 50 * 1024 * 1024 // 50MB
      if (stats.size > maxSize) {
        return {
          error: `Image file too large: ${Math.round(stats.size / 1024 / 1024)}MB (max: 50MB)`
        }
      }

      // Basic image information without external dependencies
      const basicInfo = {
        filename,
        extension,
        mimeType,
        fileSize: stats.size,
        lastModified: stats.mtime,
        processor: this.name,
        processingLevel: 'basic'
      }

      // For SVG files, we can extract some text content
      if (extension === '.svg') {
        try {
          const svgContent = await fs.promises.readFile(filePath, 'utf8')
          const textContent = this.extractSVGText(svgContent)
          
          return {
            text: `SVG Image: ${filename}\n${textContent}`,
            metadata: {
              ...basicInfo,
              svgElements: this.countSVGElements(svgContent),
              hasText: textContent.length > 0
            }
          }
        } catch (error) {
          console.warn('Failed to process SVG content:', error)
        }
      }

      // For other image types, provide basic description
      const description = this.generateImageDescription(filename, extension, stats.size, mimeType)

      return {
        text: description,
        metadata: basicInfo
      }

    } catch (error: any) {
      console.error('Error processing image file:', error)
      return {
        error: `Failed to process image file: ${error?.message || 'Unknown error'}`
      }
    }
  }

  // Extract text content from SVG files
  private extractSVGText(svgContent: string): string {
    const textElements = svgContent.match(/<text[^>]*>(.*?)<\/text>/gi) || []
    const titleElements = svgContent.match(/<title[^>]*>(.*?)<\/title>/gi) || []
    const descElements = svgContent.match(/<desc[^>]*>(.*?)<\/desc>/gi) || []

    const allText = [...textElements, ...titleElements, ...descElements]
      .map(element => element.replace(/<[^>]*>/g, '').trim())
      .filter(text => text.length > 0)
      .join(' ')

    return allText
  }

  // Count SVG elements
  private countSVGElements(svgContent: string): Record<string, number> {
    const elements = ['rect', 'circle', 'ellipse', 'line', 'polyline', 'polygon', 'path', 'text', 'image', 'g']
    const counts: Record<string, number> = {}

    for (const element of elements) {
      const regex = new RegExp(`<${element}[^>]*>`, 'gi')
      const matches = svgContent.match(regex) || []
      counts[element] = matches.length
    }

    return counts
  }

  // Generate basic image description
  private generateImageDescription(filename: string, extension: string, fileSize: number, mimeType: string): string {
    const sizeKB = Math.round(fileSize / 1024)
    const sizeMB = Math.round(fileSize / 1024 / 1024 * 100) / 100

    let sizeText = sizeKB < 1024 ? `${sizeKB} KB` : `${sizeMB} MB`

    const typeDescription = this.getImageTypeDescription(extension)

    return `Image: ${filename}
File type: ${typeDescription}
MIME type: ${mimeType}
File size: ${sizeText}
Extension: ${extension}`
  }

  // Get human-readable image type description
  private getImageTypeDescription(extension: string): string {
    const descriptions: Record<string, string> = {
      '.jpg': 'JPEG Image',
      '.jpeg': 'JPEG Image',
      '.png': 'PNG Image',
      '.gif': 'GIF Image',
      '.bmp': 'Bitmap Image',
      '.webp': 'WebP Image',
      '.svg': 'SVG Vector Image',
      '.ico': 'Icon File'
    }

    return descriptions[extension] || 'Image File'
  }
}
