# ChatLo Unified IPC System - Internal Developer Documentation

## Table of Contents
1. [System Overview](#system-overview)
2. [Architecture Design](#architecture-design)
3. [Core Module Design](#core-module-design)
4. [Plugin System Architecture](#plugin-system-architecture)
5. [Frontend-Backend Communication](#frontend-backend-communication)
6. [Data Flow & Middleware](#data-flow--middleware)
7. [Implementation Examples](#implementation-examples)
8. [Development Guidelines](#development-guidelines)

## System Overview

The ChatLo Unified IPC System is a comprehensive inter-process communication framework that provides:

- **Centralized API Registry**: Single point of registration for all API endpoints
- **Plugin API Extension System**: Dynamic plugin API registration with namespace isolation
- **Comprehensive Middleware Stack**: Security, validation, monitoring, and error handling
- **Structured Error Handling**: Unified error management with codes and statistics
- **Real-time Monitoring**: Performance tracking and system health monitoring
- **Unified Frontend Client**: Type-safe API client with React hooks

### Key Benefits
- **Developer Experience**: Type-safe APIs with comprehensive error handling
- **Security**: Built-in validation, permission checks, and rate limiting
- **Monitoring**: Real-time performance metrics and error tracking
- **Extensibility**: Plugin system for custom API endpoints
- **Maintainability**: Centralized registration and structured error handling

## Architecture Design

```mermaid
graph TB
    subgraph "Frontend (Renderer Process)"
        A[React Components] --> B[UnifiedAPIClient]
        B --> C[React Hooks]
        C --> D[electronAPI Bridge]
    end
    
    subgraph "Backend (Main Process)"
        D --> E[APIRegistry]
        E --> F[Middleware Stack]
        F --> G[Security Manager]
        F --> H[Validator]
        F --> I[Monitor]
        E --> J[Core APIs]
        E --> K[Plugin APIs]
    end
    
    subgraph "Core Systems"
        J --> L[Database Manager]
        J --> M[File System Manager]
        J --> N[Vault Manager]
        J --> O[Plugin Manager]
    end
    
    subgraph "Plugin System"
        K --> P[Plugin Namespace]
        P --> Q[Plugin API Extension]
        Q --> R[Custom Endpoints]
    end
    
    subgraph "Monitoring & Error Handling"
        I --> S[Performance Metrics]
        I --> T[System Health]
        F --> U[Error Handler]
        U --> V[Structured Errors]
    end
```

### System Components

#### 1. APIRegistry (Core)
- **Location**: `electron/api/APIRegistry.ts`
- **Purpose**: Central registration and routing for all API endpoints
- **Features**: Category-based organization, middleware integration, monitoring

#### 2. Middleware Stack
- **Location**: `electron/api/middleware.ts`
- **Components**:
  - RequestLoggingMiddleware
  - RateLimitingMiddleware
  - SecurityMiddleware
  - ErrorHandlingMiddleware
  - PerformanceMiddleware
  - ValidationMiddleware

#### 3. Security Manager
- **Location**: `electron/api/validation.ts`
- **Features**: Permission management, input sanitization, rate limiting

#### 4. Error Handler
- **Location**: `electron/api/errorHandling.ts`
- **Features**: Structured errors, error codes, statistics tracking

#### 5. API Monitor
- **Location**: `electron/api/monitoring.ts`
- **Features**: Performance metrics, system health, alerting

## Core Module Design

### APIRegistry Class Structure

```typescript
export class APIRegistry {
  private categories: Map<string, APICategory> = new Map()
  private globalMiddleware: Function[] = []
  private middlewareStack: DefaultMiddlewareStack
  private monitor: APIMonitor
  private securityManager: SecurityManager

  // Core methods
  registerCategory(name: string, middleware?: Function[]): void
  registerEndpoint(category: string, name: string, handler: Function, options?: EndpointOptions): void
  initialize(): void
  
  // Helper methods
  createFilePathValidator(): ValidationSchema
  createPluginIdValidator(): ValidationSchema
  getErrorStatistics(): any
}
```

### Endpoint Registration Pattern

```typescript
interface APIEndpoint {
  handler: Function
  validator?: Function
  validationSchema?: ValidationSchema
  middleware?: Function[]
  description?: string
  requiresAuth?: boolean
  requiredPermission?: string
  rateLimit?: { maxRequests: number; windowMs: number }
}
```

### File Structure

```
electron/
├── api/
│   ├── APIRegistry.ts          # Core registry system
│   ├── middleware.ts           # Middleware stack
│   ├── monitoring.ts           # Performance monitoring
│   ├── validation.ts           # Security & validation
│   └── errorHandling.ts        # Error management
├── plugins/
│   ├── PluginManager.ts        # Plugin management
│   ├── extensionPoints.ts      # Plugin interfaces
│   └── examples/               # Example plugins
└── main.ts                     # Main process entry

src/
├── api/
│   └── UnifiedAPIClient.ts     # Frontend API client
├── hooks/
│   └── useAPI.ts               # React hooks
└── components/
    ├── APIClientExample.tsx    # Usage examples
    └── PluginAPIExplorer.tsx   # Plugin API testing
```

## Plugin System Architecture

### Plugin API Extension Interface

```typescript
export interface APIExtension {
  getNamespace(): string
  registerEndpoints(apiRegistry: APIRegistry): PluginAPINamespace
  provideMiddleware?(): Function[]
  discoverEndpoints?(): Promise<PluginEndpoint[]>
  validateEndpoint?(endpoint: string, args: any[]): boolean
}
```

### Plugin Namespace System

```mermaid
graph LR
    A[Plugin ID: myPlugin] --> B[Namespace: plugin_myPlugin]
    B --> C[Endpoint: getData]
    C --> D[Channel: plugin_myPlugin:getData]
    
    E[Plugin ID: dataProcessor] --> F[Namespace: plugin_dataProcessor]
    F --> G[Endpoint: processData]
    G --> H[Channel: plugin_dataProcessor:processData]
```

### Plugin Registration Flow

1. **Plugin Loading**: PluginManager loads plugin with API_EXTENSION capability
2. **Namespace Generation**: Automatic namespace creation (`plugin_${pluginId}`)
3. **Endpoint Registration**: Plugin registers endpoints through APIExtension interface
4. **IPC Handler Setup**: APIRegistry creates IPC handlers for plugin endpoints
5. **Frontend Discovery**: Frontend can discover plugin APIs dynamically

## Frontend-Backend Communication

### Communication Flow

```mermaid
sequenceDiagram
    participant FC as Frontend Component
    participant UC as UnifiedAPIClient
    participant EA as electronAPI
    participant AR as APIRegistry
    participant MW as Middleware
    participant H as Handler

    FC->>UC: apiClient.getConversations()
    UC->>EA: invoke('db:getConversations')
    EA->>AR: Route to endpoint
    AR->>MW: Execute middleware chain
    MW->>MW: Security, Validation, Logging
    MW->>H: Call handler
    H->>MW: Return result
    MW->>AR: Process response
    AR->>EA: Return structured response
    EA->>UC: Receive response
    UC->>FC: Return typed data
```

### API Client Usage Patterns

#### Direct API Calls
```typescript
import { apiClient } from '../api/UnifiedAPIClient'

// Direct method calls
const conversations = await apiClient.getConversations()
const conversation = await apiClient.getConversation(id)

// Convenience methods
import { db, vault, plugins } from '../api/UnifiedAPIClient'
const files = await db.files.getAll()
const registry = await vault.getRegistry()
const allPlugins = await plugins.getAll()
```

#### React Hooks
```typescript
import { useConversations, useCreateConversation } from '../hooks/useAPI'

function ConversationList() {
  const { data: conversations, loading, error, refetch } = useConversations()
  const createConversation = useCreateConversation()
  
  const handleCreate = async (title: string) => {
    await createConversation.mutate(title)
    refetch()
  }
  
  // Component JSX...
}
```

## Data Flow & Middleware

### Middleware Execution Order

```mermaid
graph TD
    A[API Request] --> B[Request Logging]
    B --> C[Rate Limiting]
    C --> D[Security Checks]
    D --> E[Input Validation]
    E --> F[Performance Monitoring Start]
    F --> G[Handler Execution]
    G --> H[Performance Monitoring End]
    H --> I[Response Processing]
    I --> J[Error Handling]
    J --> K[Response Logging]
    K --> L[API Response]
```

### Middleware Context

```typescript
interface MiddlewareContext {
  event: IpcMainInvokeEvent
  category: string
  endpoint: string
  args: any[]
  startTime: number
  metadata: Record<string, any>
}
```

### Error Handling Flow

```mermaid
graph TD
    A[Error Occurs] --> B[ErrorHandler.handleError()]
    B --> C[Create StructuredAPIError]
    C --> D[Log Error by Severity]
    D --> E[Update Error Statistics]
    E --> F[Return Structured Response]
    
    G[Error Statistics] --> H[Error Counts by Code]
    G --> I[Error History]
    G --> J[Top Errors]
    G --> K[Recent Errors]
```

## Implementation Examples

### 1. Registering Core API Endpoints

```typescript
// In main.ts - registerCoreAPIs method
private registerCoreAPIs(): void {
  // Database category
  this.apiRegistry.registerCategory('db')
  
  this.apiRegistry.registerEndpoint('db', 'getConversations',
    () => this.db.getConversations(),
    {
      description: 'Get all conversations',
      requiredPermission: 'db.read',
      validationSchema: {}, // No args to validate
      rateLimit: { maxRequests: 100, windowMs: 60000 }
    }
  )
  
  this.apiRegistry.registerEndpoint('db', 'createConversation',
    (title: string) => this.db.createConversation(title),
    {
      description: 'Create a new conversation',
      requiredPermission: 'db.write',
      validationSchema: {
        title: { type: 'string', min: 1, max: 200 }
      }
    }
  )
}
```

### 2. Creating a Plugin with API Extension

```typescript
// Plugin implementation
export class DataProcessorPlugin implements Plugin, APIExtension {
  id = 'dataProcessor'
  name = 'Data Processor'
  capabilities = [PluginCapability.API_EXTENSION]
  
  getNamespace(): string {
    return `plugin_${this.id}`
  }
  
  registerEndpoints(apiRegistry: APIRegistry): PluginAPINamespace {
    const namespace: PluginAPINamespace = {
      namespace: this.getNamespace(),
      endpoints: new Map(),
      middleware: []
    }
    
    // Register plugin-specific endpoints
    namespace.endpoints.set('processData', {
      handler: this.processData.bind(this),
      description: 'Process data with custom algorithm',
      validationSchema: {
        data: { type: 'object' },
        options: { type: 'object', allowEmpty: true }
      }
    })
    
    return namespace
  }
  
  private async processData(data: any, options: any = {}): Promise<any> {
    // Custom data processing logic
    return { processed: true, result: data }
  }
}
```

### 3. Frontend Plugin API Usage

```typescript
// Using plugin APIs from frontend
import { apiClient } from '../api/UnifiedAPIClient'

// Direct plugin API call
const result = await apiClient.callPluginAPI('dataProcessor', 'processData', data, options)

// Using plugin discovery
const endpoints = await apiClient.getPluginAPIEndpoints('dataProcessor')
console.log('Available endpoints:', endpoints.data)

// Batch plugin operations
const results = await apiClient.batchCall([
  { category: 'plugin_dataProcessor', endpoint: 'processData', args: [data1] },
  { category: 'plugin_dataProcessor', endpoint: 'processData', args: [data2] }
])
```

### 4. Custom Middleware Implementation

```typescript
// Custom middleware for specific requirements
export class CustomAuthMiddleware {
  async execute(context: MiddlewareContext): Promise<void> {
    const { event, category, endpoint, args } = context
    
    // Custom authentication logic
    if (category === 'admin' && !this.isAdmin(event.sender)) {
      throw new StructuredAPIError(
        ErrorCode.FORBIDDEN,
        'Admin access required',
        { category, endpoint }
      )
    }
    
    // Add user context to metadata
    context.metadata.userId = this.getUserId(event.sender)
  }
  
  private isAdmin(sender: any): boolean {
    // Implementation specific logic
    return false
  }
  
  private getUserId(sender: any): string {
    // Implementation specific logic
    return 'anonymous'
  }
}
```

## Development Guidelines

### 1. API Endpoint Registration
- Always register endpoints in `registerCoreAPIs()` method
- Use descriptive endpoint names and categories
- Include validation schemas for all endpoints with parameters
- Set appropriate permissions and rate limits
- Provide clear descriptions for documentation

### 2. Error Handling
- Use `StructuredAPIError` for all custom errors
- Choose appropriate error codes from `ErrorCode` enum
- Include relevant context and details in errors
- Set proper severity levels for different error types

### 3. Plugin Development
- Implement `APIExtension` interface for plugins with API endpoints
- Use the plugin namespace pattern (`plugin_${pluginId}`)
- Provide comprehensive validation for plugin endpoints
- Include middleware for plugin-specific security requirements

### 4. Frontend Integration
- Use `UnifiedAPIClient` instead of direct `electronAPI` calls
- Leverage React hooks for component integration
- Handle loading states and errors appropriately
- Use batch operations for multiple related API calls

### 5. Testing
- Test all API endpoints with various input scenarios
- Verify middleware execution order and behavior
- Test plugin API registration and discovery
- Validate error handling and structured responses

### 6. Performance Considerations
- Monitor API performance using built-in metrics
- Set appropriate rate limits for endpoints
- Use batch operations for multiple related calls
- Implement caching where appropriate

### 7. Security Best Practices
- Always validate input parameters
- Use permission-based access control
- Implement rate limiting for public endpoints
- Sanitize all user inputs
- Log security-related events

This internal documentation provides comprehensive guidance for developers working on the ChatLo unified IPC system. The system is designed to be extensible, secure, and maintainable while providing excellent developer experience.
