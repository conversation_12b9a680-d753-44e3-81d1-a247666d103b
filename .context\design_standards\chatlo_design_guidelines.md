# ChatLo Design Guidelines v1

## Overview

ChatLo follows a modern, professional design system inspired by VSCode and contemporary chat applications. The design emphasizes clarity, accessibility, and a cohesive user experience across all components.

## Design Principles

### 1. **Consistency**
- All components follow the same visual language
- Consistent spacing, typography, and color usage
- Unified interaction patterns across the application

### 2. **Clarity**
- Clear visual hierarchy with proper contrast ratios
- Readable typography with Inter font family
- Intuitive iconography and visual cues

### 3. **Accessibility**
- WCAG 2.1 AA compliant color contrasts
- Keyboard navigation support
- Screen reader friendly markup

### 4. **Modern Aesthetics**
- Clean, minimalist interface
- Subtle animations and transitions
- Contemporary color palette with professional feel

## Color System

### Primary Palette

| Color | Hex | Usage |
|-------|-----|-------|
| **Primary** | `#8AB0BB` | Main brand color, primary actions, active states |
| **Secondary** | `#FF8383` | Accent elements, hearts, notifications |
| **Tertiary** | `#1B3E68` | Deep accents, borders, secondary actions |
| **Supplement1** | `#D5D8E0` | Primary text, light borders |
| **Supplement2** | `#89AFBA` | Secondary text, muted elements |

### Extended Palette

#### ChatLo Teal (`chatlo-teal`)
- **50**: `#f0f9fa` - Very light backgrounds
- **100**: `#daf0f3` - Light backgrounds
- **200**: `#b8e1e7` - Subtle highlights
- **300**: `#8AB0BB` - Primary brand color
- **400**: `#6b9aa8` - Hover states
- **500**: `#4f7c8a` - Active states
- **600**: `#3d5f6b` - Pressed states
- **700**: `#2d464f` - Dark accents
- **800**: `#1e2f35` - Very dark accents
- **900**: `#0f171a` - Darkest shade

#### ChatLo Coral (`chatlo-coral`)
- **50**: `#fff5f5` - Very light backgrounds
- **100**: `#ffe3e3` - Light backgrounds
- **200**: `#ffc9c9` - Subtle highlights
- **300**: `#FF8383` - Secondary brand color
- **400**: `#ff5555` - Hover states
- **500**: `#e53e3e` - Active states
- **600**: `#c53030` - Pressed states
- **700**: `#9c2626` - Dark accents
- **800**: `#742a2a` - Very dark accents
- **900**: `#4a1414` - Darkest shade

#### ChatLo Navy (`chatlo-navy`)
- **50**: `#f7f8fa` - Very light backgrounds
- **100**: `#eef1f5` - Light backgrounds
- **200**: `#dde3eb` - Subtle highlights
- **300**: `#c4cdd9` - Light text
- **400**: `#a6b3c4` - Medium text
- **500**: `#8694a8` - Regular text
- **600**: `#6b7a8f` - Dark text
- **700**: `#556275` - Darker text
- **800**: `#3d4a5c` - Very dark text
- **900**: `#1B3E68` - Tertiary brand color

### Gray Scale
- **Gray 900**: `#111827` - Main background
- **Gray 800**: `#1f2937` - Card backgrounds
- **Gray 700**: `#374151` - Hover states
- **Gray 600**: `#4b5563` - Borders
- **Gray 500**: `#6b7280` - Disabled text
- **Gray 400**: `#9ca3af` - Placeholder text

## Typography

### Font Family
- **Primary**: Inter (Google Fonts)
- **Fallback**: system-ui, sans-serif

### Type Scale

| Style | Size | Weight | Usage |
|-------|------|--------|-------|
| **H1** | `text-4xl` (36px) | `font-bold` (700) | Page titles |
| **H2** | `text-3xl` (30px) | `font-semibold` (600) | Section headers |
| **H3** | `text-2xl` (24px) | `font-medium` (500) | Subsection headers |
| **H4** | `text-xl` (20px) | `font-medium` (500) | Card titles |
| **Body** | `text-base` (16px) | `font-normal` (400) | Regular text |
| **Small** | `text-sm` (14px) | `font-normal` (400) | Secondary text |
| **Caption** | `text-xs` (12px) | `font-normal` (400) | Captions, labels |

### Text Colors
- **Primary Text**: `text-supplement1` - Main content
- **Secondary Text**: `text-gray-400` - Supporting content
- **Muted Text**: `text-gray-500` - Disabled/placeholder text
- **Brand Text**: `text-primary` - Brand elements
- **Accent Text**: `text-secondary` - Highlights

## Component System

### Naming Convention
All components use the `u1-` prefix to indicate ChatLo Design System v1:

```css
.u1-button-primary    /* Primary button */
.u1-input-field       /* Standard input */
.u1-card              /* Container card */
.u1-nav-icon          /* Navigation icon */
.u1-badge-primary     /* Primary badge */
```

### Component Categories

#### 1. Buttons (`u1-button-*`)
- `u1-button-primary` - Main actions
- `u1-button-secondary` - Secondary actions
- `u1-button-outline` - Outlined buttons
- `u1-button-icon` - Icon-only buttons
- `u1-button-ghost` - Minimal buttons
- `u1-button-nav` - Navigation buttons

#### 2. Inputs (`u1-input-*`)
- `u1-input-field` - Standard text input
- `u1-input-search` - Search input with icon
- `u1-textarea` - Multi-line text input
- `u1-chat-input` - Chat message input

#### 3. Cards (`u1-card-*`)
- `u1-card` - Basic container
- `u1-chat-bubble-user` - User message bubble
- `u1-chat-bubble-assistant` - AI message bubble
- `u1-sidebar-item` - Sidebar list item
- `u1-status-card` - Status display card
- `u1-artifact-card` - Code/content artifact

#### 4. Navigation (`u1-nav-*`)
- `u1-nav-iconbar` - VSCode-style icon bar
- `u1-nav-icon` - Individual nav icon
- `u1-nav-tabs` - Tab container
- `u1-tab-active` - Active tab
- `u1-tab-inactive` - Inactive tab

#### 5. Badges (`u1-badge-*`)
- `u1-badge-primary` - Primary status
- `u1-badge-secondary` - Secondary status
- `u1-badge-success` - Success state
- `u1-badge-warning` - Warning state
- `u1-badge-error` - Error state

## Layout System

### Spacing Scale
- **xs**: `0.25rem` (4px)
- **sm**: `0.5rem` (8px)
- **md**: `1rem` (16px)
- **lg**: `1.5rem` (24px)
- **xl**: `2rem` (32px)
- **2xl**: `3rem` (48px)

### Border Radius
- **sm**: `0.25rem` (4px) - Small elements
- **md**: `0.5rem` (8px) - Standard elements
- **lg**: `0.75rem` (12px) - Cards, containers
- **xl**: `1rem` (16px) - Large containers
- **2xl**: `1.5rem` (24px) - Chat bubbles
- **full**: `9999px` - Pills, badges

### Shadows
- **sm**: `0 1px 2px 0 rgb(0 0 0 / 0.05)`
- **md**: `0 4px 6px -1px rgb(0 0 0 / 0.1)`
- **lg**: `0 10px 15px -3px rgb(0 0 0 / 0.1)`
- **xl**: `0 20px 25px -5px rgb(0 0 0 / 0.1)`

## Animation & Transitions

### Standard Transitions
- **Duration**: `150ms` for micro-interactions
- **Duration**: `300ms` for component state changes
- **Easing**: `cubic-bezier(0.4, 0, 0.2, 1)` - Standard ease

### Custom Animations
- `animate-fade-in-up` - Chat message appearance
- `animate-pulse-slow` - Loading states
- `transition-colors` - Color changes on hover/focus

### Usage Guidelines
- Use transitions for hover states: `transition-colors`
- Animate chat bubbles with: `animate-fade-in-up`
- Loading indicators use: `animate-pulse-slow`
- Keep animations subtle and purposeful

## Accessibility Guidelines

### Color Contrast
- Text on background: Minimum 4.5:1 ratio
- Large text: Minimum 3:1 ratio
- Interactive elements: Minimum 3:1 ratio

### Focus States
- All interactive elements must have visible focus indicators
- Use `focus:ring-2 focus:ring-primary` for form elements
- Use `focus:bg-gray-700` for buttons and navigation

### Keyboard Navigation
- Tab order follows logical flow
- All interactive elements are keyboard accessible
- Escape key closes modals and dropdowns

## Usage Examples

### Creating a Survey with u1-button
```html
<button class="u1-button-primary">
  <i class="fa-solid fa-poll"></i>
  Create Survey
</button>
```

### Chat Input Component
```html
<div class="u1-chat-input">
  <button class="u1-button-ghost">
    <i class="fa-solid fa-paperclip"></i>
  </button>
  <textarea class="u1-textarea" placeholder="Type your message..."></textarea>
  <button class="u1-button-icon">
    <i class="fa-solid fa-paper-plane"></i>
  </button>
</div>
```

### Status Badge
```html
<div class="u1-badge-success">
  <i class="fa-solid fa-check"></i>
  Online
</div>
```

## Implementation Notes

1. **Backward Compatibility**: Legacy classes are maintained alongside new u1- classes
2. **Gradual Migration**: Components can be updated incrementally to use new design system
3. **Customization**: Colors and spacing can be customized via Tailwind config
4. **Documentation**: All components are documented in `design_system_v1.html`

## Future Considerations

- Dark/Light theme toggle support
- Additional component variants
- Animation library integration
- Component composition patterns
- Performance optimizations
