import React, { useRef, useEffect } from 'react'
import { FileText, Image } from './Icons'

interface AttachmentMenuProps {
  isOpen: boolean
  onClose: () => void
  onFileSelect: () => void
  onImageSelect: () => void
  anchorRef: React.RefObject<HTMLElement | HTMLButtonElement>
}

const AttachmentMenu: React.FC<AttachmentMenuProps> = ({
  isOpen,
  onClose,
  onFileSelect,
  onImageSelect,
  anchorRef: _anchorRef
}) => {
  const menuRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleEscape)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleEscape)
    }
  }, [isOpen, onClose])

  if (!isOpen) return null

  return (
    <>
      {/* Backdrop */}
      <div className="fixed inset-0 z-40" onClick={onClose} />
      
      {/* Menu */}
      <div
        ref={menuRef}
        className="fixed bottom-20 left-4 z-50 bg-neutral-800 border border-neutral-700 rounded-lg shadow-lg backdrop-blur-lg min-w-[160px]"
      >
        <div className="p-2">
          {/* Files option */}
          <button
            onClick={() => {
              console.log('Files option clicked in AttachmentMenu')
              onFileSelect()
              onClose()
            }}
            className="w-full flex items-center gap-3 px-3 py-2 text-sm text-neutral-200 hover:bg-neutral-700 rounded-md transition-colors"
          >
            <FileText className="h-4 w-4 text-neutral-400" />
            <span>Files</span>
          </button>

          {/* Images option */}
          <button
            onClick={() => {
              console.log('Images option clicked in AttachmentMenu')
              onImageSelect()
              onClose()
            }}
            className="w-full flex items-center gap-3 px-3 py-2 text-sm text-neutral-200 hover:bg-neutral-700 rounded-md transition-colors"
          >
            <Image className="h-4 w-4 text-neutral-400" />
            <span>Images</span>
          </button>
        </div>
      </div>
    </>
  )
}

export default AttachmentMenu
