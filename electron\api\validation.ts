/**
 * API Validation and Security Layer
 * Provides comprehensive validation, permission checking, and security controls
 */

export interface ValidationRule {
  type: 'required' | 'string' | 'number' | 'boolean' | 'array' | 'object' | 'email' | 'path' | 'custom'
  message?: string
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (value: any) => boolean | string
  allowEmpty?: boolean
}

export interface ValidationSchema {
  [key: string]: ValidationRule | ValidationRule[]
}

export interface ValidationResult {
  valid: boolean
  errors: Array<{
    field: string
    message: string
    value?: any
  }>
}

export interface Permission {
  id: string
  name: string
  description: string
  category: string
  level: 'read' | 'write' | 'admin' | 'system'
}

export interface SecurityContext {
  userId?: string
  sessionId?: string
  permissions: string[]
  ipAddress?: string
  userAgent?: string
  timestamp: number
}

export class APIValidator {
  private static readonly COMMON_PATTERNS = {
    email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    path: /^[a-zA-Z]:[\\\/]|^[\\\/]|^\.{1,2}[\\\/]|^[a-zA-Z0-9._-]+$/,
    filename: /^[^<>:"/\\|?*\x00-\x1f]+$/,
    uuid: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  }

  /**
   * Validate arguments against a schema
   */
  static validate(args: any[], schema: ValidationSchema): ValidationResult {
    const errors: ValidationResult['errors'] = []

    // Convert args array to object for easier validation
    const argsObj: Record<string, any> = {}
    const schemaKeys = Object.keys(schema)
    
    schemaKeys.forEach((key, index) => {
      argsObj[key] = args[index]
    })

    for (const [field, rules] of Object.entries(schema)) {
      const value = argsObj[field]
      const ruleArray = Array.isArray(rules) ? rules : [rules]

      for (const rule of ruleArray) {
        const error = this.validateField(field, value, rule)
        if (error) {
          errors.push(error)
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  private static validateField(field: string, value: any, rule: ValidationRule): ValidationResult['errors'][0] | null {
    // Handle required validation
    if (rule.type === 'required' && (value === undefined || value === null)) {
      return {
        field,
        message: rule.message || `${field} is required`,
        value
      }
    }

    // Skip validation if value is empty and allowEmpty is true
    if (rule.allowEmpty && (value === undefined || value === null || value === '')) {
      return null
    }

    // Skip validation if value is undefined/null and not required
    if (rule.type !== 'required' && (value === undefined || value === null)) {
      return null
    }

    switch (rule.type) {
      case 'string':
        if (typeof value !== 'string') {
          return {
            field,
            message: rule.message || `${field} must be a string`,
            value
          }
        }
        if (rule.min !== undefined && value.length < rule.min) {
          return {
            field,
            message: rule.message || `${field} must be at least ${rule.min} characters`,
            value
          }
        }
        if (rule.max !== undefined && value.length > rule.max) {
          return {
            field,
            message: rule.message || `${field} must be at most ${rule.max} characters`,
            value
          }
        }
        if (rule.pattern && !rule.pattern.test(value)) {
          return {
            field,
            message: rule.message || `${field} format is invalid`,
            value
          }
        }
        break

      case 'number':
        if (typeof value !== 'number' || isNaN(value)) {
          return {
            field,
            message: rule.message || `${field} must be a number`,
            value
          }
        }
        if (rule.min !== undefined && value < rule.min) {
          return {
            field,
            message: rule.message || `${field} must be at least ${rule.min}`,
            value
          }
        }
        if (rule.max !== undefined && value > rule.max) {
          return {
            field,
            message: rule.message || `${field} must be at most ${rule.max}`,
            value
          }
        }
        break

      case 'boolean':
        if (typeof value !== 'boolean') {
          return {
            field,
            message: rule.message || `${field} must be a boolean`,
            value
          }
        }
        break

      case 'array':
        if (!Array.isArray(value)) {
          return {
            field,
            message: rule.message || `${field} must be an array`,
            value
          }
        }
        if (rule.min !== undefined && value.length < rule.min) {
          return {
            field,
            message: rule.message || `${field} must have at least ${rule.min} items`,
            value
          }
        }
        if (rule.max !== undefined && value.length > rule.max) {
          return {
            field,
            message: rule.message || `${field} must have at most ${rule.max} items`,
            value
          }
        }
        break

      case 'object':
        if (typeof value !== 'object' || Array.isArray(value)) {
          return {
            field,
            message: rule.message || `${field} must be an object`,
            value
          }
        }
        break

      case 'email':
        if (typeof value !== 'string' || !this.COMMON_PATTERNS.email.test(value)) {
          return {
            field,
            message: rule.message || `${field} must be a valid email address`,
            value
          }
        }
        break

      case 'path':
        if (typeof value !== 'string' || !this.COMMON_PATTERNS.path.test(value)) {
          return {
            field,
            message: rule.message || `${field} must be a valid file path`,
            value
          }
        }
        break

      case 'custom':
        if (rule.validator) {
          const result = rule.validator(value)
          if (result !== true) {
            return {
              field,
              message: typeof result === 'string' ? result : (rule.message || `${field} is invalid`),
              value
            }
          }
        }
        break
    }

    return null
  }

  /**
   * Create common validation schemas
   */
  static createFilePathSchema(): ValidationSchema {
    return {
      filePath: { type: 'path', message: 'Invalid file path' }
    }
  }

  static createPluginIdSchema(): ValidationSchema {
    return {
      pluginId: { 
        type: 'string', 
        min: 1, 
        max: 100,
        pattern: /^[a-zA-Z0-9_-]+$/,
        message: 'Plugin ID must be alphanumeric with dashes and underscores only'
      }
    }
  }

  static createDatabaseQuerySchema(): ValidationSchema {
    return {
      query: { type: 'string', min: 1, message: 'Query cannot be empty' },
      params: { type: 'array', allowEmpty: true }
    }
  }
}

export class SecurityManager {
  private permissions: Map<string, Permission> = new Map()
  private userPermissions: Map<string, Set<string>> = new Map()
  private rateLimits: Map<string, { count: number; resetTime: number }> = new Map()

  constructor() {
    this.initializeDefaultPermissions()
  }

  private initializeDefaultPermissions(): void {
    const defaultPermissions: Permission[] = [
      { id: 'db.read', name: 'Database Read', description: 'Read from database', category: 'database', level: 'read' },
      { id: 'db.write', name: 'Database Write', description: 'Write to database', category: 'database', level: 'write' },
      { id: 'file.read', name: 'File Read', description: 'Read files', category: 'filesystem', level: 'read' },
      { id: 'file.write', name: 'File Write', description: 'Write files', category: 'filesystem', level: 'write' },
      { id: 'vault.read', name: 'Vault Read', description: 'Read vault data', category: 'vault', level: 'read' },
      { id: 'vault.write', name: 'Vault Write', description: 'Write vault data', category: 'vault', level: 'write' },
      { id: 'plugin.manage', name: 'Plugin Management', description: 'Manage plugins', category: 'plugins', level: 'admin' },
      { id: 'system.admin', name: 'System Admin', description: 'System administration', category: 'system', level: 'system' }
    ]

    defaultPermissions.forEach(permission => {
      this.permissions.set(permission.id, permission)
    })
  }

  /**
   * Check if user has required permission
   */
  hasPermission(context: SecurityContext, permissionId: string): boolean {
    return context.permissions.includes(permissionId) || context.permissions.includes('system.admin')
  }

  /**
   * Get required permission for API endpoint
   */
  getRequiredPermission(category: string, endpoint: string): string | null {
    const writeOperations = ['create', 'update', 'delete', 'write', 'save', 'remove', 'set']
    const adminOperations = ['manage', 'admin', 'configure', 'reset', 'cleanup']

    const isWrite = writeOperations.some(op => endpoint.toLowerCase().includes(op))
    const isAdmin = adminOperations.some(op => endpoint.toLowerCase().includes(op))

    if (isAdmin) {
      return `${category}.admin`
    } else if (isWrite) {
      return `${category}.write`
    } else {
      return `${category}.read`
    }
  }

  /**
   * Validate security context
   */
  validateSecurityContext(context: SecurityContext): { valid: boolean; error?: string } {
    if (!context.permissions || !Array.isArray(context.permissions)) {
      return { valid: false, error: 'Invalid permissions in security context' }
    }

    if (context.timestamp && (Date.now() - context.timestamp) > 300000) { // 5 minutes
      return { valid: false, error: 'Security context has expired' }
    }

    return { valid: true }
  }

  /**
   * Create default security context for development
   */
  createDefaultContext(): SecurityContext {
    return {
      permissions: ['system.admin'], // Grant all permissions for development
      timestamp: Date.now()
    }
  }

  /**
   * Sanitize input to prevent injection attacks
   */
  sanitizeInput(input: any): any {
    if (typeof input === 'string') {
      // Remove potentially dangerous characters
      return input.replace(/[<>'"&]/g, '')
    }
    
    if (Array.isArray(input)) {
      return input.map(item => this.sanitizeInput(item))
    }
    
    if (typeof input === 'object' && input !== null) {
      const sanitized: any = {}
      for (const [key, value] of Object.entries(input)) {
        sanitized[key] = this.sanitizeInput(value)
      }
      return sanitized
    }
    
    return input
  }

  /**
   * Check rate limits
   */
  checkRateLimit(identifier: string, maxRequests: number, windowMs: number): boolean {
    const now = Date.now()
    let limit = this.rateLimits.get(identifier)

    if (!limit || now > limit.resetTime) {
      limit = { count: 0, resetTime: now + windowMs }
    }

    limit.count++
    this.rateLimits.set(identifier, limit)

    return limit.count <= maxRequests
  }
}
