import React, { useState } from 'react'
import { intelligenceService } from '../services/intelligenceService'
import { performanceMonitor } from '../services/performanceMonitor'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faBrain, faPlay, faChartLine, faSpinner, faCheckCircle, faExclamationTriangle, faCog, faStar, faRefresh } from '@fortawesome/free-solid-svg-icons'
import { useAppStore } from '../store'

/**
 * Test panel for intelligence extraction functionality
 * Used for development and debugging purposes
 */
export const IntelligenceTestPanel: React.FC = () => {
  const { models, checkLocalModels, ollamaConnected, lmStudioConnected, localModels } = useAppStore()
  const [testContent, setTestContent] = useState('China oil industry research - analyzing market trends, key players like CNPC and Sinopec, production capacity, import dependencies, and investment opportunities in the petroleum sector.')
  const [extractionResult, setExtractionResult] = useState<any>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [performanceStats, setPerformanceStats] = useState<any>(null)
  const [processingStage, setProcessingStage] = useState<string>('')
  const [notifications, setNotifications] = useState<Array<{id: string, type: 'success' | 'error' | 'info', message: string}>>([])
  const [processingProgress, setProcessingProgress] = useState(0)
  const [selectedTestModel, setSelectedTestModel] = useState<string>('')

  // Testing Framework State
  const [testSerialNumber, setTestSerialNumber] = useState(1)
  const [userRating, setUserRating] = useState<number>(0)
  const [testLogs, setTestLogs] = useState<Array<{
    serial: number
    timestamp: string
    content: string
    model: string
    topics: Array<{name: string, relevance: number}>
    entities: Array<{text: string, type: string, confidence: number}>
    hybridInfo: any
    userRating: number
    notes?: string
  }>>([])

  // Combine models for display (with proper typing)
  const allAvailableModels = [
    ...models.map(m => ({ ...m, isLocal: false })),
    ...localModels.map(m => ({ ...m, isLocal: true, context_length: 4096, pricing: null, top_provider: null }))
  ]

  // Debug: Log available models
  console.log('Available models:', models)
  console.log('Local models:', localModels)
  console.log('Combined models:', allAvailableModels)
  console.log('Connection status:', { ollamaConnected, lmStudioConnected, localModelsCount: localModels.length })

  const addNotification = (type: 'success' | 'error' | 'info', message: string) => {
    const id = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}` // Unique ID
    setNotifications(prev => [...prev, { id, type, message }])

    // Auto-remove notification after 5 seconds
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== id))
    }, 5000)
  }

  // Temporary direct LLM call function for testing
  const testDirectLLMCall = async (content: string, modelId: string, _modelInfo: any) => {
    const provider = modelId.split(':')[0]
    const modelName = modelId.split(':')[1]

    const prompt = `Analyze the following content and extract structured intelligence:

CONTENT:
${content}

Please provide a JSON response with entities and topics:
{
  "entities": [{"name": "entity", "type": "person|place|concept|technology|organization|other", "confidence": 0.8}],
  "topics": [{"name": "topic", "relevance": 0.9, "category": "business|technology|research|finance|energy|healthcare|general"}],
  "summary": "brief analysis"
}`

    if (provider === 'ollama') {
      const response = await fetch('http://localhost:11434/api/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json; charset=utf-8' },
        body: JSON.stringify({
          model: modelName,
          prompt: prompt,
          stream: false
        })
      })

      if (!response.ok) {
        throw new Error(`Ollama API error: ${response.status}`)
      }

      const data = await response.json()
      return { data: { rawResponse: data.response || '', entities: [], topics: [] } }
    } else if (provider === 'lmstudio') {
      const response = await fetch('http://localhost:1234/v1/chat/completions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json; charset=utf-8' },
        body: JSON.stringify({
          model: modelName,
          messages: [{ role: 'user', content: prompt }],
          temperature: 0.3,
          max_tokens: 2000
        })
      })

      if (!response.ok) {
        throw new Error(`LM Studio API error: ${response.status}`)
      }

      const data = await response.json()
      return { data: { rawResponse: data.choices?.[0]?.message?.content || '', entities: [], topics: [] } }
    }

    throw new Error(`Unsupported provider: ${provider}`)
  }

  const logTestResult = (result: any, modelInfo: any) => {
    const testLog = {
      serial: testSerialNumber,
      timestamp: new Date().toISOString(),
      content: testContent,
      model: modelInfo ? `${modelInfo.name} (${selectedTestModel?.split(':')[0] || 'unknown'})` : 'Unknown',
      topics: result.data.topics || [],
      entities: result.data.entities || [],
      hybridInfo: result.hybridInfo || {},
      userRating: 0, // Will be updated when user rates
      notes: ''
    }

    setTestLogs(prev => [...prev, testLog])
    console.log(`Test #${testSerialNumber} logged:`, testLog)
    setTestSerialNumber(prev => prev + 1)

    return testLog
  }

  const handleRating = (rating: number) => {
    setUserRating(rating)

    // Update the latest test log with rating
    if (testLogs.length > 0) {
      const latestTest = testLogs[testLogs.length - 1]
      const updatedLogs = [...testLogs]
      updatedLogs[updatedLogs.length - 1] = { ...latestTest, userRating: rating }
      setTestLogs(updatedLogs)

      addNotification('success', `Test #${latestTest.serial} rated ${rating} stars`)
      console.log(`Test #${latestTest.serial} rated ${rating} stars:`, updatedLogs[updatedLogs.length - 1])
    }
  }

  const renderStarRating = () => {
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map(star => (
          <button
            key={star}
            onClick={() => handleRating(star)}
            className={`text-lg transition-colors ${
              star <= userRating ? 'text-yellow-400' : 'text-gray-600 hover:text-yellow-300'
            }`}
          >
            <FontAwesomeIcon icon={faStar} />
          </button>
        ))}
        <span className="text-sm text-gray-400 ml-2">
          {userRating > 0 ? `${userRating}/5 stars` : 'Rate this test'}
        </span>
      </div>
    )
  }

  const handleRefreshModels = async () => {
    addNotification('info', 'Refreshing local models...')
    await checkLocalModels()
    addNotification('success', `Models refreshed. Found ${allAvailableModels.length} total models`)
  }

  const handleTestExtraction = async () => {
    if (!selectedTestModel) {
      addNotification('error', 'Please select a model for testing')
      return
    }

    setIsProcessing(true)
    setExtractionResult(null)
    setProcessingProgress(0)
    setNotifications([])

    try {
      // Stage 1: Initialize
      setProcessingStage('Initializing intelligence extraction...')
      setProcessingProgress(10)
      addNotification('info', 'Starting intelligence extraction test')

      await new Promise(resolve => setTimeout(resolve, 200)) // Brief pause for UI feedback

      // Stage 2: Content Analysis
      setProcessingStage('Analyzing content and extracting entities...')
      setProcessingProgress(30)

      // Add model information to processing
      const modelInfo = allAvailableModels.find(m => m.id === selectedTestModel)
      const provider = selectedTestModel?.split(':')[0] || 'unknown'
      if (selectedTestModel && modelInfo) {
        addNotification('info', `Using model: ${modelInfo.name} (${provider})`)
      }

      const startTime = Date.now()

      // TEMPORARY: Direct LLM call for testing
      const result = await testDirectLLMCall(testContent, selectedTestModel, modelInfo!)

      const processingTime = Date.now() - startTime

      // Stage 3: Performance Analysis
      setProcessingStage('Gathering performance metrics...')
      setProcessingProgress(70)

      const stats = performanceMonitor.getPerformanceSummary()
      setPerformanceStats(stats)

      // Stage 4: Complete
      setProcessingStage('Processing complete!')
      setProcessingProgress(100)

      // Add model performance data to result
      const enhancedResult = {
        ...result,
        modelInfo: modelInfo ? {
          id: modelInfo.id,
          name: modelInfo.name,
          provider: selectedTestModel?.split(':')[0] || 'unknown',
          processingTime
        } : null
      }

      setExtractionResult(enhancedResult)

      // Log test result for analysis
      const testLog = logTestResult(result, modelInfo)

      addNotification('success', `Test #${testLog.serial}: Intelligence extraction completed in ${processingTime}ms`)
      addNotification('info', `Found ${result.data.entities.length} entities and ${result.data.topics.length} topics`)

      if (modelInfo) {
        addNotification('info', `Model: ${modelInfo.name} (${provider}) - ${processingTime}ms`)
      }

    } catch (error) {
      console.error('Test extraction failed:', error)
      setExtractionResult({ error: error instanceof Error ? error.message : 'Unknown error' })
      addNotification('error', `Extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
      setProcessingStage('Processing failed')
    } finally {
      setIsProcessing(false)
      setTimeout(() => {
        setProcessingStage('')
        setProcessingProgress(0)
      }, 2000)
    }
  }

  const handleGetPerformanceStats = () => {
    addNotification('info', 'Gathering performance statistics...')

    try {
      const stats = performanceMonitor.getPerformanceSummary()
      const currentCPU = performanceMonitor.getCurrentCPUUsage()
      const currentMemory = performanceMonitor.getCurrentMemoryUsage()
      const isStressed = performanceMonitor.isSystemUnderStress()
      const mode = performanceMonitor.getRecommendedProcessingMode()

      setPerformanceStats({
        ...stats,
        currentCPU,
        currentMemory: Math.round(currentMemory / 1024 / 1024), // Convert to MB
        isStressed,
        recommendedMode: mode
      })

      addNotification('success', 'Performance statistics updated')
    } catch (error) {
      console.error('Performance stats failed:', error)
      addNotification('error', 'Failed to gather performance statistics')
    }
  }

  const handleTestVaultSuggestion = async () => {
    if (!extractionResult?.data) {
      addNotification('error', 'No extraction data available. Run extraction test first.')
      return
    }

    addNotification('info', 'Testing vault suggestion algorithm...')

    try {
      // Import the intelligence service to test vault suggestions
      const suggestedVaultName = intelligenceService.suggestVaultName(extractionResult.data)

      addNotification('success', `Suggested vault name: "${suggestedVaultName}"`)

      // Test vault suggestion logic
      const entities = extractionResult.data.entities
      const topics = extractionResult.data.topics

      if (entities.length > 0 || topics.length > 0) {
        addNotification('info', `Suggestion based on ${entities.length} entities and ${topics.length} topics`)
      } else {
        addNotification('info', 'Fallback suggestion used due to limited extraction data')
      }

    } catch (error) {
      console.error('Vault suggestion test failed:', error)
      addNotification('error', `Vault suggestion failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  return (
    <div className="max-h-[85vh] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800">
      <div className="p-6 bg-gray-800 rounded-lg border border-tertiary/50 space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <FontAwesomeIcon icon={faBrain} className="text-primary text-lg" />
            <h3 className="text-lg font-semibold text-white">Intelligence System Test Panel</h3>
            <span className="text-sm bg-primary/20 text-primary px-2 py-1 rounded">
              Test #{testSerialNumber}
            </span>
            {isProcessing && (
              <FontAwesomeIcon icon={faSpinner} className="text-primary text-sm animate-spin" />
            )}
          </div>

          {/* Model Selector */}
          <div className="flex items-center gap-2">
            <FontAwesomeIcon icon={faCog} className="text-gray-400 text-sm" />
            <button
              onClick={handleRefreshModels}
              disabled={isProcessing}
              className="p-1 text-gray-400 hover:text-primary transition-colors disabled:opacity-50"
              title="Refresh local models"
            >
              <FontAwesomeIcon icon={faRefresh} className="text-sm" />
            </button>
            <select
              value={selectedTestModel}
              onChange={(e) => {
                setSelectedTestModel(e.target.value)
                console.log('Selected model:', e.target.value)
                const model = allAvailableModels.find(m => m.id === e.target.value)
                console.log('Model details:', model)
              }}
              disabled={isProcessing}
              className="bg-gray-700 border border-tertiary/50 rounded px-3 py-1 text-white text-sm focus:outline-none focus:ring-2 focus:ring-primary disabled:opacity-50"
            >
              <option value="">Select Model for Testing ({allAvailableModels.length} available)</option>
              {/* Local Models First */}
              {localModels.map((model) => (
                <option key={model.id} value={model.id}>
                  🏠 {model.name} ({model.provider === 'ollama' ? 'Ollama' : 'LM Studio'})
                </option>
              ))}
              {/* External Models */}
              {models.map((model) => (
                <option key={model.id} value={model.id}>
                  ☁️ {model.name} (External)
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Local Model Status */}
        <div className="flex items-center gap-4 text-xs">
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${ollamaConnected ? 'bg-green-400' : 'bg-red-400'}`}></div>
            <span className="text-gray-400">Ollama: {ollamaConnected ? 'Connected' : 'Disconnected'}</span>
          </div>
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${lmStudioConnected ? 'bg-green-400' : 'bg-red-400'}`}></div>
            <span className="text-gray-400">LM Studio: {lmStudioConnected ? 'Connected' : 'Disconnected'}</span>
          </div>
          <span className="text-gray-400">Local Models: {localModels.length}</span>
        </div>

      {/* Notifications */}
      {notifications.length > 0 && (
        <div className="space-y-2">
          {notifications.map(notification => (
            <div
              key={notification.id}
              className={`p-3 rounded-lg border flex items-center gap-2 ${
                notification.type === 'success' ? 'bg-green-900/20 border-green-500/50 text-green-300' :
                notification.type === 'error' ? 'bg-red-900/20 border-red-500/50 text-red-300' :
                'bg-blue-900/20 border-blue-500/50 text-blue-300'
              }`}
            >
              <FontAwesomeIcon
                icon={
                  notification.type === 'success' ? faCheckCircle :
                  notification.type === 'error' ? faExclamationTriangle :
                  faBrain
                }
                className="text-sm"
              />
              <span className="text-sm">{notification.message}</span>
            </div>
          ))}
        </div>
      )}

      {/* Test Content Input */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <label className="block text-sm font-medium text-gray-300">Test Content</label>
          <div className="flex gap-2">
            <button
              onClick={() => setTestContent('China oil industry research - analyzing market trends, key players like CNPC and Sinopec, production capacity, import dependencies, and investment opportunities in the petroleum sector.')}
              className="text-xs px-2 py-1 bg-primary/20 text-primary rounded hover:bg-primary/30 transition-colors"
            >
              Oil Industry
            </button>
            <button
              onClick={() => setTestContent('AI development trends analysis - examining machine learning breakthroughs, LLM model comparisons, technology adoption patterns, and emerging AI capabilities in various industries.')}
              className="text-xs px-2 py-1 bg-primary/20 text-primary rounded hover:bg-primary/30 transition-colors"
            >
              AI Trends
            </button>
            <button
              onClick={() => setTestContent('Market analysis for renewable energy sector - studying solar panel market trends, wind energy investments, green technology adoption, and energy transition policies.')}
              className="text-xs px-2 py-1 bg-primary/20 text-primary rounded hover:bg-primary/30 transition-colors"
            >
              Renewable Energy
            </button>
          </div>
        </div>

        {/* UTF-8 Test Buttons */}
        <div className="flex items-center justify-between">
          <label className="block text-sm font-medium text-yellow-400">UTF-8 International Tests</label>
          <div className="flex gap-2 flex-wrap">
            <button
              onClick={() => setTestContent('中国石油行业研究 - 分析市场趋势，中石油和中石化等主要企业，生产能力，进口依赖性，以及石油行业的投资机会。研究包括原油价格波动，炼油技术发展，能源政策影响，以及可持续发展战略。')}
              className="text-xs px-2 py-1 bg-yellow-600/20 text-yellow-400 rounded hover:bg-yellow-600/30 transition-colors"
              title="Chinese Simplified UTF-8 Test"
            >
              中文简体
            </button>
            <button
              onClick={() => setTestContent('人工智慧技術趨勢 - 機器學習演算法，神經網路，深度學習應用，自然語言處理，電腦視覺，以及各行業中新興的人工智慧技術。包括數據分析，智能自動化，以及數位轉型策略。')}
              className="text-xs px-2 py-1 bg-yellow-600/20 text-yellow-400 rounded hover:bg-yellow-600/30 transition-colors"
              title="Chinese Traditional UTF-8 Test"
            >
              中文繁體
            </button>
            <button
              onClick={() => setTestContent('可再生能源市场分析 - solar power太阳能装置，wind energy風能容量，green technology绿色技术投资，sustainable energy可持续能源政策，carbon reduction碳减排倡议，以及clean energy清洁能源转型策略。Mixed language content testing for UTF-8 support.')}
              className="text-xs px-2 py-1 bg-green-600/20 text-green-400 rounded hover:bg-green-600/30 transition-colors"
              title="Mixed Language UTF-8 Test"
            >
              Mixed 混合
            </button>
          </div>
        </div>
        <textarea
          value={testContent}
          onChange={(e) => setTestContent(e.target.value)}
          className="w-full h-32 p-3 bg-gray-700 border border-tertiary/50 rounded-lg text-white resize-none focus:outline-none focus:ring-2 focus:ring-primary"
          placeholder="Enter content to test intelligence extraction..."
        />
      </div>

      {/* Processing Progress */}
      {isProcessing && (
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-300">{processingStage}</span>
            <span className="text-primary">{processingProgress}%</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div
              className="bg-primary h-2 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${processingProgress}%` }}
            />
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex gap-3">
        <button
          onClick={handleTestExtraction}
          disabled={isProcessing || !testContent.trim() || !selectedTestModel}
          className="flex items-center gap-2 px-4 py-2 bg-primary hover:bg-primary/80 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
          title={!selectedTestModel ? 'Please select a model first' : ''}
        >
          <FontAwesomeIcon
            icon={isProcessing ? faSpinner : faPlay}
            className={`text-sm ${isProcessing ? 'animate-spin' : ''}`}
          />
          {isProcessing ? 'Processing...' : 'Test Extraction'}
        </button>

        <button
          onClick={handleGetPerformanceStats}
          disabled={isProcessing}
          className="flex items-center gap-2 px-4 py-2 bg-secondary hover:bg-secondary/80 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
        >
          <FontAwesomeIcon icon={faChartLine} className="text-sm" />
          Performance Stats
        </button>

        <button
          onClick={handleTestVaultSuggestion}
          disabled={isProcessing || !extractionResult?.data}
          className="flex items-center gap-2 px-4 py-2 bg-tertiary hover:bg-tertiary/80 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
        >
          <FontAwesomeIcon icon={faBrain} className="text-sm" />
          Test Vault Suggestion
        </button>
      </div>

      {/* Extraction Results */}
      {extractionResult && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-md font-medium text-white">Extraction Results</h4>
            <div className="flex items-center gap-4">
              {renderStarRating()}
              <span className="text-xs text-gray-400">
                Test #{testLogs.length > 0 ? testLogs[testLogs.length - 1]?.serial : testSerialNumber - 1}
              </span>
            </div>
          </div>
          
          {extractionResult.error ? (
            <div className="p-3 bg-red-900/50 border border-red-500/50 rounded-lg">
              <p className="text-red-300">Error: {extractionResult.error}</p>
            </div>
          ) : (
            <div className="space-y-3">
              {/* Hybrid Intelligence Info */}
              {extractionResult.hybridInfo && (
                <div className="p-3 bg-purple-900/20 border border-purple-500/50 rounded-lg">
                  <h5 className="text-sm font-medium text-purple-300 mb-2">
                    <FontAwesomeIcon icon={faBrain} className="mr-2" />
                    Hybrid Intelligence Analysis
                  </h5>
                  <div className="grid grid-cols-2 gap-4 text-xs">
                    <div>
                      <span className="text-gray-400">Confidence:</span>
                      <span className={`ml-2 ${
                        extractionResult.hybridInfo.confidence > 0.8 ? 'text-green-400' :
                        extractionResult.hybridInfo.confidence > 0.6 ? 'text-yellow-400' :
                        'text-red-400'
                      }`}>
                        {Math.round(extractionResult.hybridInfo.confidence * 100)}%
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-400">Primary Domain:</span>
                      <span className="text-white ml-2 capitalize">{extractionResult.hybridInfo.primaryDomain.domain.replace('_', ' ')}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Method:</span>
                      <span className="text-white ml-2 capitalize">{extractionResult.hybridInfo.extractionMethod}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Needs Review:</span>
                      <span className={`ml-2 ${extractionResult.hybridInfo.needsUserReview ? 'text-yellow-400' : 'text-green-400'}`}>
                        {extractionResult.hybridInfo.needsUserReview ? 'Yes' : 'No'}
                      </span>
                    </div>
                    {extractionResult.hybridInfo.enhancementUsed && (
                      <div className="col-span-2">
                        <span className="text-gray-400">Enhancement:</span>
                        <span className="text-blue-400 ml-2">LLM Enhanced</span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Model Performance Info */}
              {extractionResult.modelInfo && (
                <div className="p-3 bg-blue-900/20 border border-blue-500/50 rounded-lg">
                  <h5 className="text-sm font-medium text-blue-300 mb-2">
                    <FontAwesomeIcon icon={faCog} className="mr-2" />
                    Model Performance
                  </h5>
                  <div className="grid grid-cols-2 gap-4 text-xs">
                    <div>
                      <span className="text-gray-400">Model:</span>
                      <span className="text-white ml-2">{extractionResult.modelInfo.name}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Provider:</span>
                      <span className="text-white ml-2 capitalize">{extractionResult.modelInfo.provider}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Processing Time:</span>
                      <span className="text-white ml-2">{extractionResult.modelInfo.processingTime}ms</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Performance:</span>
                      <span className={`ml-2 ${
                        extractionResult.modelInfo.processingTime < 50 ? 'text-green-400' :
                        extractionResult.modelInfo.processingTime < 200 ? 'text-yellow-400' :
                        'text-red-400'
                      }`}>
                        {extractionResult.modelInfo.processingTime < 50 ? 'Excellent' :
                         extractionResult.modelInfo.processingTime < 200 ? 'Good' : 'Slow'}
                      </span>
                    </div>
                  </div>
                </div>
              )}

              {/* Quality Indicators */}
              {extractionResult.hybridInfo?.qualityIndicators && (
                <div className="p-3 bg-gray-700/50 rounded-lg">
                  <h5 className="text-sm font-medium text-gray-300 mb-2">
                    <FontAwesomeIcon icon={faChartLine} className="mr-2 text-yellow-400" />
                    Quality Indicators
                  </h5>
                  <div className="grid grid-cols-3 gap-4 text-xs">
                    <div>
                      <span className="text-gray-400">Entity Density:</span>
                      <span className="text-white ml-2">{extractionResult.hybridInfo.qualityIndicators.entityDensity.toFixed(2)}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Topic Relevance:</span>
                      <span className="text-white ml-2">{extractionResult.hybridInfo.qualityIndicators.topicRelevance.toFixed(2)}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Content Coverage:</span>
                      <span className="text-white ml-2">{extractionResult.hybridInfo.qualityIndicators.contentCoverage.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              )}

              {/* Entities */}
              <div className="p-3 bg-gray-700/50 rounded-lg">
                <h5 className="text-sm font-medium text-gray-300 mb-2">
                  <FontAwesomeIcon icon={faBrain} className="mr-2 text-primary" />
                  Entities ({extractionResult.data.entities.length})
                </h5>
                {extractionResult.data.entities.length > 0 ? (
                  <div className="flex flex-wrap gap-2">
                    {extractionResult.data.entities.map((entity: any, index: number) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-primary/20 text-primary text-xs rounded-full border border-primary/30"
                        title={`Type: ${entity.type}, Confidence: ${Math.round(entity.confidence * 100)}%`}
                      >
                        {entity.text} ({entity.type}, {Math.round(entity.confidence * 100)}%)
                      </span>
                    ))}
                  </div>
                ) : (
                  <div className="text-xs text-gray-500 italic">No entities extracted</div>
                )}
              </div>

              {/* Topics */}
              <div className="p-3 bg-gray-700/50 rounded-lg">
                <h5 className="text-sm font-medium text-gray-300 mb-2">
                  <FontAwesomeIcon icon={faChartLine} className="mr-2 text-secondary" />
                  Topics ({extractionResult.data.topics.length})
                </h5>
                {extractionResult.data.topics.length > 0 ? (
                  <div className="flex flex-wrap gap-2">
                    {extractionResult.data.topics.map((topic: any, index: number) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-secondary/20 text-secondary text-xs rounded-full border border-secondary/30"
                        title={`Relevance: ${Math.round(topic.relevance * 100)}%`}
                      >
                        {topic.name} ({Math.round(topic.relevance * 100)}%)
                      </span>
                    ))}
                  </div>
                ) : (
                  <div className="text-xs text-gray-500 italic">No topics identified</div>
                )}
              </div>

              {/* Artifacts */}
              <div className="p-3 bg-gray-700/50 rounded-lg">
                <h5 className="text-sm font-medium text-gray-300 mb-2">Artifacts ({extractionResult.data.artifacts.length})</h5>
                <div className="space-y-1">
                  {extractionResult.data.artifacts.map((artifact: any, index: number) => (
                    <div key={index} className="text-xs text-gray-400">
                      {artifact.type}: {artifact.title}
                    </div>
                  ))}
                </div>
              </div>

              {/* Summary */}
              {extractionResult.data.summary && (
                <div className="p-3 bg-gray-700/50 rounded-lg">
                  <h5 className="text-sm font-medium text-gray-300 mb-2">Summary</h5>
                  <p className="text-xs text-gray-400">{extractionResult.data.summary}</p>
                </div>
              )}

              {/* Metadata */}
              <div className="p-3 bg-gray-700/50 rounded-lg">
                <h5 className="text-sm font-medium text-gray-300 mb-2">Processing Metadata</h5>
                <div className="text-xs text-gray-400 space-y-1">
                  <div>Processing Time: {extractionResult.metadata.extraction_time_ms}ms</div>
                  <div>Model Used: {extractionResult.metadata.model_used}</div>
                  <div>Version: {extractionResult.metadata.processing_version}</div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Performance Stats */}
      {performanceStats && (
        <div className="space-y-2">
          <h4 className="text-md font-medium text-white">Performance Statistics</h4>
          <div className="p-3 bg-gray-700/50 rounded-lg">
            <div className="grid grid-cols-2 gap-4 text-xs">
              <div>
                <span className="text-gray-400">Avg CPU:</span>
                <span className="text-white ml-2">{performanceStats.avgCPU}%</span>
              </div>
              <div>
                <span className="text-gray-400">Avg Memory:</span>
                <span className="text-white ml-2">{Math.round(performanceStats.avgMemory / 1024 / 1024)}MB</span>
              </div>
              <div>
                <span className="text-gray-400">Avg Processing:</span>
                <span className="text-white ml-2">{performanceStats.avgProcessingTime}ms</span>
              </div>
              <div>
                <span className="text-gray-400">Operations:</span>
                <span className="text-white ml-2">{performanceStats.totalOperations}</span>
              </div>
              {performanceStats.currentCPU !== undefined && (
                <>
                  <div>
                    <span className="text-gray-400">Current CPU:</span>
                    <span className="text-white ml-2">{performanceStats.currentCPU}%</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Current Memory:</span>
                    <span className="text-white ml-2">{performanceStats.currentMemory}MB</span>
                  </div>
                  <div>
                    <span className="text-gray-400">System Stressed:</span>
                    <span className={`ml-2 ${performanceStats.isStressed ? 'text-red-400' : 'text-green-400'}`}>
                      {performanceStats.isStressed ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-400">Recommended Mode:</span>
                    <span className="text-white ml-2">{performanceStats.recommendedMode}</span>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Test Logs */}
      {testLogs.length > 0 && (
        <div className="space-y-3">
          <h4 className="text-md font-medium text-white">Test History ({testLogs.length} tests)</h4>
          <div className="max-h-60 overflow-y-auto space-y-2">
            {testLogs.slice(-5).reverse().map((log) => (
              <div key={log.serial} className="p-3 bg-gray-700/30 rounded-lg border border-gray-600/50">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-primary">Test #{log.serial}</span>
                    <span className="text-xs text-gray-400">{new Date(log.timestamp).toLocaleTimeString()}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="flex">
                      {[1, 2, 3, 4, 5].map(star => (
                        <FontAwesomeIcon
                          key={star}
                          icon={faStar}
                          className={`text-xs ${star <= log.userRating ? 'text-yellow-400' : 'text-gray-600'}`}
                        />
                      ))}
                    </div>
                    <span className="text-xs text-gray-400">{log.userRating}/5</span>
                  </div>
                </div>
                <div className="text-xs text-gray-300 mb-2">
                  <strong>Model:</strong> {log.model}
                </div>
                <div className="text-xs text-gray-300 mb-2" style={{ fontFamily: 'Inter, "Noto Sans CJK", sans-serif' }}>
                  <strong>Content:</strong> {log.content.length > 100 ? log.content.substring(0, 100) + '...' : log.content}
                </div>
                <div className="flex gap-4 text-xs">
                  <span className="text-gray-400">
                    Topics: {log.topics.length} | Entities: {log.entities.length}
                  </span>
                  {log.hybridInfo?.confidence && (
                    <span className={`${
                      log.hybridInfo.confidence > 0.8 ? 'text-green-400' :
                      log.hybridInfo.confidence > 0.6 ? 'text-yellow-400' :
                      'text-red-400'
                    }`}>
                      Confidence: {Math.round(log.hybridInfo.confidence * 100)}%
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
          {testLogs.length > 5 && (
            <div className="text-xs text-gray-400 text-center">
              Showing last 5 tests. Total: {testLogs.length} tests logged.
            </div>
          )}
        </div>
      )}
      </div>
    </div>
  )
}
