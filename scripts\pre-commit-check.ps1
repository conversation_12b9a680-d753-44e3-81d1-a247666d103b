# ChatLo Pre-Commit TypeScript Check
# DEVELOPMENT STANDARD RULE 3.2: TypeScript Error Zero Tolerance

Write-Host "🔍 Running Pre-Commit TypeScript Check..." -ForegroundColor Cyan

# Run TypeScript compilation check
$tsResult = npx tsc --noEmit 2>&1
$errorCount = ($tsResult | Measure-Object -Line).Lines

if ($errorCount -eq 0) {
    Write-Host "✅ TypeScript Check PASSED - Zero errors found" -ForegroundColor Green
    Write-Host "📝 Commit can proceed" -ForegroundColor Green
    exit 0
} else {
    Write-Host "❌ TypeScript Check FAILED - $errorCount errors found" -ForegroundColor Red
    Write-Host "🚫 Commit blocked until errors are resolved" -ForegroundColor Red
    Write-Host ""
    Write-Host "TypeScript Errors:" -ForegroundColor Yellow
    Write-Host $tsResult -ForegroundColor Red
    Write-Host ""
    Write-Host "Please fix all TypeScript errors before committing." -ForegroundColor Yellow
    Write-Host "Run 'npx tsc --noEmit' to see detailed error information." -ForegroundColor Yellow
    exit 1
}
