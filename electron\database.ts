import Database from 'better-sqlite3'
import path from 'path'
import { app } from 'electron'
import { v4 as uuidv4 } from 'uuid'
import { isDev } from './utils'

export interface Conversation {
  id: string
  title: string
  is_pinned: 0 | 1
  created_at: string
  updated_at: string
}

export interface Message {
  id: string
  conversation_id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  model?: string
  is_pinned?: 0 | 1
  created_at: string
}

export interface FileRecord {
  id: string
  filename: string
  filepath: string // Legacy: full path for legacy files
  file_type: string
  file_size: number
  content_hash: string
  mime_type?: string
  extracted_content?: string
  metadata?: string // JSON
  // New vault-based fields
  vault_name?: string // e.g., "personal-vault", "work-vault"
  relative_path?: string // e.g., "getting-started/documents/report.pdf"
  storage_type?: 'legacy' | 'vault' // Indicates storage system used
  created_at: string
  updated_at: string
}

export interface FileAttachment {
  id: string
  message_id: string
  file_id: string
  attachment_type: 'attachment' | 'reference'
  created_at: string
}

export interface Artifact {
  id: string
  message_id: string
  type: 'image' | 'code' | 'markdown' | 'mermaid' | 'html' | 'json'
  title: string
  content: string
  metadata: string // JSON
  original_index: number
  created_at: string
}

export class DatabaseManager {
  private db!: Database.Database
  private currentVersion = 7
  private dbPath: string
  private connectionPool: Database.Database[] = []
  private maxConnections = 3

  constructor() {
    this.dbPath = isDev
      ? path.join(__dirname, '../chatlo-dev.db')
      : path.join(app.getPath('userData'), 'chatlo.db')

    console.log('Initializing database at:', this.dbPath)

    try {
      this.db = new Database(this.dbPath)
      this.checkDatabaseIntegrity()
      this.init()
    } catch (error) {
      console.error('Database initialization failed:', error)
      if (error instanceof Error && error.message.includes('malformed')) {
        console.log('Database appears to be corrupted, attempting repair...')
        this.repairDatabase()
      } else {
        throw error
      }
    }
  }

  private init(): void {
    // Enable UTF-8 encoding for international character support
    this.db.pragma('encoding = "UTF-8"')

    // Enable WAL mode for better performance
    this.db.pragma('journal_mode = WAL')
    this.db.pragma('synchronous = NORMAL')
    this.db.pragma('cache_size = 10000')
    this.db.pragma('temp_store = memory')

    console.log('Database encoding set to UTF-8 for international character support')

    // Initialize connection pool
    this.initializeConnectionPool()

    // Create tables
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS conversations (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        is_pinned INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS messages (
        id TEXT PRIMARY KEY,
        conversation_id TEXT NOT NULL,
        role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
        content TEXT NOT NULL,
        model TEXT,
        is_pinned INTEGER DEFAULT 0,
        entities TEXT,
        topics TEXT,
        processed_at TEXT,
        extraction_confidence REAL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (conversation_id) REFERENCES conversations (id) ON DELETE CASCADE
      );

      CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL
      );

      CREATE TABLE IF NOT EXISTS files (
        id TEXT PRIMARY KEY,
        filename TEXT NOT NULL,
        filepath TEXT NOT NULL,
        file_type TEXT NOT NULL,
        file_size INTEGER NOT NULL,
        content_hash TEXT NOT NULL,
        mime_type TEXT,
        extracted_content TEXT,
        metadata TEXT,
        vault_name TEXT,
        relative_path TEXT,
        storage_type TEXT DEFAULT 'legacy',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      CREATE TABLE IF NOT EXISTS file_attachments (
        id TEXT PRIMARY KEY,
        message_id TEXT NOT NULL,
        file_id TEXT NOT NULL,
        attachment_type TEXT NOT NULL CHECK (attachment_type IN ('attachment', 'reference')),
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (message_id) REFERENCES messages (id) ON DELETE CASCADE,
        FOREIGN KEY (file_id) REFERENCES files (id) ON DELETE CASCADE
      );

      CREATE TABLE IF NOT EXISTS artifacts (
        id TEXT PRIMARY KEY,
        message_id TEXT NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('image', 'code', 'markdown', 'mermaid', 'html', 'json')),
        title TEXT NOT NULL,
        content TEXT NOT NULL,
        metadata TEXT,
        original_index INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (message_id) REFERENCES messages (id) ON DELETE CASCADE
      );

      CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages (conversation_id);
      CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages (created_at);
      CREATE INDEX IF NOT EXISTS idx_artifacts_message_id ON artifacts (message_id);

      -- Intelligence tracking tables
      CREATE TABLE IF NOT EXISTS pinned_intelligence (
        id TEXT PRIMARY KEY,
        message_id TEXT NOT NULL,
        extraction_data TEXT NOT NULL,
        vault_assignment TEXT NOT NULL,
        processing_metadata TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (message_id) REFERENCES messages (id) ON DELETE CASCADE
      );

      CREATE INDEX IF NOT EXISTS idx_pinned_intelligence_message_id ON pinned_intelligence (message_id);
      
      -- Performance optimizations
      CREATE INDEX IF NOT EXISTS idx_messages_conversation_created ON messages (conversation_id, created_at DESC);
      CREATE INDEX IF NOT EXISTS idx_files_hash_type ON files (content_hash, file_type);
      CREATE INDEX IF NOT EXISTS idx_artifacts_message_type ON artifacts (message_id, type);
      CREATE INDEX IF NOT EXISTS idx_conversations_updated ON conversations (updated_at DESC);
      CREATE INDEX IF NOT EXISTS idx_file_attachments_message ON file_attachments (message_id);
      CREATE INDEX IF NOT EXISTS idx_file_attachments_file ON file_attachments (file_id);
    `)

    // Run migrations
    this.runMigrations()

    console.log('Database initialized successfully')
  }

  private initializeConnectionPool(): void {
    for (let i = 0; i < this.maxConnections; i++) {
      const connection = new Database(this.dbPath)
      connection.pragma('encoding = "UTF-8"')
      connection.pragma('journal_mode = WAL')
      connection.pragma('synchronous = NORMAL')
      this.connectionPool.push(connection)
    }
  }

  private getConnection(): Database.Database {
    return this.connectionPool[Math.floor(Math.random() * this.connectionPool.length)] || this.db
  }

  private closeConnectionPool(): void {
    this.connectionPool.forEach(connection => {
      try {
        connection.close()
      } catch (error) {
        console.error('Error closing database connection:', error)
      }
    })
    this.connectionPool = []
  }

  private runMigrations(): void {
    // Get current database version
    let version = 0
    try {
      const result = this.db.prepare('SELECT value FROM settings WHERE key = ?').get('db_version') as { value: string } | undefined
      version = result ? parseInt(JSON.parse(result.value)) : 0
    } catch (error) {
      // Settings table might not exist yet
      version = 0
    }

    // Run migrations if needed
    if (version < this.currentVersion) {
      console.log(`Running database migrations from version ${version} to ${this.currentVersion}`)

      // Migration v2 -> v3: Add mime_type column to files table
      if (version < 3) {
        try {
          const tableInfo = this.db.prepare("PRAGMA table_info(files)").all() as any[]
          const hasMimeType = tableInfo.some(column => column.name === 'mime_type')

          if (!hasMimeType) {
            console.log('Migration v3: Adding mime_type column to files table')
            this.db.exec('ALTER TABLE files ADD COLUMN mime_type TEXT')

            // Update existing image files with proper MIME types
            const imageFiles = this.db.prepare("SELECT id, filename FROM files WHERE file_type = 'image'").all() as any[]
            const updateStmt = this.db.prepare('UPDATE files SET mime_type = ? WHERE id = ?')

            for (const file of imageFiles) {
              const ext = file.filename.toLowerCase().split('.').pop()
              let mimeType = 'image/png' // default

              switch (ext) {
                case 'jpg':
                case 'jpeg':
                  mimeType = 'image/jpeg'
                  break
                case 'png':
                  mimeType = 'image/png'
                  break
                case 'gif':
                  mimeType = 'image/gif'
                  break
                case 'webp':
                  mimeType = 'image/webp'
                  break
                case 'bmp':
                  mimeType = 'image/bmp'
                  break
                case 'svg':
                  mimeType = 'image/svg+xml'
                  break
              }

              updateStmt.run(mimeType, file.id)
            }

            console.log(`Updated ${imageFiles.length} existing image files with MIME types`)
          }
        } catch (error) {
          console.error('Error in migration v3:', error)
        }
      }

      // Migration v3 -> v4: Add artifacts table
      if (version < 4) {
        try {
          console.log('Migration v4: Creating artifacts table')
          this.db.exec(`
            CREATE TABLE IF NOT EXISTS artifacts (
              id TEXT PRIMARY KEY,
              message_id TEXT NOT NULL,
              type TEXT NOT NULL CHECK (type IN ('image', 'code', 'markdown', 'mermaid', 'html', 'json')),
              title TEXT NOT NULL,
              content TEXT NOT NULL,
              metadata TEXT,
              original_index INTEGER NOT NULL,
              created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              FOREIGN KEY (message_id) REFERENCES messages (id) ON DELETE CASCADE
            );

            CREATE INDEX IF NOT EXISTS idx_artifacts_message_id ON artifacts (message_id);
          `)
          console.log('Artifacts table created successfully')
        } catch (error) {
          console.error('Error in migration v4:', error)
        }
      }

      // Migration v4 -> v5: Add performance indexes and optimizations
      if (version < 5) {
        try {
          console.log('Migration v5: Adding performance optimizations')
          this.db.exec(`
            CREATE INDEX IF NOT EXISTS idx_messages_conversation_created ON messages (conversation_id, created_at DESC);
            CREATE INDEX IF NOT EXISTS idx_files_hash_type ON files (content_hash, file_type);
            CREATE INDEX IF NOT EXISTS idx_artifacts_message_type ON artifacts (message_id, type);
            CREATE INDEX IF NOT EXISTS idx_conversations_updated ON conversations (updated_at DESC);
            CREATE INDEX IF NOT EXISTS idx_file_attachments_message ON file_attachments (message_id);
            CREATE INDEX IF NOT EXISTS idx_file_attachments_file ON file_attachments (file_id);
          `)
          console.log('Performance indexes added successfully')
        } catch (error) {
          console.error('Error in migration v5:', error)
        }
      }

      // Migration v6: Add intelligence fields to messages table and create pinned_intelligence table
      if (this.currentVersion < 6) {
        try {
          console.log('Running migration v6: Adding intelligence support')

          // Add intelligence fields to messages table
          this.db.exec(`
            ALTER TABLE messages ADD COLUMN entities TEXT;
            ALTER TABLE messages ADD COLUMN topics TEXT;
            ALTER TABLE messages ADD COLUMN processed_at TEXT;
            ALTER TABLE messages ADD COLUMN extraction_confidence REAL;
          `)

          // Create pinned intelligence table
          this.db.exec(`
            CREATE TABLE IF NOT EXISTS pinned_intelligence (
              id TEXT PRIMARY KEY,
              message_id TEXT NOT NULL,
              extraction_data TEXT NOT NULL,
              vault_assignment TEXT NOT NULL,
              processing_metadata TEXT NOT NULL,
              created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              FOREIGN KEY (message_id) REFERENCES messages (id) ON DELETE CASCADE
            );

            CREATE INDEX IF NOT EXISTS idx_pinned_intelligence_message_id ON pinned_intelligence (message_id);
          `)

          console.log('Migration v6 completed successfully')
        } catch (error) {
          console.error('Error in migration v6:', error)
        }
      }

      // Migration v6 -> v7: Add vault-aware file storage fields
      if (version < 7) {
        try {
          console.log('Migration v7: Adding vault-aware file storage fields')

          // Check if vault fields already exist
          const tableInfo = this.db.prepare("PRAGMA table_info(files)").all() as any[]
          const hasVaultName = tableInfo.some(column => column.name === 'vault_name')
          const hasRelativePath = tableInfo.some(column => column.name === 'relative_path')
          const hasStorageType = tableInfo.some(column => column.name === 'storage_type')

          if (!hasVaultName) {
            this.db.exec('ALTER TABLE files ADD COLUMN vault_name TEXT')
            console.log('Added vault_name column to files table')
          }

          if (!hasRelativePath) {
            this.db.exec('ALTER TABLE files ADD COLUMN relative_path TEXT')
            console.log('Added relative_path column to files table')
          }

          if (!hasStorageType) {
            this.db.exec('ALTER TABLE files ADD COLUMN storage_type TEXT DEFAULT "legacy"')
            console.log('Added storage_type column to files table')
          }

          // Mark all existing files as legacy storage type
          this.db.exec('UPDATE files SET storage_type = "legacy" WHERE storage_type IS NULL')

          console.log('Migration v7 completed successfully')
        } catch (error) {
          console.error('Error in migration v7:', error)
        }
      }

      this.setSetting('db_version', this.currentVersion)
      console.log('Database migrations completed')
    }
  }

  close(): void {
    if (this.db) {
      this.db.close()
    }
    this.closeConnectionPool()
  }

  // Batch operations for better performance
  batchInsertMessages(messages: Message[]): void {
    const insert = this.db.prepare(`
      INSERT INTO messages (id, conversation_id, role, content, model, is_pinned, created_at)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `)
    
    const insertMany = this.db.transaction((messages: Message[]) => {
      for (const message of messages) {
        insert.run(
          message.id,
          message.conversation_id,
          message.role,
          message.content,
          message.model,
          message.is_pinned || 0,
          message.created_at
        )
      }
    })
    
    insertMany(messages)
  }

  // Pagination support for large datasets
  getMessagesPaginated(conversationId: string, limit: number, offset: number): Message[] {
    return this.db.prepare(`
      SELECT * FROM messages
      WHERE conversation_id = ?
      ORDER BY created_at ASC
      LIMIT ? OFFSET ?
    `).all(conversationId, limit, offset) as Message[]
  }

  // Connection pool cleanup - consolidated implementation

  private checkDatabaseIntegrity(): void {
    try {
      // Run integrity check
      const result = this.db.prepare('PRAGMA integrity_check').get() as { integrity_check: string }
      if (result.integrity_check !== 'ok') {
        throw new Error(`Database integrity check failed: ${result.integrity_check}`)
      }
      console.log('Database integrity check passed')
    } catch (error) {
      console.error('Database integrity check failed:', error)
      throw error
    }
  }

  private repairDatabase(): void {
    const backupPath = this.dbPath + '.corrupted.' + Date.now()
    const fs = require('fs')

    try {
      // Close existing connection if open
      if (this.db) {
        this.db.close()
      }

      // Move corrupted database to backup
      if (fs.existsSync(this.dbPath)) {
        fs.renameSync(this.dbPath, backupPath)
        console.log(`Corrupted database backed up to: ${backupPath}`)
      }

      // Try to recover data using .recover command
      console.log('Attempting to recover data from corrupted database...')

      // Create new database
      this.db = new Database(this.dbPath)

      // Try to recover data from backup
      try {
        this.db.exec(`ATTACH DATABASE '${backupPath}' AS corrupted`)

        // Attempt to copy data from corrupted database
        const tables = ['conversations', 'messages', 'settings', 'files', 'file_attachments', 'artifacts']

        // Initialize new database structure first
        this.init()

        for (const table of tables) {
          try {
            // Check if table exists in corrupted database
            const tableExists = this.db.prepare(`SELECT name FROM corrupted.sqlite_master WHERE type='table' AND name=?`).get(table)

            if (tableExists) {
              console.log(`Attempting to recover table: ${table}`)

              // Get recoverable rows (skip corrupted ones)
              const rows = this.db.prepare(`SELECT * FROM corrupted.${table}`).all() as Record<string, any>[]

              if (rows.length > 0) {
                // Clear existing data in new database
                this.db.prepare(`DELETE FROM ${table}`).run()

                // Insert recovered data
                const columns = Object.keys(rows[0])
                const placeholders = columns.map(() => '?').join(', ')
                const insertStmt = this.db.prepare(`INSERT OR IGNORE INTO ${table} (${columns.join(', ')}) VALUES (${placeholders})`)

                let recovered = 0
                for (const row of rows) {
                  try {
                    insertStmt.run(...columns.map(col => row[col]))
                    recovered++
                  } catch (rowError) {
                    console.warn(`Failed to recover row from ${table}:`, rowError)
                  }
                }

                console.log(`Recovered ${recovered}/${rows.length} rows from ${table}`)
              }
            }
          } catch (tableError) {
            console.warn(`Failed to recover table ${table}:`, tableError)
          }
        }

        this.db.exec('DETACH DATABASE corrupted')
        console.log('Database recovery completed')

      } catch (recoveryError) {
        console.error('Data recovery failed:', recoveryError)
        console.log('Creating fresh database...')

        // If recovery fails, just create a fresh database
        if (this.db) {
          this.db.close()
        }
        this.db = new Database(this.dbPath)
        this.init()
      }

    } catch (error) {
      console.error('Database repair failed:', error)

      // Last resort: create completely fresh database
      try {
        if (this.db) {
          this.db.close()
        }
        if (fs.existsSync(this.dbPath)) {
          fs.unlinkSync(this.dbPath)
        }
        this.db = new Database(this.dbPath)
        this.init()
        console.log('Created fresh database as last resort')
      } catch (freshError) {
        console.error('Failed to create fresh database:', freshError)
        throw freshError
      }
    }
  }

  // Conversations
  getConversations(): Conversation[] {
    const stmt = this.db.prepare('SELECT * FROM conversations ORDER BY updated_at DESC')
    return stmt.all() as Conversation[]
  }

  getConversation(id: string): Conversation | null {
    const stmt = this.db.prepare('SELECT * FROM conversations WHERE id = ?')
    return stmt.get(id) as Conversation | null
  }

  createConversation(title: string): string {
    const id = uuidv4()
    const now = new Date().toISOString()
    const stmt = this.db.prepare('INSERT INTO conversations (id, title, is_pinned, created_at, updated_at) VALUES (?, ?, ?, ?, ?)')
    stmt.run(id, title, 0, now, now)
    return id
  }

  updateConversation(id: string, title: string): void {
    const now = new Date().toISOString()
    const stmt = this.db.prepare('UPDATE conversations SET title = ?, updated_at = ? WHERE id = ?')
    stmt.run(title, now, id)
  }

  togglePinConversation(id: string): void {
    const now = new Date().toISOString()
    const stmt = this.db.prepare('UPDATE conversations SET is_pinned = NOT is_pinned, updated_at = ? WHERE id = ?')
    stmt.run(now, id)
  }

  togglePinMessage(id: string): void {
    const stmt = this.db.prepare('UPDATE messages SET is_pinned = NOT is_pinned WHERE id = ?')
    stmt.run(id)
  }

  // Intelligence-related methods
  updateMessageIntelligence(messageId: string, entities: string, topics: string, confidence: number): void {
    const now = new Date().toISOString()
    const stmt = this.db.prepare(`
      UPDATE messages
      SET entities = ?, topics = ?, processed_at = ?, extraction_confidence = ?
      WHERE id = ?
    `)
    stmt.run(entities, topics, now, confidence, messageId)
  }

  addPinnedIntelligence(messageId: string, extractionData: string, vaultAssignment: string, processingMetadata: string): string {
    const id = uuidv4()
    const now = new Date().toISOString()
    const stmt = this.db.prepare(`
      INSERT INTO pinned_intelligence (id, message_id, extraction_data, vault_assignment, processing_metadata, created_at)
      VALUES (?, ?, ?, ?, ?, ?)
    `)
    stmt.run(id, messageId, extractionData, vaultAssignment, processingMetadata, now)
    return id
  }

  getPinnedIntelligence(messageId: string): any {
    const stmt = this.db.prepare('SELECT * FROM pinned_intelligence WHERE message_id = ?')
    return stmt.get(messageId)
  }

  getAllPinnedIntelligence(): any[] {
    const stmt = this.db.prepare('SELECT * FROM pinned_intelligence ORDER BY created_at DESC')
    return stmt.all()
  }

  deleteConversation(id: string): void {
    const stmt = this.db.prepare('DELETE FROM conversations WHERE id = ?')
    stmt.run(id)
  }

  searchConversationsAndMessages(searchTerm: string): Conversation[] {
    const stmt = this.db.prepare(`
      SELECT c.*
      FROM conversations c
      WHERE c.id IN (
        SELECT id FROM conversations WHERE title LIKE ? ESCAPE '\\'
        UNION
        SELECT conversation_id FROM messages WHERE content LIKE ? ESCAPE '\\'
        UNION
        SELECT m.conversation_id FROM messages m
        JOIN artifacts a ON m.id = a.message_id
        WHERE a.title LIKE ? ESCAPE '\\' OR a.content LIKE ? ESCAPE '\\'
      )
      ORDER BY c.updated_at DESC
    `)
    const term = `%${searchTerm.replace(/%/g, '\\%').replace(/_/g, '\\_')}%`
    return stmt.all(term, term, term, term) as Conversation[]
  }

  getConversationsWithArtifacts(): Conversation[] {
    const stmt = this.db.prepare(`
      SELECT DISTINCT c.*
      FROM conversations c
      JOIN messages m ON c.id = m.conversation_id
      JOIN artifacts a ON m.id = a.message_id
      ORDER BY c.updated_at DESC
    `)
    return stmt.all() as Conversation[]
  }

  // Messages
  getMessages(conversationId: string): Message[] {
    const stmt = this.db.prepare('SELECT * FROM messages WHERE conversation_id = ? ORDER BY created_at ASC')
    return stmt.all(conversationId) as Message[]
  }

  addMessage(conversationId: string, message: Omit<Message, 'id' | 'conversation_id' | 'created_at'>): string {
    const id = uuidv4()
    const now = new Date().toISOString()
    const stmt = this.db.prepare('INSERT INTO messages (id, conversation_id, role, content, model, is_pinned, created_at) VALUES (?, ?, ?, ?, ?, ?, ?)')
    stmt.run(id, conversationId, message.role, message.content, message.model || null, message.is_pinned || 0, now)

    // Update conversation timestamp
    const updateStmt = this.db.prepare('UPDATE conversations SET updated_at = ? WHERE id = ?')
    updateStmt.run(now, conversationId)

    return id
  }

  // Settings
  getSetting(key: string): any {
    const stmt = this.db.prepare('SELECT value FROM settings WHERE key = ?')
    const result = stmt.get(key) as { value: string } | undefined
    return result ? JSON.parse(result.value) : null
  }

  setSetting(key: string, value: any): void {
    const stmt = this.db.prepare('INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)')
    stmt.run(key, JSON.stringify(value))
  }

  // Files
  getFiles(): FileRecord[] {
    const stmt = this.db.prepare('SELECT * FROM files ORDER BY updated_at DESC')
    return stmt.all() as FileRecord[]
  }

  getFile(id: string): FileRecord | null {
    const stmt = this.db.prepare('SELECT * FROM files WHERE id = ?')
    return stmt.get(id) as FileRecord | null
  }

  getFileByPath(filepath: string): FileRecord | null {
    const stmt = this.db.prepare('SELECT * FROM files WHERE filepath = ?')
    return stmt.get(filepath) as FileRecord | null
  }

  // Get vault files by vault name and relative path
  getVaultFile(vaultName: string, relativePath: string): FileRecord | null {
    try {
      // Debug: Check current table schema
      const tableInfo = this.db.prepare("PRAGMA table_info(files)").all() as any[]
      console.log('Current files table columns:', tableInfo.map(col => col.name))

      // Try with escaped column names in case there's a reserved word conflict
      const queryString = 'SELECT * FROM files WHERE [vault_name] = ? AND [relative_path] = ? AND [storage_type] = ?'
      console.log('Query string length:', queryString.length)
      console.log('Query string bytes:', Buffer.from(queryString).toString('hex'))

      // Try to prepare the statement
      const stmt = this.db.prepare(queryString)
      return stmt.get(vaultName, relativePath, 'vault') as FileRecord | null
    } catch (error) {
      console.error('Error in getVaultFile:', error)
      console.log('Attempted query: SELECT * FROM files WHERE [vault_name] = ? AND [relative_path] = ? AND [storage_type] = ?')
      console.log('Parameters:', { vaultName, relativePath })

      // Try a different approach - check if vault_name column exists
      try {
        const testStmt = this.db.prepare('SELECT vault_name FROM files LIMIT 1')
        console.log('vault_name column test: SUCCESS')
      } catch (testError) {
        console.error('vault_name column test: FAILED', testError)
      }

      throw error
    }
  }

  // Get all files for a specific vault
  getVaultFiles(vaultName: string): FileRecord[] {
    const stmt = this.db.prepare('SELECT * FROM files WHERE [vault_name] = ? AND [storage_type] = ? ORDER BY [updated_at] DESC')
    return stmt.all(vaultName, 'vault') as FileRecord[]
  }

  // Construct full path for vault files
  constructVaultFilePath(file: FileRecord, vaultRootPath: string): string | null {
    if (file.storage_type !== 'vault' || !file.vault_name || !file.relative_path) {
      return file.filepath // Return legacy filepath for non-vault files
    }

    const path = require('path')
    return path.join(vaultRootPath, file.vault_name, file.relative_path)
  }

  addFile(file: Omit<FileRecord, 'id' | 'created_at' | 'updated_at'>): string {
    const id = uuidv4()
    const now = new Date().toISOString()
    const stmt = this.db.prepare(`
      INSERT INTO files (id, filename, filepath, file_type, file_size, content_hash, mime_type, extracted_content, metadata, vault_name, relative_path, storage_type, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `)
    stmt.run(
      id,
      file.filename,
      file.filepath,
      file.file_type,
      file.file_size,
      file.content_hash,
      file.mime_type || null,
      file.extracted_content || null,
      file.metadata || null,
      file.vault_name || null,
      file.relative_path || null,
      file.storage_type || 'legacy',
      now,
      now
    )
    return id
  }

  updateFile(id: string, updates: Partial<Omit<FileRecord, 'id' | 'created_at'>>): void {
    const now = new Date().toISOString()
    const fields = Object.keys(updates).filter(key => key !== 'id' && key !== 'created_at')
    if (fields.length === 0) return

    const setClause = fields.map(field => `${field} = ?`).join(', ')
    const values = fields.map(field => updates[field as keyof typeof updates])
    values.push(now, id)

    const stmt = this.db.prepare(`UPDATE files SET ${setClause}, updated_at = ? WHERE id = ?`)
    stmt.run(...values)
  }

  deleteFile(id: string): void {
    const stmt = this.db.prepare('DELETE FROM files WHERE id = ?')
    stmt.run(id)
  }

  searchFiles(searchTerm: string, limit: number = 10): FileRecord[] {
    if (!searchTerm.trim()) {
      // Return recent files when no search term
      const stmt = this.db.prepare('SELECT * FROM files ORDER BY updated_at DESC LIMIT ?')
      return stmt.all(limit) as FileRecord[]
    }

    // Prioritize filename matches over content matches for better performance
    const stmt = this.db.prepare(`
      SELECT *,
        CASE
          WHEN filename LIKE ? ESCAPE '\\' THEN 1
          WHEN extracted_content LIKE ? ESCAPE '\\' THEN 2
          ELSE 3
        END as match_priority
      FROM files
      WHERE filename LIKE ? ESCAPE '\\'
         OR extracted_content LIKE ? ESCAPE '\\'
      ORDER BY match_priority, updated_at DESC
      LIMIT ?
    `)
    const term = `%${searchTerm.replace(/%/g, '\\%').replace(/_/g, '\\_')}%`
    return stmt.all(term, term, term, term, limit) as FileRecord[]
  }

  // File Attachments
  getFileAttachments(messageId: string): FileAttachment[] {
    const stmt = this.db.prepare('SELECT * FROM file_attachments WHERE message_id = ?')
    return stmt.all(messageId) as FileAttachment[]
  }

  addFileAttachment(messageId: string, fileId: string, attachmentType: 'attachment' | 'reference'): string {
    const id = uuidv4()
    const now = new Date().toISOString()
    const stmt = this.db.prepare('INSERT INTO file_attachments (id, message_id, file_id, attachment_type, created_at) VALUES (?, ?, ?, ?, ?)')
    stmt.run(id, messageId, fileId, attachmentType, now)
    return id
  }

  removeFileAttachment(id: string): void {
    const stmt = this.db.prepare('DELETE FROM file_attachments WHERE id = ?')
    stmt.run(id)
  }

  getMessageFiles(messageId: string): (FileRecord & { attachment_id: string; attachment_type: string })[] {
    const stmt = this.db.prepare(`
      SELECT f.*, fa.id as attachment_id, fa.attachment_type, fa.created_at as attachment_created_at
      FROM files f
      JOIN file_attachments fa ON f.id = fa.file_id
      WHERE fa.message_id = ?
    `)
    return stmt.all(messageId) as (FileRecord & { attachment_id: string; attachment_type: string })[]
  }

  // Artifacts
  getArtifacts(messageId: string): Artifact[] {
    const stmt = this.db.prepare('SELECT * FROM artifacts WHERE message_id = ? ORDER BY original_index')
    return stmt.all(messageId) as Artifact[]
  }

  addArtifact(messageId: string, artifact: Omit<Artifact, 'id' | 'message_id' | 'created_at'>): string {
    const id = uuidv4()
    const now = new Date().toISOString()
    const stmt = this.db.prepare(`
      INSERT INTO artifacts (id, message_id, type, title, content, metadata, original_index, created_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `)
    stmt.run(id, messageId, artifact.type, artifact.title, artifact.content, artifact.metadata, artifact.original_index, now)
    return id
  }

  updateArtifact(id: string, updates: Partial<Pick<Artifact, 'title' | 'content' | 'metadata'>>): void {
    const fields = []
    const values = []

    if (updates.title !== undefined) {
      fields.push('title = ?')
      values.push(updates.title)
    }
    if (updates.content !== undefined) {
      fields.push('content = ?')
      values.push(updates.content)
    }
    if (updates.metadata !== undefined) {
      fields.push('metadata = ?')
      values.push(updates.metadata)
    }

    if (fields.length > 0) {
      values.push(id)
      const stmt = this.db.prepare(`UPDATE artifacts SET ${fields.join(', ')} WHERE id = ?`)
      stmt.run(...values)
    }
  }

  removeArtifact(id: string): void {
    const stmt = this.db.prepare('DELETE FROM artifacts WHERE id = ?')
    stmt.run(id)
  }

  getConversationArtifacts(conversationId: string): (Artifact & { message_created_at: string })[] {
    const stmt = this.db.prepare(`
      SELECT a.*, m.created_at as message_created_at
      FROM artifacts a
      JOIN messages m ON a.message_id = m.id
      WHERE m.conversation_id = ?
      ORDER BY m.created_at DESC, a.original_index
    `)
    return stmt.all(conversationId) as (Artifact & { message_created_at: string })[]
  }

  // Database diagnostics and health check
  getDatabaseHealth(): {
    isHealthy: boolean,
    integrityCheck: string,
    version: number,
    tableCount: number,
    totalRecords: number,
    fileSize: number,
    lastBackup?: string
  } {
    try {
      // Check integrity
      const integrityResult = this.db.prepare('PRAGMA integrity_check').get() as { integrity_check: string }
      const isHealthy = integrityResult.integrity_check === 'ok'

      // Get database version
      const versionResult = this.getSetting('db_version') || 0

      // Count tables
      const tables = this.db.prepare("SELECT COUNT(*) as count FROM sqlite_master WHERE type='table'").get() as { count: number }

      // Count total records across main tables
      const conversationCount = this.db.prepare('SELECT COUNT(*) as count FROM conversations').get() as { count: number }
      const messageCount = this.db.prepare('SELECT COUNT(*) as count FROM messages').get() as { count: number }
      const fileCount = this.db.prepare('SELECT COUNT(*) as count FROM files').get() as { count: number }
      const artifactCount = this.db.prepare('SELECT COUNT(*) as count FROM artifacts').get() as { count: number }

      const totalRecords = conversationCount.count + messageCount.count + fileCount.count + artifactCount.count

      // Get file size
      const fs = require('fs')
      const fileSize = fs.existsSync(this.dbPath) ? fs.statSync(this.dbPath).size : 0

      // Check for backup
      const backupPath = this.dbPath + '.backup'
      let lastBackup: string | undefined
      if (fs.existsSync(backupPath)) {
        lastBackup = fs.statSync(backupPath).mtime.toISOString()
      }

      return {
        isHealthy,
        integrityCheck: integrityResult.integrity_check,
        version: versionResult,
        tableCount: tables.count,
        totalRecords,
        fileSize,
        lastBackup
      }
    } catch (error) {
      console.error('Database health check failed:', error)
      return {
        isHealthy: false,
        integrityCheck: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        version: 0,
        tableCount: 0,
        totalRecords: 0,
        fileSize: 0
      }
    }
  }

  // Create database backup
  createBackup(): string {
    const backupPath = this.dbPath + '.backup.' + Date.now()
    const fs = require('fs')

    try {
      if (fs.existsSync(this.dbPath)) {
        fs.copyFileSync(this.dbPath, backupPath)
        console.log(`Database backup created: ${backupPath}`)

        // Also create a "latest" backup
        const latestBackupPath = this.dbPath + '.backup'
        fs.copyFileSync(this.dbPath, latestBackupPath)

        return backupPath
      } else {
        throw new Error('Database file does not exist')
      }
    } catch (error) {
      console.error('Failed to create database backup:', error)
      throw error
    }
  }
}
