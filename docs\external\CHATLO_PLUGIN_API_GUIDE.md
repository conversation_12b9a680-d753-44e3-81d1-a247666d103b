# ChatLo Plugin API Development Guide

## Table of Contents
1. [Introduction](#introduction)
2. [Getting Started](#getting-started)
3. [Plugin API Extension System](#plugin-api-extension-system)
4. [API Design Patterns](#api-design-patterns)
5. [Frontend Integration](#frontend-integration)
6. [Security & Validation](#security--validation)
7. [Testing & Debugging](#testing--debugging)
8. [Best Practices](#best-practices)

## Introduction

The ChatLo Plugin API Extension System allows developers to create plugins that expose custom API endpoints to the ChatLo application. This system provides:

- **Namespace Isolation**: Each plugin gets its own API namespace
- **Type Safety**: Full TypeScript support with validation
- **Security**: Built-in permission system and input validation
- **Monitoring**: Automatic performance tracking and error handling
- **Frontend Integration**: Seamless integration with React components

### System Architecture

```mermaid
graph TB
    subgraph "Your Plugin"
        A[Plugin Class] --> B[APIExtension Interface]
        B --> C[Custom Endpoints]
    end
    
    subgraph "ChatLo Core"
        C --> D[APIRegistry]
        D --> E[Middleware Stack]
        E --> F[Security & Validation]
    end
    
    subgraph "Frontend"
        F --> G[UnifiedAPIClient]
        G --> H[React Components]
    end
```

## Getting Started

### 1. Plugin Structure

Create a plugin that implements both `Plugin` and `APIExtension` interfaces:

```typescript
import { Plugin, PluginCapability } from '../types'
import { APIExtension, PluginAPINamespace } from '../extensionPoints'
import { APIRegistry } from '../../api/APIRegistry'

export class MyAwesomePlugin implements Plugin, APIExtension {
  // Plugin metadata
  id = 'myAwesome'
  name = 'My Awesome Plugin'
  version = '1.0.0'
  description = 'An awesome plugin with custom API endpoints'
  capabilities = [PluginCapability.API_EXTENSION]

  // APIExtension implementation
  getNamespace(): string {
    return `plugin_${this.id}` // Results in 'plugin_myAwesome'
  }

  registerEndpoints(apiRegistry: APIRegistry): PluginAPINamespace {
    const namespace: PluginAPINamespace = {
      namespace: this.getNamespace(),
      endpoints: new Map(),
      middleware: []
    }

    // Register your custom endpoints
    namespace.endpoints.set('getData', {
      handler: this.getData.bind(this),
      description: 'Get processed data',
      validationSchema: {
        query: { type: 'string', min: 1 }
      }
    })

    namespace.endpoints.set('processFile', {
      handler: this.processFile.bind(this),
      description: 'Process a file with custom logic',
      validationSchema: {
        filePath: { type: 'string', min: 1 },
        options: { type: 'object', allowEmpty: true }
      },
      requiresAuth: true,
      requiredPermission: 'file.process'
    })

    return namespace
  }

  // Your custom endpoint implementations
  private async getData(query: string): Promise<any> {
    // Your custom logic here
    return {
      success: true,
      data: `Processed query: ${query}`,
      timestamp: new Date().toISOString()
    }
  }

  private async processFile(filePath: string, options: any = {}): Promise<any> {
    // Your file processing logic here
    return {
      success: true,
      processedFile: filePath,
      options: options
    }
  }
}
```

### 2. Plugin Registration

Register your plugin in the plugin manifest:

```json
{
  "id": "myAwesome",
  "name": "My Awesome Plugin",
  "version": "1.0.0",
  "main": "MyAwesomePlugin.js",
  "capabilities": ["API_EXTENSION"],
  "permissions": ["file.process"],
  "description": "An awesome plugin with custom API endpoints"
}
```

## Plugin API Extension System

### Namespace System

Each plugin gets an isolated namespace following the pattern `plugin_${pluginId}`:

```typescript
// Plugin ID: 'dataProcessor'
// Namespace: 'plugin_dataProcessor'
// Endpoint: 'processData'
// Full IPC Channel: 'plugin_dataProcessor:processData'
```

### Endpoint Definition

```typescript
interface PluginEndpoint {
  handler: Function                    // Your endpoint function
  description?: string                 // Documentation
  validationSchema?: ValidationSchema  // Input validation
  middleware?: Function[]              // Custom middleware
  requiresAuth?: boolean              // Authentication required
  requiredPermission?: string         // Permission check
  rateLimit?: {                       // Rate limiting
    maxRequests: number
    windowMs: number
  }
}
```

### Validation Schema

Define input validation for your endpoints:

```typescript
const validationSchema = {
  // String validation
  name: { 
    type: 'string', 
    min: 1, 
    max: 100, 
    pattern: /^[a-zA-Z0-9_]+$/ 
  },
  
  // Number validation
  count: { 
    type: 'number', 
    min: 0, 
    max: 1000 
  },
  
  // Object validation
  options: { 
    type: 'object', 
    allowEmpty: true 
  },
  
  // Array validation
  items: { 
    type: 'array', 
    min: 1 
  },
  
  // Boolean validation
  enabled: { 
    type: 'boolean' 
  }
}
```

## API Design Patterns

### 1. Data Processing Plugin

```typescript
export class DataProcessorPlugin implements Plugin, APIExtension {
  id = 'dataProcessor'
  name = 'Data Processor'
  capabilities = [PluginCapability.API_EXTENSION]

  registerEndpoints(apiRegistry: APIRegistry): PluginAPINamespace {
    const namespace: PluginAPINamespace = {
      namespace: this.getNamespace(),
      endpoints: new Map(),
      middleware: [this.authMiddleware.bind(this)]
    }

    // Batch processing endpoint
    namespace.endpoints.set('processBatch', {
      handler: this.processBatch.bind(this),
      description: 'Process multiple items in batch',
      validationSchema: {
        items: { type: 'array', min: 1 },
        options: { type: 'object', allowEmpty: true }
      },
      rateLimit: { maxRequests: 10, windowMs: 60000 }
    })

    // Status checking endpoint
    namespace.endpoints.set('getStatus', {
      handler: this.getStatus.bind(this),
      description: 'Get processing status',
      validationSchema: {
        jobId: { type: 'string', min: 1 }
      }
    })

    return namespace
  }

  private async processBatch(items: any[], options: any = {}): Promise<any> {
    const jobId = `job_${Date.now()}`
    
    // Start background processing
    this.startBackgroundProcessing(jobId, items, options)
    
    return {
      success: true,
      jobId: jobId,
      status: 'started',
      itemCount: items.length
    }
  }

  private async getStatus(jobId: string): Promise<any> {
    // Check job status
    const status = this.getJobStatus(jobId)
    
    return {
      success: true,
      jobId: jobId,
      status: status.status,
      progress: status.progress,
      results: status.results
    }
  }

  private async authMiddleware(context: any): Promise<void> {
    // Custom authentication logic
    if (!context.metadata.userId) {
      throw new Error('Authentication required')
    }
  }
}
```

### 2. File Management Plugin

```typescript
export class FileManagerPlugin implements Plugin, APIExtension {
  id = 'fileManager'
  name = 'Advanced File Manager'
  capabilities = [PluginCapability.API_EXTENSION]

  registerEndpoints(apiRegistry: APIRegistry): PluginAPINamespace {
    const namespace: PluginAPINamespace = {
      namespace: this.getNamespace(),
      endpoints: new Map(),
      middleware: []
    }

    // File analysis endpoint
    namespace.endpoints.set('analyzeFile', {
      handler: this.analyzeFile.bind(this),
      description: 'Analyze file content and metadata',
      validationSchema: {
        filePath: { type: 'string', min: 1 },
        analysisType: { 
          type: 'string', 
          enum: ['content', 'metadata', 'both'] 
        }
      },
      requiredPermission: 'file.read'
    })

    // Bulk operations endpoint
    namespace.endpoints.set('bulkOperation', {
      handler: this.bulkOperation.bind(this),
      description: 'Perform bulk file operations',
      validationSchema: {
        operation: { 
          type: 'string', 
          enum: ['copy', 'move', 'delete', 'rename'] 
        },
        files: { type: 'array', min: 1 },
        destination: { type: 'string', allowEmpty: true }
      },
      requiredPermission: 'file.write',
      rateLimit: { maxRequests: 5, windowMs: 60000 }
    })

    return namespace
  }

  private async analyzeFile(filePath: string, analysisType: string): Promise<any> {
    // File analysis logic
    const analysis = {
      path: filePath,
      type: analysisType,
      size: 0,
      modified: new Date().toISOString(),
      content: null,
      metadata: null
    }

    if (analysisType === 'content' || analysisType === 'both') {
      // Analyze content
      analysis.content = await this.analyzeContent(filePath)
    }

    if (analysisType === 'metadata' || analysisType === 'both') {
      // Analyze metadata
      analysis.metadata = await this.analyzeMetadata(filePath)
    }

    return {
      success: true,
      analysis: analysis
    }
  }

  private async bulkOperation(operation: string, files: string[], destination?: string): Promise<any> {
    const results = []
    
    for (const file of files) {
      try {
        const result = await this.performOperation(operation, file, destination)
        results.push({ file, success: true, result })
      } catch (error) {
        results.push({ file, success: false, error: error.message })
      }
    }

    return {
      success: true,
      operation: operation,
      results: results,
      summary: {
        total: files.length,
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length
      }
    }
  }
}
```

## Frontend Integration

### Using Plugin APIs in React Components

```typescript
import React, { useState } from 'react'
import { apiClient } from '../api/UnifiedAPIClient'
import { useAPIMutation } from '../hooks/useAPI'

export const PluginAPIExample: React.FC = () => {
  const [query, setQuery] = useState('')
  const [result, setResult] = useState(null)

  // Using direct API calls
  const handleDirectCall = async () => {
    try {
      const response = await apiClient.callPluginAPI('myAwesome', 'getData', query)
      setResult(response)
    } catch (error) {
      console.error('API call failed:', error)
    }
  }

  // Using mutation hook
  const processData = useAPIMutation(
    (query: string) => apiClient.callPluginAPI('dataProcessor', 'processBatch', [query], {})
  )

  const handleMutationCall = async () => {
    try {
      const result = await processData.mutate(query)
      setResult(result)
    } catch (error) {
      console.error('Mutation failed:', error)
    }
  }

  return (
    <div className="plugin-api-example">
      <h3>Plugin API Integration</h3>
      
      <div className="input-section">
        <input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="Enter query"
        />
        
        <button onClick={handleDirectCall}>
          Direct API Call
        </button>
        
        <button 
          onClick={handleMutationCall}
          disabled={processData.loading}
        >
          {processData.loading ? 'Processing...' : 'Mutation Call'}
        </button>
      </div>

      {result && (
        <div className="result-section">
          <h4>Result:</h4>
          <pre>{JSON.stringify(result, null, 2)}</pre>
        </div>
      )}

      {processData.error && (
        <div className="error-section">
          <h4>Error:</h4>
          <p>{processData.error}</p>
        </div>
      )}
    </div>
  )
}
```

### Plugin API Discovery

```typescript
import React, { useEffect, useState } from 'react'
import { apiClient } from '../api/UnifiedAPIClient'

export const PluginAPIExplorer: React.FC = () => {
  const [plugins, setPlugins] = useState([])
  const [selectedPlugin, setSelectedPlugin] = useState(null)
  const [endpoints, setEndpoints] = useState([])

  useEffect(() => {
    loadPlugins()
  }, [])

  const loadPlugins = async () => {
    try {
      const pluginList = await apiClient.getAllPlugins()
      const apiPlugins = pluginList.filter(p => 
        p.capabilities.includes('API_EXTENSION')
      )
      setPlugins(apiPlugins)
    } catch (error) {
      console.error('Failed to load plugins:', error)
    }
  }

  const loadPluginEndpoints = async (pluginId: string) => {
    try {
      const response = await apiClient.getPluginAPIEndpoints(pluginId)
      setEndpoints(response.data || [])
      setSelectedPlugin(pluginId)
    } catch (error) {
      console.error('Failed to load endpoints:', error)
    }
  }

  const testEndpoint = async (pluginId: string, endpoint: string) => {
    try {
      // Test with sample data
      const result = await apiClient.callPluginAPI(pluginId, endpoint, 'test')
      console.log('Test result:', result)
    } catch (error) {
      console.error('Test failed:', error)
    }
  }

  return (
    <div className="plugin-explorer">
      <h3>Plugin API Explorer</h3>
      
      <div className="plugin-list">
        <h4>Available Plugins:</h4>
        {plugins.map(plugin => (
          <div key={plugin.id} className="plugin-item">
            <button onClick={() => loadPluginEndpoints(plugin.id)}>
              {plugin.name} ({plugin.id})
            </button>
          </div>
        ))}
      </div>

      {selectedPlugin && (
        <div className="endpoint-list">
          <h4>Endpoints for {selectedPlugin}:</h4>
          {endpoints.map(endpoint => (
            <div key={endpoint.name} className="endpoint-item">
              <div className="endpoint-info">
                <strong>{endpoint.name}</strong>
                <p>{endpoint.description}</p>
              </div>
              <button onClick={() => testEndpoint(selectedPlugin, endpoint.name)}>
                Test
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
```

## Security & Validation

### Input Validation

Always validate input parameters to prevent security issues:

```typescript
// Comprehensive validation example
const validationSchema = {
  filePath: {
    type: 'string',
    min: 1,
    max: 500,
    pattern: /^[a-zA-Z0-9\-_\/\\\.]+$/,
    sanitize: true
  },
  options: {
    type: 'object',
    allowEmpty: true,
    properties: {
      recursive: { type: 'boolean' },
      maxDepth: { type: 'number', min: 1, max: 10 }
    }
  }
}
```

### Permission System

Define and check permissions for sensitive operations:

```typescript
// In your endpoint definition
namespace.endpoints.set('sensitiveOperation', {
  handler: this.sensitiveOperation.bind(this),
  description: 'Perform sensitive operation',
  requiresAuth: true,
  requiredPermission: 'admin.sensitive',
  validationSchema: {
    action: { type: 'string', enum: ['read', 'write', 'delete'] }
  }
})
```

### Rate Limiting

Protect your endpoints from abuse:

```typescript
// Rate limiting configuration
namespace.endpoints.set('heavyOperation', {
  handler: this.heavyOperation.bind(this),
  description: 'CPU-intensive operation',
  rateLimit: {
    maxRequests: 5,      // Maximum 5 requests
    windowMs: 60000      // Per minute (60 seconds)
  }
})
```

## Testing & Debugging

### Plugin Testing

Create comprehensive tests for your plugin endpoints:

```typescript
// Test file: MyAwesomePlugin.test.ts
import { MyAwesomePlugin } from './MyAwesomePlugin'
import { APIRegistry } from '../../api/APIRegistry'

describe('MyAwesomePlugin', () => {
  let plugin: MyAwesomePlugin
  let apiRegistry: APIRegistry

  beforeEach(() => {
    plugin = new MyAwesomePlugin()
    apiRegistry = new APIRegistry()
  })

  test('should register endpoints correctly', () => {
    const namespace = plugin.registerEndpoints(apiRegistry)
    
    expect(namespace.namespace).toBe('plugin_myAwesome')
    expect(namespace.endpoints.has('getData')).toBe(true)
    expect(namespace.endpoints.has('processFile')).toBe(true)
  })

  test('getData should return processed data', async () => {
    const result = await plugin['getData']('test query')
    
    expect(result.success).toBe(true)
    expect(result.data).toContain('test query')
    expect(result.timestamp).toBeDefined()
  })

  test('should validate input parameters', () => {
    const namespace = plugin.registerEndpoints(apiRegistry)
    const getDataEndpoint = namespace.endpoints.get('getData')
    
    expect(getDataEndpoint.validationSchema).toBeDefined()
    expect(getDataEndpoint.validationSchema.query).toBeDefined()
  })
})
```

### Debugging Tools

Use the built-in monitoring and error tracking:

```typescript
// Access error statistics
const errorStats = await apiClient.call('system', 'getErrorStatistics')
console.log('Plugin errors:', errorStats.data)

// Monitor API performance
const monitoring = await apiClient.call('system', 'getMonitoringData')
console.log('Plugin performance:', monitoring.data)

// Test plugin endpoints
const testResult = await apiClient.callPluginAPI('myPlugin', 'testEndpoint')
console.log('Test result:', testResult)
```

## Best Practices

### 1. Plugin Design
- **Single Responsibility**: Each plugin should have a focused purpose
- **Clear Naming**: Use descriptive names for plugins and endpoints
- **Documentation**: Provide comprehensive descriptions for all endpoints
- **Error Handling**: Implement proper error handling and return structured responses

### 2. API Design
- **RESTful Patterns**: Follow REST conventions where applicable
- **Consistent Responses**: Use consistent response formats across endpoints
- **Versioning**: Consider versioning for breaking changes
- **Pagination**: Implement pagination for endpoints returning large datasets

### 3. Security
- **Input Validation**: Always validate and sanitize input parameters
- **Permission Checks**: Implement appropriate permission requirements
- **Rate Limiting**: Protect against abuse with rate limiting
- **Audit Logging**: Log important operations for security auditing

### 4. Performance
- **Async Operations**: Use async/await for all I/O operations
- **Caching**: Implement caching for expensive operations
- **Batch Operations**: Provide batch endpoints for bulk operations
- **Resource Management**: Properly manage resources and clean up after operations

### 5. Testing
- **Unit Tests**: Write comprehensive unit tests for all endpoints
- **Integration Tests**: Test plugin integration with the core system
- **Error Scenarios**: Test error handling and edge cases
- **Performance Tests**: Verify performance under load

This guide provides everything you need to create powerful plugins with custom API endpoints for ChatLo. The system is designed to be secure, performant, and easy to use while providing maximum flexibility for plugin developers.
