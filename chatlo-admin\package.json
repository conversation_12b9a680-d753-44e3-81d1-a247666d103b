{"name": "chatlo-admin", "version": "1.0.0", "description": "ChatLo Admin <PERSON> - Intelligence Testing, System Management & Plugin Architecture", "main": "dist/index.js", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:intelligence": "vitest run --reporter=verbose intelligence", "benchmark": "node scripts/benchmark.js", "lint": "eslint src --ext ts,tsx", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0", "axios": "^1.6.0", "socket.io-client": "^4.7.0", "chart.js": "^4.4.0", "react-chartjs-2": "^5.2.0", "monaco-editor": "^0.44.0", "@monaco-editor/react": "^4.6.0", "js-yaml": "^4.1.0", "lodash": "^4.17.21"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/js-yaml": "^4.0.0", "@types/lodash": "^4.14.0", "@vitejs/plugin-react": "^4.0.0", "typescript": "^5.0.0", "vite": "^5.0.0", "vitest": "^1.0.0", "eslint": "^8.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0"}, "keywords": ["chatlo", "admin", "intelligence", "testing", "ai", "llm", "plugin-architecture"], "author": "ChatLo Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/chatlo/chatlo-admin.git"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}