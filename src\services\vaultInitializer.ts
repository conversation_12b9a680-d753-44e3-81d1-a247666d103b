import { VaultRegistry, ContextVault, ContextFolder } from '../types'

/**
 * VaultInitializer Service
 * Handles creation of vault structure, templates, and master.md files
 */
export class VaultInitializer {
  private joinPath(...parts: string[]): string {
    const separator = navigator.platform.toLowerCase().includes('win') ? '\\' : '/'
    return parts.join(separator).replace(/[\/\\]+/g, separator)
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9)
  }

  private getCurrentTimestamp(): string {
    return new Date().toISOString()
  }

  /**
   * Initialize vault root with default template
   */
  async initializeVaultRoot(rootPath: string, templateType: string = 'default'): Promise<{ success: boolean; vaults: ContextVault[]; error?: string }> {
    try {
      // Check if electronAPI is available
      if (!window.electronAPI?.vault?.createDirectory) {
        return { success: false, vaults: [], error: 'Vault API not available - please run in Electron' }
      }

      // Create root directory
      const createRootResult = await window.electronAPI.vault.createDirectory(rootPath)
      if (!createRootResult.success) {
        return { success: false, vaults: [], error: createRootResult.error }
      }

      // Create .chatlo system directory
      const systemDir = this.joinPath(rootPath, '.chatlo')
      await window.electronAPI.vault.createDirectory(systemDir)

      // Create vaults based on template
      const vaults = await this.createVaultsFromTemplate(rootPath, templateType)

      // Create vault registry
      const registry: VaultRegistry = {
        version: '1.0',
        vaultRoot: rootPath,
        vaults,
        lastScan: this.getCurrentTimestamp(),
        preferences: {
          defaultVault: vaults.length > 0 ? vaults[0].id : null,
          defaultContext: vaults.length > 0 && vaults[0].contexts.length > 0 ? vaults[0].contexts[0].id : null,
          autoOrganize: true,
          showEmptyHints: true
        }
      }

      // Save registry
      const saveResult = await window.electronAPI.vault.saveVaultRegistry(registry)
      if (!saveResult.success) {
        return { success: false, vaults: [], error: saveResult.error }
      }

      return { success: true, vaults }
    } catch (error: any) {
      return { success: false, vaults: [], error: error.message }
    }
  }

  /**
   * Create vaults from template
   */
  private async createVaultsFromTemplate(rootPath: string, templateType: string): Promise<ContextVault[]> {


    const vaults: ContextVault[] = []

    if (templateType === 'default') {
      // Create Personal and Work vaults
      const personalVault = await this.createVault(rootPath, 'Personal Vault', 'personal-vault', '#8AB0BB', 'fa-user')
      const workVault = await this.createVault(rootPath, 'Work Vault', 'work-vault', '#FF8383', 'fa-briefcase')

      // Add first context to personal vault
      const firstContext = await this.createContext(
        this.joinPath(rootPath, 'personal-vault'),
        'Your First Context Vault',
        'getting-started',
        'Welcome to your intelligent context vault system',
        '#8AB0BB',
        'fa-lightbulb'
      )
      personalVault.contexts = [firstContext]

      // Add projects context to work vault
      const projectsContext = await this.createContext(
        this.joinPath(rootPath, 'work-vault'),
        'Projects',
        'projects',
        'Organize your work projects and documentation',
        '#FF8383',
        'fa-folder-open'
      )
      workVault.contexts = [projectsContext]

      vaults.push(personalVault, workVault)
    } else if (templateType === 'simple') {
      // Create single vault
      const simpleVault = await this.createVault(rootPath, 'My Vault', 'my-vault', '#8AB0BB', 'fa-folder')
      const firstContext = await this.createContext(
        this.joinPath(rootPath, 'my-vault'),
        'Getting Started',
        'getting-started',
        'Your first context vault',
        '#8AB0BB',
        'fa-lightbulb'
      )
      simpleVault.contexts = [firstContext]
      vaults.push(simpleVault)
    }

    return vaults
  }

  /**
   * Create a vault
   */
  private async createVault(rootPath: string, name: string, folderName: string, color: string, icon: string): Promise<ContextVault> {
    const vaultPath = this.joinPath(rootPath, folderName)
    const vaultId = this.generateId()

    // Create vault directory
    await window.electronAPI.vault.createDirectory(vaultPath)

    // Create vault metadata directory
    const metadataDir = this.joinPath(vaultPath, '.vault')
    await window.electronAPI.vault.createDirectory(metadataDir)

    // Create vault metadata
    const metadata = {
      id: vaultId,
      name,
      created: this.getCurrentTimestamp(),
      color,
      icon,
      description: `${name} for organizing your files and conversations`
    }

    const metadataPath = this.joinPath(metadataDir, 'metadata.json')
    await window.electronAPI.vault.writeFile(metadataPath, JSON.stringify(metadata, null, 2))

    return {
      id: vaultId,
      name,
      path: vaultPath,
      color,
      icon,
      created: this.getCurrentTimestamp(),
      lastAccessed: this.getCurrentTimestamp(),
      contexts: []
    }
  }

  /**
   * Create a context folder
   */
  private async createContext(
    vaultPath: string,
    name: string,
    folderName: string,
    description: string,
    color: string,
    icon: string
  ): Promise<ContextFolder> {
    const contextPath = this.joinPath(vaultPath, folderName)
    const contextId = this.generateId()

    // Create context directory
    await window.electronAPI.vault.createDirectory(contextPath)

    // Create subdirectories
    const subdirs = ['documents', 'images', 'artifacts']
    for (const subdir of subdirs) {
      await window.electronAPI.vault.createDirectory(this.joinPath(contextPath, subdir))
    }

    // Create .context system directory
    const contextSystemDir = this.joinPath(contextPath, '.context')
    await window.electronAPI.vault.createDirectory(contextSystemDir)

    // Create context metadata
    const metadata = {
      id: contextId,
      name,
      created: this.getCurrentTimestamp(),
      description,
      color,
      icon,
      contextType: folderName === 'getting-started' ? 'getting-started' : 'project',
      aiInsights: {
        lastAnalysis: this.getCurrentTimestamp(),
        suggestedActions: this.getInitialSuggestions(folderName),
        contextType: folderName === 'getting-started' ? 'getting-started' : 'project'
      },
      userPreferences: {
        color,
        icon,
        autoUpdate: true
      }
    }

    const metadataPath = this.joinPath(contextSystemDir, 'metadata.json')
    await window.electronAPI.vault.writeFile(metadataPath, JSON.stringify(metadata, null, 2))

    // Create master.md
    const masterContent = this.generateMasterDocContent(name, description, folderName)
    const masterPath = this.joinPath(contextPath, 'master.md')
    await window.electronAPI.vault.writeFile(masterPath, masterContent)

    // Create AI memory file
    const memoryContent = this.generateAIMemoryContent(name)
    const memoryPath = this.joinPath(contextSystemDir, 'ai-memory.md')
    await window.electronAPI.vault.writeFile(memoryPath, memoryContent)

    // Create chat links file
    const chatLinksPath = this.joinPath(contextSystemDir, 'chat-links.json')
    await window.electronAPI.vault.writeFile(chatLinksPath, JSON.stringify({ conversations: [] }, null, 2))

    return {
      id: contextId,
      name,
      path: contextPath,
      description,
      color,
      icon,
      status: 'empty',
      stats: {
        fileCount: 1, // master.md
        conversationCount: 0,
        lastModified: this.getCurrentTimestamp(),
        sizeBytes: masterContent.length
      },
      masterDoc: {
        exists: true,
        path: 'master.md',
        preview: masterContent.substring(0, 100) + '...',
        wordCount: masterContent.split(' ').length,
        lastUpdated: this.getCurrentTimestamp()
      },
      aiInsights: {
        suggestedActions: this.getInitialSuggestions(folderName),
        contextType: folderName === 'getting-started' ? 'getting-started' : 'project',
        readinessScore: 0.1
      }
    }
  }

  /**
   * Generate master.md content
   */
  private generateMasterDocContent(name: string, description: string, contextType: string): string {
    const timestamp = new Date().toLocaleString()
    
    if (contextType === 'getting-started') {
      return `# ${name}

${description}

## Overview
This is your intelligent context vault that serves as the central hub for organizing and understanding your project. Think of this master document as the brain of your context - it learns and grows with your work.

## Quick Start
1. **Add files** to the \`documents/\` folder to get started
2. **Start a conversation** using the chat interface with this context selected
3. **Organize insights** and progress notes right here in this document

## How It Works
- 📁 **Documents folder**: Store your project files here
- 🖼️ **Images folder**: Add screenshots, diagrams, and visual assets
- 🎯 **Artifacts folder**: Save important outputs from AI conversations
- 🧠 **AI Memory**: The \`.context/\` folder contains AI memory optimized for Gemma models

## AI Insights
*This section will be automatically updated as you add files and have conversations*

## Project Progress
*Use this space to track your progress and key milestones*

---
*Last updated: ${timestamp}*  
*Files: 0 | Conversations: 0*`
    } else {
      return `# ${name}

${description}

## Project Overview
*Describe your project goals and objectives here*

## Key Files
*This section will be automatically updated as you add files*

## Recent Activity
*Track your recent work and conversations*

## AI Insights
*AI-generated insights about your project will appear here*

## Next Steps
*Plan your next actions and milestones*

---
*Last updated: ${timestamp}*  
*Files: 0 | Conversations: 0*`
    }
  }

  /**
   * Generate AI memory content
   */
  private generateAIMemoryContent(contextName: string): string {
    return `# AI Memory for ${contextName}

## Context Understanding
This context vault was just created and is ready for your first project.

## Key Concepts
*AI will learn and document key concepts from your files and conversations*

## Relationships
*Connections between files, ideas, and conversations will be tracked here*

## Memory Chunks
*Optimized memory chunks for Gemma models will be stored here*

---
*This file is automatically managed by ChatLo's AI system*`
  }

  /**
   * Get initial suggestions based on context type
   */
  private getInitialSuggestions(contextType: string): string[] {
    if (contextType === 'getting-started') {
      return [
        '📁 Drop files here to get started',
        '💬 Ask me about organizing your project',
        '🎯 Tell me what you\'re working on'
      ]
    } else {
      return [
        '📋 Add project documentation',
        '💻 Upload code or design files',
        '🗣️ Discuss project goals with AI'
      ]
    }
  }


}

export const vaultInitializer = new VaultInitializer()
