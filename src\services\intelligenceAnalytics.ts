/**
 * Intelligence Analytics Service
 * Tracks user engagement, performance metrics, and intelligence quality
 */

interface UserEngagementMetrics {
  messagePinRate: number
  vaultSuggestionAcceptanceRate: number
  contextVaultSelectionFrequency: number
  userCreatedVaultRate: number
  featureAdoptionProgression: 'casual' | 'engaged' | 'power_user'
}

interface TechnicalPerformanceMetrics {
  processingLatency: {
    immediate: number[]
    quick: number[]
    batch: number[]
  }
  resourceUsage: {
    cpuUsage: number[]
    memoryUsage: number[]
  }
  cacheHitRates: {
    entityCache: number
    topicCache: number
    vaultSuggestionCache: number
  }
  databaseQueryPerformance: number[]
  systemResponsiveness: number[]
}

interface IntelligenceQualityMetrics {
  entityExtractionAccuracy: number
  vaultSuggestionRelevance: number
  userOverridePatterns: {
    rejectedSuggestions: number
    modifiedSuggestions: number
    acceptedSuggestions: number
  }
  masterMdUpdateQuality: number
  crossConversationRelationshipAccuracy: number
}

interface AnalyticsEvent {
  id: string
  timestamp: string
  eventType: 'pin_message' | 'vault_suggestion' | 'context_selection' | 'vault_creation' | 'processing_time' | 'user_feedback'
  data: any
  sessionId: string
  userId?: string
}

class IntelligenceAnalytics {
  private events: AnalyticsEvent[] = []
  private sessionId: string
  private readonly MAX_EVENTS = 1000
  private readonly STORAGE_KEY = 'chatlo_intelligence_analytics'

  constructor() {
    this.sessionId = this.generateSessionId()
    this.loadStoredEvents()
  }

  /**
   * Generate a unique session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Load stored events from localStorage
   */
  private loadStoredEvents(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY)
      if (stored) {
        this.events = JSON.parse(stored)
      }
    } catch (error) {
      console.error('Error loading analytics events:', error)
      this.events = []
    }
  }

  /**
   * Save events to localStorage
   */
  private saveEvents(): void {
    try {
      // Keep only recent events to avoid storage bloat
      const recentEvents = this.events.slice(-this.MAX_EVENTS)
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(recentEvents))
      this.events = recentEvents
    } catch (error) {
      console.error('Error saving analytics events:', error)
    }
  }

  /**
   * Track an analytics event
   */
  trackEvent(eventType: AnalyticsEvent['eventType'], data: any): void {
    const event: AnalyticsEvent = {
      id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      eventType,
      data,
      sessionId: this.sessionId
    }

    this.events.push(event)
    this.saveEvents()

    console.log('Analytics event tracked:', eventType, data)
  }

  /**
   * Track message pinning event
   */
  trackMessagePin(messageId: string, extractionData: any, vaultAssignment: any): void {
    this.trackEvent('pin_message', {
      messageId,
      entitiesCount: extractionData.entities?.length || 0,
      topicsCount: extractionData.topics?.length || 0,
      artifactsCount: extractionData.artifacts?.length || 0,
      processingTime: extractionData.processingTime || 0,
      vaultAssigned: !!vaultAssignment.vault_id,
      assignmentMethod: vaultAssignment.assignment_method,
      suggestionConfidence: vaultAssignment.suggestion_confidence
    })
  }

  /**
   * Track vault suggestion interaction
   */
  trackVaultSuggestion(suggestionData: {
    messageId: string
    suggestionsCount: number
    topSuggestionConfidence: number
    userAction: 'accepted' | 'rejected' | 'modified' | 'created_new' | 'skipped'
    selectedVaultId?: string
    processingTime: number
  }): void {
    this.trackEvent('vault_suggestion', suggestionData)
  }

  /**
   * Track context vault selection
   */
  trackContextSelection(contextId: string | null, selectionMethod: 'manual' | 'auto' | 'cleared'): void {
    this.trackEvent('context_selection', {
      contextId,
      selectionMethod,
      timestamp: new Date().toISOString()
    })
  }

  /**
   * Track processing time for performance monitoring
   */
  trackProcessingTime(operationType: 'immediate' | 'quick' | 'batch', processingTime: number, success: boolean): void {
    this.trackEvent('processing_time', {
      operationType,
      processingTime,
      success,
      timestamp: new Date().toISOString()
    })
  }

  /**
   * Track user feedback on suggestions
   */
  trackUserFeedback(feedbackData: {
    messageId: string
    suggestionId: string
    feedbackType: 'helpful' | 'not_helpful' | 'incorrect' | 'missing_context'
    userComment?: string
  }): void {
    this.trackEvent('user_feedback', feedbackData)
  }

  /**
   * Get user engagement metrics
   */
  getUserEngagementMetrics(): UserEngagementMetrics {
    const pinEvents = this.events.filter(e => e.eventType === 'pin_message')
    const suggestionEvents = this.events.filter(e => e.eventType === 'vault_suggestion')
    const contextEvents = this.events.filter(e => e.eventType === 'context_selection')
    
    const totalMessages = pinEvents.length // Approximate
    const totalSuggestions = suggestionEvents.length
    const acceptedSuggestions = suggestionEvents.filter(e => e.data.userAction === 'accepted').length
    const createdVaults = suggestionEvents.filter(e => e.data.userAction === 'created_new').length
    const contextSelections = contextEvents.filter(e => e.data.selectionMethod === 'manual').length

    const messagePinRate = totalMessages > 0 ? pinEvents.length / totalMessages : 0
    const vaultSuggestionAcceptanceRate = totalSuggestions > 0 ? acceptedSuggestions / totalSuggestions : 0
    const contextVaultSelectionFrequency = contextSelections
    const userCreatedVaultRate = totalSuggestions > 0 ? createdVaults / totalSuggestions : 0

    // Determine user progression
    let featureAdoptionProgression: 'casual' | 'engaged' | 'power_user' = 'casual'
    if (pinEvents.length > 10 && vaultSuggestionAcceptanceRate > 0.6) {
      featureAdoptionProgression = 'power_user'
    } else if (pinEvents.length > 3 && vaultSuggestionAcceptanceRate > 0.3) {
      featureAdoptionProgression = 'engaged'
    }

    return {
      messagePinRate,
      vaultSuggestionAcceptanceRate,
      contextVaultSelectionFrequency,
      userCreatedVaultRate,
      featureAdoptionProgression
    }
  }

  /**
   * Get technical performance metrics
   */
  getTechnicalPerformanceMetrics(): TechnicalPerformanceMetrics {
    const processingEvents = this.events.filter(e => e.eventType === 'processing_time')
    
    const immediateProcessing = processingEvents
      .filter(e => e.data.operationType === 'immediate')
      .map(e => e.data.processingTime)
    
    const quickProcessing = processingEvents
      .filter(e => e.data.operationType === 'quick')
      .map(e => e.data.processingTime)
    
    const batchProcessing = processingEvents
      .filter(e => e.data.operationType === 'batch')
      .map(e => e.data.processingTime)

    return {
      processingLatency: {
        immediate: immediateProcessing,
        quick: quickProcessing,
        batch: batchProcessing
      },
      resourceUsage: {
        cpuUsage: [], // Will be populated by performance monitor
        memoryUsage: []
      },
      cacheHitRates: {
        entityCache: 0.85, // Placeholder - would be calculated from actual cache stats
        topicCache: 0.78,
        vaultSuggestionCache: 0.92
      },
      databaseQueryPerformance: [],
      systemResponsiveness: []
    }
  }

  /**
   * Get intelligence quality metrics
   */
  getIntelligenceQualityMetrics(): IntelligenceQualityMetrics {
    const suggestionEvents = this.events.filter(e => e.eventType === 'vault_suggestion')
    // const _feedbackEvents = this.events.filter(e => e.eventType === 'user_feedback')
    
    const totalSuggestions = suggestionEvents.length
    const acceptedSuggestions = suggestionEvents.filter(e => e.data.userAction === 'accepted').length
    const rejectedSuggestions = suggestionEvents.filter(e => e.data.userAction === 'rejected').length
    const modifiedSuggestions = suggestionEvents.filter(e => e.data.userAction === 'modified').length

    const vaultSuggestionRelevance = totalSuggestions > 0 ? acceptedSuggestions / totalSuggestions : 0
    
    // Calculate average confidence of accepted suggestions
    const acceptedConfidences = suggestionEvents
      .filter(e => e.data.userAction === 'accepted')
      .map(e => e.data.topSuggestionConfidence || 0)
    
    const entityExtractionAccuracy = acceptedConfidences.length > 0 
      ? acceptedConfidences.reduce((sum, conf) => sum + conf, 0) / acceptedConfidences.length 
      : 0

    return {
      entityExtractionAccuracy,
      vaultSuggestionRelevance,
      userOverridePatterns: {
        rejectedSuggestions,
        modifiedSuggestions,
        acceptedSuggestions
      },
      masterMdUpdateQuality: 0.85, // Placeholder - would need user feedback
      crossConversationRelationshipAccuracy: 0.75 // Placeholder
    }
  }

  /**
   * Get comprehensive analytics summary
   */
  getAnalyticsSummary() {
    return {
      sessionId: this.sessionId,
      totalEvents: this.events.length,
      userEngagement: this.getUserEngagementMetrics(),
      technicalPerformance: this.getTechnicalPerformanceMetrics(),
      intelligenceQuality: this.getIntelligenceQualityMetrics(),
      lastUpdated: new Date().toISOString()
    }
  }

  /**
   * Clear all analytics data
   */
  clearAnalytics(): void {
    this.events = []
    localStorage.removeItem(this.STORAGE_KEY)
    console.log('Analytics data cleared')
  }

  /**
   * Export analytics data for analysis
   */
  exportAnalytics(): string {
    return JSON.stringify({
      sessionId: this.sessionId,
      events: this.events,
      summary: this.getAnalyticsSummary()
    }, null, 2)
  }
}

export const intelligenceAnalytics = new IntelligenceAnalytics()
