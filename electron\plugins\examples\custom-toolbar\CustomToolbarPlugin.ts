import { BasePlugin, PluginCapability } from '../../types'
import { UIExtension, UILocation, ToolbarItem, ToolbarLocation } from '../../extensionPoints'

export default class CustomToolbarPlugin implements BasePlugin, UIExtension {
  id = 'custom-toolbar-enhancer'
  name = 'Custom Toolbar Enhancer'
  version = '1.0.0'
  description = 'Adds custom toolbar items to the chat interface'
  author = 'ChatLo Community'
  
  async initialize(): Promise<void> {
    console.log('Custom Toolbar Plugin initialized')
  }
  
  getCapabilities(): PluginCapability[] {
    return [PluginCapability.UI_EXTENSION]
  }
  
  getDefaultConfig(): Record<string, any> {
    return {
      showQuickActions: true,
      showTemplates: true
    }
  }
  
  // Provide toolbar items
  provideToolbarItem?(location: ToolbarLocation): ToolbarItem {
    if (location === ToolbarLocation.CHAT_INPUT_TOOLBAR) {
      return {
        id: 'quick-templates',
        label: 'Templates',
        icon: 'template',
        onClick: () => this.showTemplateMenu(),
        tooltip: 'Insert message template'
      }
    }
    
    // Return a default toolbar item for other locations
    return {
      id: 'default',
      label: '',
      icon: '',
      onClick: () => {}
    }
  }
  
  private showTemplateMenu(): void {
    // Implementation would show a template selection menu
    console.log('Showing template menu...')
  }
}