/**
 * Markdown File Processing Plugin
 * Core plugin for processing Markdown files with metadata extraction
 */

import * as fs from 'fs'
import * as path from 'path'
import { FileProcessorPlugin, ProcessedFileContent } from '../types'

export default class MarkdownPlugin implements FileProcessorPlugin {
  name = 'MarkdownPlugin'
  version = '1.0.0'
  description = 'Core plugin for processing Markdown files with frontmatter support'
  author = 'ChatLo Team'
  optional = false

  supportedTypes = ['markdown', 'md']
  supportedExtensions = ['.md', '.markdown', '.mdown', '.mkd']

  canProcess(filePath: string, fileType: string): boolean {
    const extension = path.extname(filePath).toLowerCase()
    return this.supportedTypes.includes(fileType) || this.supportedExtensions.includes(extension)
  }

  async process(filePath: string): Promise<ProcessedFileContent> {
    try {
      const stats = await fs.promises.stat(filePath)
      
      // Check file size (limit to 10MB for markdown files)
      const maxSize = 10 * 1024 * 1024 // 10MB
      if (stats.size > maxSize) {
        return {
          error: `Markdown file too large: ${Math.round(stats.size / 1024 / 1024)}MB (max: 10MB)`
        }
      }

      const content = await fs.promises.readFile(filePath, 'utf8')
      
      // Parse frontmatter if present
      const { frontmatter, body } = this.parseFrontmatter(content)
      
      // Extract markdown metadata
      const markdownMetadata = this.extractMarkdownMetadata(body)
      
      return {
        text: body,
        metadata: {
          encoding: 'utf8',
          frontmatter,
          ...markdownMetadata,
          fileSize: stats.size,
          lastModified: stats.mtime,
          extension: path.extname(filePath),
          processor: this.name
        }
      }
    } catch (error: any) {
      console.error('Error processing markdown file:', error)
      return {
        error: `Failed to process markdown file: ${error?.message || 'Unknown error'}`
      }
    }
  }

  // Parse YAML frontmatter
  private parseFrontmatter(content: string): { frontmatter: any; body: string } {
    const frontmatterRegex = /^---\s*\n([\s\S]*?)\n---\s*\n([\s\S]*)$/
    const match = content.match(frontmatterRegex)
    
    if (!match) {
      return { frontmatter: null, body: content }
    }

    try {
      // Simple YAML parsing for basic frontmatter
      const yamlContent = match[1]
      const frontmatter: any = {}
      
      const lines = yamlContent.split('\n')
      for (const line of lines) {
        const colonIndex = line.indexOf(':')
        if (colonIndex > 0) {
          const key = line.substring(0, colonIndex).trim()
          const value = line.substring(colonIndex + 1).trim()
          
          // Remove quotes if present
          const cleanValue = value.replace(/^["']|["']$/g, '')
          frontmatter[key] = cleanValue
        }
      }
      
      return { frontmatter, body: match[2] }
    } catch (error) {
      console.warn('Failed to parse frontmatter:', error)
      return { frontmatter: null, body: content }
    }
  }

  // Extract markdown metadata
  private extractMarkdownMetadata(content: string) {
    const lines = content.split('\n')
    const words = content.split(/\s+/).filter(word => word.length > 0)
    
    // Count headers
    const headers = {
      h1: (content.match(/^# /gm) || []).length,
      h2: (content.match(/^## /gm) || []).length,
      h3: (content.match(/^### /gm) || []).length,
      h4: (content.match(/^#### /gm) || []).length,
      h5: (content.match(/^##### /gm) || []).length,
      h6: (content.match(/^###### /gm) || []).length
    }

    // Count other elements
    const links = (content.match(/\[.*?\]\(.*?\)/g) || []).length
    const images = (content.match(/!\[.*?\]\(.*?\)/g) || []).length
    const codeBlocks = (content.match(/```[\s\S]*?```/g) || []).length
    const inlineCode = (content.match(/`[^`]+`/g) || []).length
    const tables = (content.match(/\|.*\|/g) || []).length
    const lists = (content.match(/^[\s]*[-*+]\s/gm) || []).length
    const numberedLists = (content.match(/^[\s]*\d+\.\s/gm) || []).length

    return {
      lines: lines.length,
      characters: content.length,
      words: words.length,
      headers,
      elements: {
        links,
        images,
        codeBlocks,
        inlineCode,
        tables,
        lists,
        numberedLists
      }
    }
  }
}
