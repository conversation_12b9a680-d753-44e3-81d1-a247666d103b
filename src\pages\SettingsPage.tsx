import React, { useState, useEffect } from 'react'
import { useAppStore } from '../store'
import { <PERSON>Left, Key, Settings, User, FileText, Folder, RefreshCw, Sliders } from '../components/Icons'
import { useNavigate } from 'react-router-dom'
import { vaultInitializer } from '../services/vaultInitializer'
import { vaultUIManager } from '../services/vaultUIManager'
import { IntelligenceTestLauncher } from '../components/IntelligenceTestLauncher'
import { IntelligenceAnalyticsDashboard } from '../components/IntelligenceAnalyticsDashboard'
import { PluginManager } from '../components/PluginManager'

const SettingsPage: React.FC = () => {
  const navigate = useNavigate()
  const { settings, updateSettings } = useAppStore()
  const [activeTab, setActiveTab] = useState('api')
  const [localSettings, setLocalSettings] = useState(settings)
  const [isLoading, setIsLoading] = useState(false)
  const [testResult, setTestResult] = useState<string | null>(null)
  const [chatloPath, setChatloPath] = useState('')
  const [storageInfo, setStorageInfo] = useState<any>(null)

  // Vault-related state
  const [vaultRootPath, setVaultRootPath] = useState('')
  const [vaultInitializing, setVaultInitializing] = useState(false)
  const [vaultResult, setVaultResult] = useState<string | null>(null)
  const [showTemplateSelection, setShowTemplateSelection] = useState(false)

  useEffect(() => {
    setLocalSettings(settings)
    loadChatloPath()
    loadStorageInfo()
    loadVaultRoot()
  }, [settings])

  const loadChatloPath = async () => {
    try {
      if (window.electronAPI?.files) {
        const path = await window.electronAPI.files.getChatloFolderPath()
        setChatloPath(path)
      }
    } catch (error) {
      console.error('Error loading Chatlo path:', error)
    }
  }

  const loadStorageInfo = async () => {
    try {
      if (window.electronAPI?.files) {
        const files = await window.electronAPI.files.getIndexedFiles()
        const totalSize = files.reduce((sum, file) => sum + file.file_size, 0)
        setStorageInfo({
          totalFiles: files.length,
          totalSize: totalSize,
          fileTypes: files.reduce((acc, file) => {
            acc[file.file_type] = (acc[file.file_type] || 0) + 1
            return acc
          }, {} as Record<string, number>)
        })
      }
    } catch (error) {
      console.error('Error loading storage info:', error)
    }
  }

  // Load vault root path
  const loadVaultRoot = async () => {
    try {
      // First try to load from database settings
      if (window.electronAPI?.settings) {
        const savedVaultRoot = await window.electronAPI.settings.get('vault-root-path')
        if (savedVaultRoot) {
          console.log('Loaded vault root from database:', savedVaultRoot)
          setVaultRootPath(savedVaultRoot)
          return
        }
      }

      // Fallback to registry file
      const registry = await vaultUIManager.getVaultRegistry()
      if (registry) {
        console.log('Loaded vault root from registry:', registry.vaultRoot)
        setVaultRootPath(registry.vaultRoot)

        // Save to database for future use
        if (window.electronAPI?.settings) {
          await window.electronAPI.settings.set('vault-root-path', registry.vaultRoot)
        }
      }
    } catch (error) {
      console.error('Error loading vault root:', error)
    }
  }

  // Handle vault root selection
  const handleSelectVaultRoot = async () => {
    try {
      setVaultInitializing(true)
      if (window.electronAPI?.files) {
        const result = await window.electronAPI.files.showOpenDialog({
          title: 'Select Vault Root Folder',
          properties: ['openDirectory'],
          defaultPath: vaultRootPath || undefined
        })

        if (!result.canceled && result.filePaths.length > 0) {
          const newPath = result.filePaths[0]
          setVaultRootPath(newPath)

          // Save vault root path to database
          if (window.electronAPI?.settings) {
            await window.electronAPI.settings.set('vault-root-path', newPath)
            console.log('Vault root path saved to database:', newPath)
          }

          setShowTemplateSelection(true)
        }
      }
    } catch (error) {
      console.error('Error selecting vault root:', error)
      setVaultResult('❌ Error selecting folder')
    } finally {
      setVaultInitializing(false)
    }
  }

  // Initialize vault with template
  const handleInitializeVault = async (templateType: string) => {
    try {
      setVaultInitializing(true)
      setVaultResult('Initializing vault structure...')

      const result = await vaultInitializer.initializeVaultRoot(vaultRootPath, templateType)

      if (result.success) {
        setVaultResult(`✅ Vault initialized successfully! Created ${result.vaults.length} vault${result.vaults.length !== 1 ? 's' : ''}.`)
        setShowTemplateSelection(false)

        // Refresh vault data
        await loadVaultRoot()

        // Navigate to Files page to show the new vault structure
        setTimeout(() => {
          navigate('/files')
        }, 2000) // Give user time to see success message
      } else {
        setVaultResult(`❌ Failed to initialize vault: ${result.error}`)
      }
    } catch (error: any) {
      console.error('Error initializing vault:', error)
      setVaultResult(`❌ Error: ${error.message}`)
    } finally {
      setVaultInitializing(false)
      setTimeout(() => setVaultResult(null), 5000)
    }
  }

  const handleSave = async () => {
    try {
      setIsLoading(true)
      updateSettings(localSettings)

      if (window.electronAPI?.settings) {
        await window.electronAPI.settings.set('app-settings', localSettings)
      }

      setTestResult('✅ Settings saved successfully!')
      setTimeout(() => setTestResult(null), 3000)
    } catch (error) {
      console.error('Error saving settings:', error)
      setTestResult('❌ Failed to save settings')
      setTimeout(() => setTestResult(null), 3000)
    } finally {
      setIsLoading(false)
    }
  }

  const testApiKey = async () => {
    if (!localSettings.openRouterApiKey) {
      setTestResult('❌ Please enter an API key first')
      return
    }

    try {
      setIsLoading(true)
      setTestResult('Testing API key...')

      // Import the service dynamically to avoid circular dependencies
      const { openRouterService } = await import('../services/openrouter')
      openRouterService.setApiKey(localSettings.openRouterApiKey)

      const validation = await openRouterService.validateApiKey()

      if (validation.valid) {
        setTestResult('✅ API key is valid!')
      } else {
        setTestResult('❌ Invalid API key')
      }
    } catch (error) {
      console.error('Error testing API key:', error)
      setTestResult('❌ Failed to test API key')
    } finally {
      setIsLoading(false)
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  const handleSelectFolder = async () => {
    try {
      setIsLoading(true)
      if (window.electronAPI?.files) {
        const result = await window.electronAPI.files.showOpenDialog({
          title: 'Select Chatlo Folder',
          properties: ['openDirectory'],
          defaultPath: chatloPath
        })

        if (!result.canceled && result.filePaths.length > 0) {
          const newPath = result.filePaths[0]
          setTestResult('Updating folder location...')

          await window.electronAPI.files.setChatloFolderPath(newPath)
          setChatloPath(newPath)
          await loadStorageInfo() // Refresh storage info

          setTestResult('✅ Folder location updated successfully!')
          setTimeout(() => setTestResult(null), 3000)
        }
      }
    } catch (error) {
      console.error('Error selecting folder:', error)
      setTestResult('❌ Failed to update folder location')
      setTimeout(() => setTestResult(null), 3000)
    } finally {
      setIsLoading(false)
    }
  }

  const handleIndexFiles = async () => {
    try {
      setIsLoading(true)
      if (window.electronAPI?.files) {
        await window.electronAPI.files.indexAllFiles()
        await loadStorageInfo() // Refresh storage info
        setTestResult('✅ Files indexed successfully!')
        setTimeout(() => setTestResult(null), 3000)
      }
    } catch (error) {
      console.error('Error indexing files:', error)
      setTestResult('❌ Failed to index files')
      setTimeout(() => setTestResult(null), 3000)
    } finally {
      setIsLoading(false)
    }
  }

  const tabs = [
    { id: 'api', label: 'API Setup', icon: Key },
    { id: 'data', label: 'Data Management', icon: FileText },
    { id: 'plugins', label: 'Plugins', icon: Sliders },
    { id: 'profile', label: 'User Profile', icon: User },
    { id: 'test', label: 'Intelligence Test', icon: Settings },
  ]

  return (
    <div className="h-screen flex flex-col bg-gray-900 text-supplement1">
      {/* Header */}
      <header className="flex items-center gap-4 h-16 px-6 border-b border-tertiary bg-gray-800/60 backdrop-blur-lg">
        <button
          onClick={() => navigate('/')}
          className="u1-button-ghost h-8 w-8 flex items-center justify-center"
        >
          <ArrowLeft className="h-4 w-4" />
        </button>
        <div className="flex items-center gap-2">
          <Settings className="h-5 w-5 text-primary" />
          <h1 className="text-xl font-semibold text-supplement1">Settings</h1>
        </div>
      </header>

      <div className="flex-1 flex">
        {/* Sidebar */}
        <nav className="w-64 border-r border-tertiary bg-gray-800/30">
          <div className="p-4">
            <div className="space-y-1">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`
                      w-full flex items-center gap-3 px-3 py-2 rounded-lg text-sm transition-colors
                      ${activeTab === tab.id
                        ? 'bg-primary text-gray-900'
                        : 'text-gray-400 hover:text-supplement1 hover:bg-gray-700'
                      }
                    `}
                  >
                    <Icon className="h-4 w-4" />
                    {tab.label}
                  </button>
                )
              })}
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto">
          <div className="max-w-4xl mx-auto p-8">
            {activeTab === 'api' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-2 text-supplement1">API Configuration</h2>
                  <p className="text-gray-400">Configure your OpenRouter API key to access AI models.</p>
                </div>

                <div className="u1-card bg-gray-800/50 border border-gray-700">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-2 text-supplement1">
                        OpenRouter API Key
                      </label>
                      <div className="flex gap-3">
                        <input
                          type="password"
                          value={localSettings.openRouterApiKey || ''}
                          onChange={(e) => setLocalSettings(prev => ({
                            ...prev,
                            openRouterApiKey: e.target.value
                          }))}
                          placeholder="sk-or-..."
                          className="u1-input-field flex-1"
                        />
                        <button
                          onClick={testApiKey}
                          disabled={isLoading}
                          className="u1-button-ghost px-4 py-2 disabled:opacity-50"
                        >
                          Test
                        </button>
                      </div>
                      {testResult && (
                        <p className="mt-2 text-sm">{testResult}</p>
                      )}
                    </div>

                    <div className="flex gap-3">
                      <button
                        onClick={handleSave}
                        disabled={isLoading}
                        className="u1-button-primary disabled:opacity-50"
                      >
                        Save Settings
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'data' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-2 text-supplement1">Data Management</h2>
                  <p className="text-gray-400">Manage your files, conversations, and storage.</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Context Vault Root */}
                  <div className="u1-card bg-gray-800/50 border border-gray-700">
                    <h3 className="text-lg font-medium mb-4">Context Vault Root</h3>
                    <p className="text-gray-400 text-sm mb-4">
                      Choose where to store your context vaults and organized files.
                    </p>

                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium mb-2">
                          Vault Root Location
                        </label>
                        <div className="flex gap-2">
                          <input
                            type="text"
                            value={vaultRootPath}
                            readOnly
                            placeholder="No vault root selected"
                            className="flex-1 bg-neutral-800 border border-neutral-700 rounded-lg px-3 py-2 text-sm font-mono text-neutral-300"
                          />
                          <button
                            onClick={handleSelectVaultRoot}
                            disabled={vaultInitializing}
                            className="px-3 py-2 bg-neutral-700 hover:bg-neutral-600 text-white rounded-lg text-sm font-medium transition-colors flex items-center gap-2 disabled:opacity-50"
                          >
                            <Folder size={16} />
                            {vaultInitializing ? 'Loading...' : 'Browse'}
                          </button>
                        </div>
                      </div>

                      {vaultResult && (
                        <div className="text-sm p-3 bg-gray-700 rounded-lg">
                          {vaultResult}
                        </div>
                      )}

                      {/* Template Selection Modal */}
                      {showTemplateSelection && (
                        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
                          <div className="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 border border-gray-700">
                            <h3 className="text-lg font-medium mb-4">Choose Vault Template</h3>
                            <p className="text-gray-400 text-sm mb-6">
                              Select how you'd like to organize your context vaults:
                            </p>

                            <div className="space-y-3">
                              <button
                                onClick={() => handleInitializeVault('default')}
                                disabled={vaultInitializing}
                                className="w-full p-4 bg-gray-700 hover:bg-gray-600 rounded-lg text-left transition-colors disabled:opacity-50"
                              >
                                <div className="font-medium">Default Setup</div>
                                <div className="text-sm text-gray-400">Personal & Work vaults with getting started context</div>
                              </button>

                              <button
                                onClick={() => handleInitializeVault('simple')}
                                disabled={vaultInitializing}
                                className="w-full p-4 bg-gray-700 hover:bg-gray-600 rounded-lg text-left transition-colors disabled:opacity-50"
                              >
                                <div className="font-medium">Simple Setup</div>
                                <div className="text-sm text-gray-400">Single vault to get started</div>
                              </button>
                            </div>

                            <div className="flex gap-2 mt-6">
                              <button
                                onClick={() => setShowTemplateSelection(false)}
                                disabled={vaultInitializing}
                                className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-500 text-white rounded-lg transition-colors disabled:opacity-50"
                              >
                                Cancel
                              </button>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* File Storage */}
                  <div className="u1-card bg-gray-800/50 border border-gray-700">
                    <h3 className="text-lg font-medium mb-4">File Storage</h3>
                    <div className="space-y-4">
                      {/* Folder Selection */}
                      <div>
                        <label className="block text-sm font-medium mb-2">
                          Chatlo Folder Location
                        </label>
                        <div className="flex gap-2">
                          <input
                            type="text"
                            value={chatloPath}
                            readOnly
                            className="flex-1 bg-neutral-800 border border-neutral-700 rounded-lg px-3 py-2 text-sm font-mono text-neutral-300"
                          />
                          <button
                            onClick={handleSelectFolder}
                            className="px-3 py-2 bg-neutral-700 hover:bg-neutral-600 text-white rounded-lg text-sm font-medium transition-colors flex items-center gap-2"
                            title="Select folder"
                          >
                            <Folder className="h-4 w-4" />
                            Browse
                          </button>
                          <button
                            onClick={handleIndexFiles}
                            disabled={isLoading}
                            className="px-3 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg text-sm font-medium transition-colors flex items-center gap-2 disabled:opacity-50"
                            title="Index files"
                          >
                            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                            Index Files
                          </button>
                        </div>
                        {testResult && (
                          <p className="mt-2 text-sm">{testResult}</p>
                        )}
                      </div>

                      {/* Storage Stats */}
                      {storageInfo && (
                        <div className="pt-4 border-t border-neutral-700">
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div className="flex justify-between">
                              <span className="text-neutral-400">Total Files:</span>
                              <span className="text-neutral-200">{storageInfo.totalFiles}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-neutral-400">Total Size:</span>
                              <span className="text-neutral-200">{formatFileSize(storageInfo.totalSize)}</span>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* File Types */}
                  {storageInfo && (
                    <div className="bg-neutral-900/50 border border-neutral-800 rounded-lg p-6">
                      <h3 className="text-lg font-medium mb-4">File Types</h3>
                      <div className="space-y-2">
                        {Object.entries(storageInfo.fileTypes).map(([type, count]) => (
                          <div key={type} className="flex justify-between text-sm">
                            <span className="text-neutral-400 capitalize">{type}:</span>
                            <span className="text-neutral-200">{String(count)}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'profile' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-2">User Profile</h2>
                  <p className="text-neutral-400">Manage your profile and preferences.</p>
                </div>

                <div className="bg-neutral-900/50 border border-neutral-800 rounded-lg p-6">
                  <div className="text-center py-8">
                    <User className="h-12 w-12 text-neutral-500 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-neutral-400 mb-2">Coming Soon</h3>
                    <p className="text-sm text-neutral-500">
                      User profile features will be available in a future update.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'plugins' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-2 text-supplement1">Plugin Management</h2>
                  <p className="text-gray-400">Manage your plugins and extensions.</p>
                </div>

                <PluginManager />
              </div>
            )}

            {activeTab === 'test' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-2 text-supplement1">Intelligence System Test</h2>
                  <p className="text-gray-400">Test the intelligence extraction and performance monitoring systems.</p>
                </div>

                <IntelligenceTestLauncher />

                <div className="border-t border-tertiary/30 pt-6">
                  <h3 className="text-xl font-semibold mb-4 text-supplement1">Analytics Dashboard</h3>
                  <IntelligenceAnalyticsDashboard />
                </div>
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  )
}

export default SettingsPage
