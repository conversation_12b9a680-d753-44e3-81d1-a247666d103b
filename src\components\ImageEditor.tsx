import React, { useState, useRef, useEffect } from 'react'
import { X, RotateC<PERSON>, <PERSON>rop, Check } from './Icons'

interface ImageEditorProps {
  imageUrl: string
  filename: string
  onSave: (editedImageBlob: Blob, filename: string) => void
  onClose: () => void
}

interface CropArea {
  x: number
  y: number
  width: number
  height: number
}

const ImageEditor: React.FC<ImageEditorProps> = ({ imageUrl, filename, onSave, onClose }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const imageRef = useRef<HTMLImageElement>(null)
  const [rotation, setRotation] = useState(0)
  const [cropArea, setCropArea] = useState<CropArea | null>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const [imageLoaded, setImageLoaded] = useState(false)
  const [originalDimensions, setOriginalDimensions] = useState({ width: 0, height: 0 })

  useEffect(() => {
    const img = new Image()
    img.onload = () => {
      setOriginalDimensions({ width: img.width, height: img.height })
      setImageLoaded(true)
      drawImage()
    }
    img.src = imageUrl
    imageRef.current = img
  }, [imageUrl])

  useEffect(() => {
    if (imageLoaded) {
      drawImage()
    }
  }, [rotation, cropArea, imageLoaded])

  const drawImage = () => {
    const canvas = canvasRef.current
    const img = imageRef.current
    if (!canvas || !img) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Set canvas size
    const maxSize = 600
    const scale = Math.min(maxSize / img.width, maxSize / img.height)
    canvas.width = img.width * scale
    canvas.height = img.height * scale

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // Save context for rotation
    ctx.save()

    // Apply rotation
    if (rotation !== 0) {
      ctx.translate(canvas.width / 2, canvas.height / 2)
      ctx.rotate((rotation * Math.PI) / 180)
      ctx.translate(-canvas.width / 2, -canvas.height / 2)
    }

    // Draw image
    ctx.drawImage(img, 0, 0, canvas.width, canvas.height)

    // Restore context
    ctx.restore()

    // Draw crop overlay
    if (cropArea) {
      ctx.fillStyle = 'rgba(0, 0, 0, 0.5)'
      ctx.fillRect(0, 0, canvas.width, canvas.height)
      
      ctx.clearRect(
        cropArea.x * scale,
        cropArea.y * scale,
        cropArea.width * scale,
        cropArea.height * scale
      )
      
      // Draw crop border
      ctx.strokeStyle = '#3b82f6'
      ctx.lineWidth = 2
      ctx.strokeRect(
        cropArea.x * scale,
        cropArea.y * scale,
        cropArea.width * scale,
        cropArea.height * scale
      )
    }
  }

  const handleRotate = () => {
    setRotation((prev) => (prev + 90) % 360)
  }

  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current
    if (!canvas) return

    const rect = canvas.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    setIsDragging(true)
    setDragStart({ x, y })
  }

  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDragging) return

    const canvas = canvasRef.current
    if (!canvas) return

    const rect = canvas.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    const scale = Math.min(600 / originalDimensions.width, 600 / originalDimensions.height)

    setCropArea({
      x: Math.min(dragStart.x, x) / scale,
      y: Math.min(dragStart.y, y) / scale,
      width: Math.abs(x - dragStart.x) / scale,
      height: Math.abs(y - dragStart.y) / scale,
    })
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  const handleSave = async () => {
    const canvas = canvasRef.current
    const img = imageRef.current
    if (!canvas || !img) return

    // Create a new canvas for the final image
    const outputCanvas = document.createElement('canvas')
    const outputCtx = outputCanvas.getContext('2d')
    if (!outputCtx) return

    let finalWidth = img.width
    let finalHeight = img.height

    // Apply rotation to dimensions
    if (rotation === 90 || rotation === 270) {
      finalWidth = img.height
      finalHeight = img.width
    }

    // Set output canvas size
    if (cropArea) {
      outputCanvas.width = cropArea.width
      outputCanvas.height = cropArea.height
    } else {
      outputCanvas.width = finalWidth
      outputCanvas.height = finalHeight
    }

    // Draw the edited image
    outputCtx.save()

    if (rotation !== 0) {
      outputCtx.translate(outputCanvas.width / 2, outputCanvas.height / 2)
      outputCtx.rotate((rotation * Math.PI) / 180)
      outputCtx.translate(-outputCanvas.width / 2, -outputCanvas.height / 2)
    }

    if (cropArea) {
      outputCtx.drawImage(
        img,
        cropArea.x, cropArea.y, cropArea.width, cropArea.height,
        0, 0, cropArea.width, cropArea.height
      )
    } else {
      outputCtx.drawImage(img, 0, 0, finalWidth, finalHeight)
    }

    outputCtx.restore()

    // Convert to blob
    outputCanvas.toBlob((blob) => {
      if (blob) {
        const editedFilename = filename.replace(/\.[^/.]+$/, '_edited$&')
        onSave(blob, editedFilename)
      }
    }, 'image/png', 0.9)
  }

  const clearCrop = () => {
    setCropArea(null)
  }

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
      <div className="bg-neutral-900 rounded-lg shadow-xl max-w-4xl w-full max-h-full overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-neutral-800">
          <h2 className="text-lg font-semibold text-white">Edit Image</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-neutral-800 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-neutral-400" />
          </button>
        </div>

        {/* Toolbar */}
        <div className="flex items-center gap-2 p-4 border-b border-neutral-800 bg-neutral-800/50">
          <button
            onClick={handleRotate}
            className="flex items-center gap-2 px-3 py-2 bg-neutral-700 hover:bg-neutral-600 rounded-lg transition-colors text-sm"
          >
            <RotateCw className="w-4 h-4" />
            Rotate
          </button>
          
          <button
            onClick={() => setCropArea({ x: 0, y: 0, width: originalDimensions.width, height: originalDimensions.height })}
            className="flex items-center gap-2 px-3 py-2 bg-neutral-700 hover:bg-neutral-600 rounded-lg transition-colors text-sm"
          >
            <Crop className="w-4 h-4" />
            Crop
          </button>

          {cropArea && (
            <button
              onClick={clearCrop}
              className="px-3 py-2 bg-red-600 hover:bg-red-700 rounded-lg transition-colors text-sm"
            >
              Clear Crop
            </button>
          )}

          <div className="flex-1" />

          <button
            onClick={handleSave}
            className="flex items-center gap-2 px-4 py-2 bg-indigo-600 hover:bg-indigo-700 rounded-lg transition-colors text-sm font-medium"
          >
            <Check className="w-4 h-4" />
            Save Changes
          </button>
        </div>

        {/* Canvas */}
        <div className="p-4 flex justify-center bg-neutral-800/30">
          <canvas
            ref={canvasRef}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            className="border border-neutral-600 rounded cursor-crosshair max-w-full max-h-96"
            style={{ imageRendering: 'pixelated' }}
          />
        </div>

        {/* Info */}
        <div className="p-4 border-t border-neutral-800 text-sm text-neutral-400">
          <div className="flex items-center justify-between">
            <span>Original: {originalDimensions.width} × {originalDimensions.height}</span>
            {cropArea && (
              <span>Crop: {Math.round(cropArea.width)} × {Math.round(cropArea.height)}</span>
            )}
            <span>Rotation: {rotation}°</span>
          </div>
          <div className="mt-2 text-xs">
            Click and drag to select crop area. Use toolbar buttons to rotate or crop the image.
          </div>
        </div>
      </div>
    </div>
  )
}

export default ImageEditor
