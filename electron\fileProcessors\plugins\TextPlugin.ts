/**
 * Text File Processing Plugin
 * Core plugin for processing plain text files
 */

import * as fs from 'fs'
import * as path from 'path'
import { FileProcessorPlugin, ProcessedFileContent } from '../types'

export default class TextPlugin implements FileProcessorPlugin {
  name = 'TextPlugin'
  version = '1.0.0'
  description = 'Core plugin for processing plain text files'
  author = 'ChatLo Team'
  optional = false

  supportedTypes = ['text', 'plain']
  supportedExtensions = ['.txt', '.log', '.md', '.json', '.xml', '.csv', '.yaml', '.yml', '.ini', '.conf']

  canProcess(filePath: string, fileType: string): boolean {
    const extension = path.extname(filePath).toLowerCase()
    return this.supportedTypes.includes(fileType) || this.supportedExtensions.includes(extension)
  }

  async process(filePath: string): Promise<ProcessedFileContent> {
    try {
      const stats = await fs.promises.stat(filePath)
      
      // Check file size (limit to 10MB for text files)
      const maxSize = 10 * 1024 * 1024 // 10MB
      if (stats.size > maxSize) {
        return {
          error: `Text file too large: ${Math.round(stats.size / 1024 / 1024)}MB (max: 10MB)`
        }
      }

      const content = await fs.promises.readFile(filePath, 'utf8')
      
      return {
        text: content,
        metadata: {
          encoding: 'utf8',
          lines: content.split('\n').length,
          characters: content.length,
          words: content.split(/\s+/).filter(word => word.length > 0).length,
          fileSize: stats.size,
          lastModified: stats.mtime,
          extension: path.extname(filePath),
          processor: this.name
        }
      }
    } catch (error: any) {
      console.error('Error processing text file:', error)
      return {
        error: `Failed to process text file: ${error?.message || 'Unknown error'}`
      }
    }
  }
}
