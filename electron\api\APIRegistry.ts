/**
 * API Registry
 * Central registry for all API endpoints with plugin support
 */

import { ipcMain } from 'electron'
import { DefaultMiddlewareStack, MiddlewareContext, MiddlewareFunction } from './middleware'
import { APIMonitor } from './monitoring'
import { APIValidator, SecurityManager, ValidationSchema, SecurityContext } from './validation'
import { ErrorHandler, StructuredAPIError, ErrorCode, APIResponse } from './errorHandling'

export interface APIEndpoint {
  handler: Function
  validator?: Function
  validationSchema?: ValidationSchema
  middleware?: Function[]
  description?: string
  requiresAuth?: boolean
  requiredPermission?: string
  rateLimit?: { maxRequests: number; windowMs: number }
}

export interface APICategory {
  name: string
  endpoints: Map<string, APIEndpoint>
  middleware?: Function[]
}

export class APIRegistry {
  private categories: Map<string, APICategory> = new Map()
  private globalMiddleware: Function[] = []
  private middlewareStack: DefaultMiddlewareStack
  private monitor: APIMonitor
  private securityManager: SecurityManager

  constructor(middlewareOptions?: {
    logging?: any
    rateLimiting?: any
    security?: any
    errorHandling?: any
  }) {
    this.middlewareStack = new DefaultMiddlewareStack(middlewareOptions)
    this.monitor = new APIMonitor()
    this.securityManager = new SecurityManager()

    // Add default global middleware
    const globalMiddleware = this.middlewareStack.getGlobalMiddleware()
    this.globalMiddleware.push(...globalMiddleware)
  }

  // Add global middleware
  addGlobalMiddleware(middleware: Function): void {
    this.globalMiddleware.push(middleware)
  }
  
  // Register a new API category
  registerCategory(name: string, middleware?: Function[]): void {
    if (!this.categories.has(name)) {
      this.categories.set(name, {
        name,
        endpoints: new Map(),
        middleware: middleware || []
      })
    }
  }
  
  // Register an endpoint in a category
  registerEndpoint(
    category: string,
    name: string,
    handler: Function,
    options?: {
      validator?: Function
      validationSchema?: ValidationSchema
      middleware?: Function[]
      description?: string
      requiresAuth?: boolean
      requiredPermission?: string
      rateLimit?: { maxRequests: number; windowMs: number }
    }
  ): void {
    if (!this.categories.has(category)) {
      this.registerCategory(category)
    }

    const categoryObj = this.categories.get(category)!
    categoryObj.endpoints.set(name, {
      handler,
      validator: options?.validator,
      validationSchema: options?.validationSchema,
      middleware: options?.middleware || [],
      description: options?.description,
      requiresAuth: options?.requiresAuth,
      requiredPermission: options?.requiredPermission || this.securityManager.getRequiredPermission(category, name) || undefined,
      rateLimit: options?.rateLimit
    })
  }
  
  // Initialize all API endpoints with Electron IPC
  initialize(): void {
    console.log('[APIRegistry] Initializing API endpoints...')
    console.log('[APIRegistry] Categories:', Array.from(this.categories.keys()))

    for (const [categoryName, category] of this.categories.entries()) {
      console.log(`[APIRegistry] Processing category: ${categoryName}`)
      console.log(`[APIRegistry] Endpoints in ${categoryName}:`, Array.from(category.endpoints.keys()))

      for (const [endpointName, endpoint] of category.endpoints.entries()) {
        const channelName = `${categoryName}:${endpointName}`
        console.log(`[APIRegistry] Registering IPC handler: ${channelName}`)
        
        ipcMain.handle(channelName, async (event, ...args) => {
          // Create middleware context outside try block so it's available in catch
          const context: MiddlewareContext = {
            event,
            category: categoryName,
            endpoint: endpointName,
            args,
            startTime: Date.now(),
            metadata: {}
          }

          try {
            // Security validation
            if (!this.validateSender(event.sender)) {
              throw new Error('Unauthorized sender')
            }

            // Create security context for development (in production, this would come from authentication)
            const securityContext: SecurityContext = this.securityManager.createDefaultContext()

            // Check authentication if required
            if (endpoint.requiresAuth && !securityContext.userId) {
              throw new Error('Authentication required')
            }

            // Check permissions
            if (endpoint.requiredPermission && !this.securityManager.hasPermission(securityContext, endpoint.requiredPermission)) {
              throw new Error(`Permission denied: ${endpoint.requiredPermission} required`)
            }

            // Check rate limits
            if (endpoint.rateLimit) {
              const rateLimitKey = `${event.sender.id}:${categoryName}:${endpointName}`
              if (!this.securityManager.checkRateLimit(rateLimitKey, endpoint.rateLimit.maxRequests, endpoint.rateLimit.windowMs)) {
                throw new Error('Rate limit exceeded')
              }
            }

            // Validate arguments using schema
            if (endpoint.validationSchema) {
              const validation = APIValidator.validate(args, endpoint.validationSchema)
              if (!validation.valid) {
                const errorMessages = validation.errors.map(e => `${e.field}: ${e.message}`).join(', ')
                throw new Error(`Validation failed: ${errorMessages}`)
              }
            }

            // Sanitize input for security
            const sanitizedArgs = args.map(arg => this.securityManager.sanitizeInput(arg))

            // Run global middleware
            for (const middleware of this.globalMiddleware) {
              if (typeof middleware === 'function') {
                if (middleware.length === 1) {
                  // New middleware format with context
                  await middleware(context)
                } else {
                  // Legacy middleware format
                  await middleware(event, ...args)
                }
              }
            }

            // Run category middleware
            if (category.middleware) {
              for (const middleware of category.middleware) {
                if (typeof middleware === 'function') {
                  if (middleware.length === 1) {
                    await middleware(context)
                  } else {
                    await middleware(event, ...args)
                  }
                }
              }
            }

            // Run endpoint middleware
            if (endpoint.middleware) {
              for (const middleware of endpoint.middleware) {
                if (typeof middleware === 'function') {
                  if (middleware.length === 1) {
                    await middleware(context)
                  } else {
                    await middleware(event, ...args)
                  }
                }
              }
            }
            
            // Legacy validator (for backward compatibility)
            if (endpoint.validator) {
              endpoint.validator(...sanitizedArgs)
            }

            // Call the handler with sanitized arguments
            const callStartTime = process.hrtime.bigint()
            const result = await endpoint.handler(...sanitizedArgs)
            const callEndTime = process.hrtime.bigint()
            const duration = Number(callEndTime - callStartTime) / 1000000 // Convert to ms

            // Record successful call in monitor
            this.monitor.recordCall(categoryName, endpointName, duration, true)

            // Run response middleware
            const responseMiddleware = this.middlewareStack.getResponseMiddleware()
            for (const middleware of responseMiddleware) {
              await middleware(context)
            }

            return result

          } catch (error: any) {
            // Create structured error
            const structuredError = ErrorHandler.handleError(error, {
              category: categoryName,
              endpoint: endpointName,
              additionalContext: {
                args: args,
                channelName: channelName
              }
            })

            // Record failed call in monitor
            const callEndTime = Date.now()
            const duration = callEndTime - context.startTime
            this.monitor.recordCall(categoryName, endpointName, duration, false, structuredError.message)

            // Handle error with middleware
            const errorHandler = this.middlewareStack.getErrorHandler()
            const middlewareResult = errorHandler.handleError(structuredError, context)

            // Return structured error response
            return structuredError.toResponse(`${Date.now()}-${Math.random().toString(36).substring(2, 11)}`)
          }
        })
      }
    }
  }
  
  // Get all registered endpoints
  getAllEndpoints(): Array<{category: string, name: string, description?: string}> {
    const endpoints: Array<{category: string, name: string, description?: string}> = []
    
    for (const [categoryName, category] of this.categories.entries()) {
      for (const [endpointName, endpoint] of category.endpoints.entries()) {
        endpoints.push({
          category: categoryName,
          name: endpointName,
          description: endpoint.description
        })
      }
    }
    
    return endpoints
  }
  
  // Validate sender (security check)
  private validateSender(sender: any): boolean {
    // Implement your security validation logic here
    // For now, we'll allow all senders from the main window
    return true
  }

  // Get performance metrics from middleware
  getPerformanceMetrics(): Record<string, any> {
    return this.middlewareStack.getPerformanceMetrics()
  }

  // Cleanup middleware resources
  cleanup(): void {
    this.middlewareStack.cleanup()
    this.monitor.cleanup()
  }

  // Get monitoring data
  getMonitoringData(): any {
    return {
      metrics: this.monitor.getAllMetrics(),
      systemHealth: this.monitor.getSystemHealth(),
      activeAlerts: this.monitor.getActiveAlerts(),
      topEndpoints: {
        byCalls: this.monitor.getTopEndpoints('calls', 5),
        byErrors: this.monitor.getTopEndpoints('errors', 5),
        byTime: this.monitor.getTopEndpoints('time', 5)
      }
    }
  }

  // Get specific endpoint metrics
  getEndpointMetrics(category: string, endpoint: string): any {
    return this.monitor.getEndpointMetrics(category, endpoint)
  }

  // Reset monitoring data
  resetMonitoring(): void {
    this.monitor.resetMetrics()
  }

  // Validation helper methods
  createFilePathValidator(): ValidationSchema {
    return APIValidator.createFilePathSchema()
  }

  createPluginIdValidator(): ValidationSchema {
    return APIValidator.createPluginIdSchema()
  }

  createDatabaseQueryValidator(): ValidationSchema {
    return APIValidator.createDatabaseQuerySchema()
  }

  // Security helper methods
  getSecurityManager(): SecurityManager {
    return this.securityManager
  }

  // Error handling methods
  getErrorStatistics(): any {
    return ErrorHandler.getErrorStatistics()
  }

  clearErrorHistory(): void {
    ErrorHandler.clearErrorHistory()
  }

  // Get all registered categories and endpoints for debugging
  getRegistryInfo(): any {
    const info: any = {}
    for (const [categoryName, category] of this.categories.entries()) {
      info[categoryName] = {
        endpoints: Array.from(category.endpoints.keys()),
        middlewareCount: category.middleware?.length || 0
      }
    }
    return {
      categories: info,
      globalMiddlewareCount: this.globalMiddleware.length
    }
  }
}