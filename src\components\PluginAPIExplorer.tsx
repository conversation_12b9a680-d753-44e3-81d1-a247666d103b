/**
 * Plugin API Explorer Component
 * Provides a UI for discovering and testing plugin API endpoints
 */

import React, { useState, useEffect } from 'react'
import { pluginAPIDiscovery, PluginAPIInfo, PluginEndpointInfo } from '../services/PluginAPIDiscovery'

interface APICallResult {
  success: boolean
  data?: any
  error?: string
  timestamp: string
}

export const PluginAPIExplorer: React.FC = () => {
  const [apiInfo, setApiInfo] = useState<PluginAPIInfo[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedPlugin, setSelectedPlugin] = useState<string>('')
  const [selectedEndpoint, setSelectedEndpoint] = useState<string>('')
  const [apiArgs, setApiArgs] = useState<string>('')
  const [callResult, setCallResult] = useState<APICallResult | null>(null)
  const [calling, setCalling] = useState(false)

  useEffect(() => {
    loadPluginAPIs()
  }, [])

  const loadPluginAPIs = async () => {
    setLoading(true)
    try {
      const apis = await pluginAPIDiscovery.discoverPluginAPIs(true)
      setApiInfo(apis)
      
      // Auto-select first plugin if available
      if (apis.length > 0 && !selectedPlugin) {
        setSelectedPlugin(apis[0].pluginId)
      }
    } catch (error) {
      console.error('Error loading plugin APIs:', error)
    } finally {
      setLoading(false)
    }
  }

  const getSelectedPluginInfo = (): PluginAPIInfo | undefined => {
    return apiInfo.find(api => api.pluginId === selectedPlugin)
  }

  const getSelectedEndpointInfo = (): PluginEndpointInfo | undefined => {
    const pluginInfo = getSelectedPluginInfo()
    return pluginInfo?.endpoints.find(ep => ep.name === selectedEndpoint)
  }

  const callPluginAPI = async () => {
    if (!selectedPlugin || !selectedEndpoint) {
      return
    }

    setCalling(true)
    setCallResult(null)

    try {
      let args: any[] = []
      
      // Parse arguments if provided
      if (apiArgs.trim()) {
        try {
          args = JSON.parse(`[${apiArgs}]`)
        } catch (parseError) {
          // If JSON parsing fails, treat as single string argument
          args = [apiArgs]
        }
      }

      const result = await pluginAPIDiscovery.callPluginAPI(selectedPlugin, selectedEndpoint, ...args)
      
      setCallResult({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      })
    } catch (error: any) {
      setCallResult({
        success: false,
        error: error.message || 'Unknown error',
        timestamp: new Date().toISOString()
      })
    } finally {
      setCalling(false)
    }
  }

  const generateDocumentation = () => {
    const doc = pluginAPIDiscovery.generateAPIDocumentation(apiInfo)
    
    // Create a new window/tab with the documentation
    const newWindow = window.open('', '_blank')
    if (newWindow) {
      newWindow.document.write(`
        <html>
          <head>
            <title>Plugin API Documentation</title>
            <style>
              body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 40px; line-height: 1.6; }
              pre { background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto; }
              code { background: #f0f0f0; padding: 2px 4px; border-radius: 3px; }
              h1, h2, h3, h4 { color: #333; }
              hr { border: none; border-top: 1px solid #eee; margin: 30px 0; }
            </style>
          </head>
          <body>
            <div id="content"></div>
            <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
            <script>
              document.getElementById('content').innerHTML = marked.parse(\`${doc.replace(/`/g, '\\`')}\`);
            </script>
          </body>
        </html>
      `)
      newWindow.document.close()
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span className="ml-3">Loading plugin APIs...</span>
      </div>
    )
  }

  const selectedPluginInfo = getSelectedPluginInfo()
  const selectedEndpointInfo = getSelectedEndpointInfo()

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Plugin API Explorer</h1>
        <div className="space-x-2">
          <button
            onClick={loadPluginAPIs}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Refresh
          </button>
          <button
            onClick={generateDocumentation}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
            disabled={apiInfo.length === 0}
          >
            Generate Docs
          </button>
        </div>
      </div>

      {apiInfo.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <p>No plugins with API extensions found.</p>
          <p className="text-sm mt-2">Make sure you have plugins with API_EXTENSION capability installed.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Plugin Selection */}
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Select Plugin</label>
              <select
                value={selectedPlugin}
                onChange={(e) => {
                  setSelectedPlugin(e.target.value)
                  setSelectedEndpoint('')
                  setCallResult(null)
                }}
                className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Choose a plugin...</option>
                {apiInfo.map(api => (
                  <option key={api.pluginId} value={api.pluginId}>
                    {api.pluginName || api.pluginId} (v{api.version || 'unknown'})
                  </option>
                ))}
              </select>
            </div>

            {selectedPluginInfo && (
              <div>
                <label className="block text-sm font-medium mb-2">Select Endpoint</label>
                <select
                  value={selectedEndpoint}
                  onChange={(e) => {
                    setSelectedEndpoint(e.target.value)
                    setCallResult(null)
                  }}
                  className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Choose an endpoint...</option>
                  {selectedPluginInfo.endpoints.map(ep => (
                    <option key={ep.name} value={ep.name}>
                      {ep.name} {ep.description ? `- ${ep.description}` : ''}
                    </option>
                  ))}
                </select>
              </div>
            )}

            {selectedEndpointInfo && (
              <div>
                <label className="block text-sm font-medium mb-2">Arguments (JSON format)</label>
                <textarea
                  value={apiArgs}
                  onChange={(e) => setApiArgs(e.target.value)}
                  placeholder='e.g., "Hello World" or {"key": "value"}, [1, 2, 3]'
                  className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 h-20"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Enter arguments separated by commas. Strings, objects, and arrays are supported.
                </p>
              </div>
            )}

            <button
              onClick={callPluginAPI}
              disabled={!selectedPlugin || !selectedEndpoint || calling}
              className="w-full px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
            >
              {calling ? 'Calling...' : 'Call API'}
            </button>
          </div>

          {/* Results */}
          <div className="space-y-4">
            {selectedPluginInfo && (
              <div className="bg-gray-50 p-4 rounded">
                <h3 className="font-medium mb-2">Plugin Info</h3>
                <p><strong>Name:</strong> {selectedPluginInfo.pluginName || selectedPluginInfo.pluginId}</p>
                <p><strong>Namespace:</strong> <code>{selectedPluginInfo.namespace}</code></p>
                <p><strong>Version:</strong> {selectedPluginInfo.version || 'unknown'}</p>
                <p><strong>Endpoints:</strong> {selectedPluginInfo.endpoints.length}</p>
              </div>
            )}

            {selectedEndpointInfo && (
              <div className="bg-blue-50 p-4 rounded">
                <h3 className="font-medium mb-2">Endpoint Info</h3>
                <p><strong>Name:</strong> {selectedEndpointInfo.name}</p>
                {selectedEndpointInfo.description && (
                  <p><strong>Description:</strong> {selectedEndpointInfo.description}</p>
                )}
                <p><strong>Has Validation:</strong> {selectedEndpointInfo.hasValidator ? 'Yes' : 'No'}</p>
                <p><strong>Has Middleware:</strong> {selectedEndpointInfo.hasMiddleware ? 'Yes' : 'No'}</p>
              </div>
            )}

            {callResult && (
              <div className={`p-4 rounded ${callResult.success ? 'bg-green-50' : 'bg-red-50'}`}>
                <h3 className="font-medium mb-2">
                  {callResult.success ? 'Success' : 'Error'}
                  <span className="text-sm font-normal text-gray-500 ml-2">
                    {new Date(callResult.timestamp).toLocaleTimeString()}
                  </span>
                </h3>
                <pre className="text-sm bg-white p-3 rounded border overflow-x-auto">
                  {callResult.success 
                    ? JSON.stringify(callResult.data, null, 2)
                    : callResult.error
                  }
                </pre>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default PluginAPIExplorer
