/**
 * Plugin Architecture for File Processing
 * Modular system for handling different file types
 */

export interface ProcessedFileContent {
  text?: string
  metadata?: any
  error?: string
}

export interface FileProcessorPlugin {
  // Plugin identification
  name: string
  version: string
  supportedTypes: string[]
  supportedExtensions: string[]
  
  // Plugin capabilities
  canProcess(filePath: string, fileType: string): boolean
  
  // Main processing method
  process(filePath: string): Promise<ProcessedFileContent>
  
  // Plugin lifecycle
  initialize?(): Promise<void>
  cleanup?(): Promise<void>
  
  // Plugin metadata
  description?: string
  author?: string
  dependencies?: string[]
  optional?: boolean // If true, plugin failure won't break the system
}

export interface PluginRegistry {
  plugins: Map<string, FileProcessorPlugin>
  typeMap: Map<string, FileProcessorPlugin[]> // Maps file types to plugins
  extensionMap: Map<string, FileProcessorPlugin[]> // Maps extensions to plugins
}

export interface PluginConfig {
  enabled: boolean
  priority: number
  options?: Record<string, any>
}

export interface FileProcessorConfig {
  plugins: Record<string, PluginConfig>
  fallbackEnabled: boolean
  maxRetries: number
  timeout: number
}

export class PluginError extends Error {
  constructor(
    message: string,
    public pluginName: string,
    public originalError?: Error
  ) {
    super(message)
    this.name = 'PluginError'
  }
}

export class PluginManager {
  private registry: PluginRegistry = {
    plugins: new Map(),
    typeMap: new Map(),
    extensionMap: new Map()
  }
  
  private config: FileProcessorConfig = {
    plugins: {},
    fallbackEnabled: true,
    maxRetries: 3,
    timeout: 30000
  }

  constructor(config?: Partial<FileProcessorConfig>) {
    if (config) {
      this.config = { ...this.config, ...config }
    }
  }

  // Register a plugin
  async registerPlugin(plugin: FileProcessorPlugin): Promise<void> {
    try {
      // Initialize plugin if needed
      if (plugin.initialize) {
        await plugin.initialize()
      }

      // Register in main registry
      this.registry.plugins.set(plugin.name, plugin)

      // Register by supported types
      for (const type of plugin.supportedTypes) {
        if (!this.registry.typeMap.has(type)) {
          this.registry.typeMap.set(type, [])
        }
        this.registry.typeMap.get(type)!.push(plugin)
      }

      // Register by supported extensions
      for (const ext of plugin.supportedExtensions) {
        if (!this.registry.extensionMap.has(ext)) {
          this.registry.extensionMap.set(ext, [])
        }
        this.registry.extensionMap.get(ext)!.push(plugin)
      }

      console.log(`Plugin registered: ${plugin.name} v${plugin.version}`)
    } catch (error) {
      console.error(`Failed to register plugin ${plugin.name}:`, error)
      throw new PluginError(`Failed to register plugin ${plugin.name}`, plugin.name, error as Error)
    }
  }

  // Unregister a plugin
  async unregisterPlugin(pluginName: string): Promise<void> {
    const plugin = this.registry.plugins.get(pluginName)
    if (!plugin) return

    try {
      // Cleanup plugin if needed
      if (plugin.cleanup) {
        await plugin.cleanup()
      }

      // Remove from main registry
      this.registry.plugins.delete(pluginName)

      // Remove from type maps
      for (const [type, plugins] of this.registry.typeMap.entries()) {
        const index = plugins.findIndex(p => p.name === pluginName)
        if (index !== -1) {
          plugins.splice(index, 1)
          if (plugins.length === 0) {
            this.registry.typeMap.delete(type)
          }
        }
      }

      // Remove from extension maps
      for (const [ext, plugins] of this.registry.extensionMap.entries()) {
        const index = plugins.findIndex(p => p.name === pluginName)
        if (index !== -1) {
          plugins.splice(index, 1)
          if (plugins.length === 0) {
            this.registry.extensionMap.delete(ext)
          }
        }
      }

      console.log(`Plugin unregistered: ${pluginName}`)
    } catch (error) {
      console.error(`Failed to unregister plugin ${pluginName}:`, error)
      throw new PluginError(`Failed to unregister plugin ${pluginName}`, pluginName, error as Error)
    }
  }

  // Get plugins for a file type
  getPluginsForType(fileType: string): FileProcessorPlugin[] {
    return this.registry.typeMap.get(fileType) || []
  }

  // Get plugins for a file extension
  getPluginsForExtension(extension: string): FileProcessorPlugin[] {
    return this.registry.extensionMap.get(extension) || []
  }

  // Get all registered plugins
  getAllPlugins(): FileProcessorPlugin[] {
    return Array.from(this.registry.plugins.values())
  }

  // Check if a file type is supported
  isTypeSupported(fileType: string): boolean {
    return this.registry.typeMap.has(fileType)
  }

  // Get plugin configuration
  getPluginConfig(pluginName: string): PluginConfig | undefined {
    return this.config.plugins[pluginName]
  }

  // Update plugin configuration
  updatePluginConfig(pluginName: string, config: Partial<PluginConfig>): void {
    if (!this.config.plugins[pluginName]) {
      this.config.plugins[pluginName] = {
        enabled: true,
        priority: 0
      }
    }
    this.config.plugins[pluginName] = {
      ...this.config.plugins[pluginName],
      ...config
    }
  }

  // Cleanup all plugins
  async cleanup(): Promise<void> {
    const cleanupPromises = Array.from(this.registry.plugins.values())
      .filter(plugin => plugin.cleanup)
      .map(plugin => plugin.cleanup!())

    await Promise.allSettled(cleanupPromises)
    
    this.registry.plugins.clear()
    this.registry.typeMap.clear()
    this.registry.extensionMap.clear()
  }
}
