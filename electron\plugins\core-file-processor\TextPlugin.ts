/**
 * Migrated Text Processing Plugin
 * Now uses the universal plugin framework
 */

import * as fs from 'fs'
import * as path from 'path'
import { BasePlugin, PluginCapability } from '../types'
import { FileProcessorExtension } from '../extensionPoints'

export default class TextPlugin implements BasePlugin, FileProcessorExtension {
  id = 'core-file-processor'
  name = 'Text File Processor'
  version = '2.0.0'
  description = 'Core plugin for processing plain text files'
  author = 'ChatLo Team'
  
  supportedTypes = ['text', 'plain']
  supportedExtensions = ['.txt', '.log', '.md', '.json', '.xml', '.csv', '.yaml', '.yml', '.ini', '.conf']
  
  async initialize(): Promise<void> {
    console.log('TextPlugin v2.0.0 initialized')
  }
  
  getCapabilities(): PluginCapability[] {
    return [PluginCapability.FILE_PROCESSING]
  }
  
  getDefaultConfig(): Record<string, any> {
    return {
      maxFileSize: 10 * 1024 * 1024, // 10MB
      encoding: 'utf8'
    }
  }
  
  canProcess(filePath: string, fileType: string): boolean {
    const extension = path.extname(filePath).toLowerCase()
    return this.supportedTypes.includes(fileType) || this.supportedExtensions.includes(extension)
  }
  
  async process(filePath: string): Promise<any> {
    try {
      const stats = await fs.promises.stat(filePath)
      const config = this.getDefaultConfig()
      
      if (stats.size > config.maxFileSize) {
        return {
          error: `File too large: ${Math.round(stats.size / 1024 / 1024)}MB (max: ${config.maxFileSize / 1024 / 1024}MB)`
        }
      }
      
      // Explicitly specify encoding to get string instead of Buffer
      const content = await fs.promises.readFile(filePath, { encoding: config.encoding as BufferEncoding })
      
      return {
        text: content,
        metadata: {
          fileSize: stats.size,
          lastModified: stats.mtime,
          encoding: config.encoding,
          lines: content.split('\n').length,
          characters: content.length,
          words: content.split(/\s+/).filter((word: string) => word.length > 0).length,
          processor: this.name,
          version: this.version
        }
      }
    } catch (error: any) {
      return {
        error: `Failed to process text file: ${error.message}`
      }
    }
  }
}