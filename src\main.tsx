import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom/client'
import App from './App'
import './index.css'
import { initializeApp } from './store'

// Check if Electron API is available (only in development)
if ((import.meta as any).env?.DEV) {
  console.log('Window electronAPI available:', !!window.electronAPI)
  if (window.electronAPI) {
    console.log('ElectronAPI methods:', Object.keys(window.electronAPI))
  } else {
    console.warn('ElectronAPI not available - running in browser mode')
  }
}

// Initialize the app
initializeApp().catch(console.error)

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
