import React, { useState } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faUpload, faFile, faCheckCircle, faExclamationTriangle, faSpinner } from '@fortawesome/free-solid-svg-icons'

interface TestResult {
  success: boolean
  filePath?: string
  fileType?: string
  textLength?: number
  firstChars?: string
  metadata?: any
  error?: string
}

const DirectParsingPopup: React.FC = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [testResult, setTestResult] = useState<TestResult | null>(null)

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setSelectedFile(file)
      setTestResult(null) // Clear previous results
    }
  }

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault()
    
    if (!selectedFile) {
      alert('Please select a file first')
      return
    }

    setIsProcessing(true)
    setTestResult(null)

    try {
      // Get the file path - in Electron, we can access the path property
      const filePath = (selectedFile as any).path || selectedFile.name
      
      console.log('Testing direct parsing for file:', filePath)
      
      // Call the testDirectParsing function through the Electron API
      const result = await window.electronAPI.files.testDirectParsing(filePath)
      
      console.log('Direct parsing test result:', result)
      setTestResult(result)
      
    } catch (error) {
      console.error('Error during direct parsing test:', error)
      setTestResult({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    } finally {
      setIsProcessing(false)
    }
  }

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault()
  }

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault()
    const files = event.dataTransfer.files
    if (files.length > 0) {
      setSelectedFile(files[0])
      setTestResult(null)
    }
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-primary mb-2">Direct File Parsing Test</h1>
          <p className="text-supplement1">
            Test the direct file parsing functionality by uploading a file and seeing the extracted content.
          </p>
        </div>

        {/* File Upload Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* File Drop Zone */}
          <div
            className={`
              border-2 border-dashed rounded-lg p-8 text-center transition-colors
              ${selectedFile 
                ? 'border-primary bg-primary/10' 
                : 'border-tertiary hover:border-primary/50 hover:bg-gray-800/50'
              }
            `}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
          >
            {selectedFile ? (
              <div className="space-y-3">
                <FontAwesomeIcon icon={faFile} className="text-4xl text-primary" />
                <div>
                  <p className="font-medium text-supplement1">{selectedFile.name}</p>
                  <p className="text-sm text-gray-400">
                    {formatFileSize(selectedFile.size)} • {selectedFile.type || 'Unknown type'}
                  </p>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <FontAwesomeIcon icon={faUpload} className="text-4xl text-gray-400" />
                <div>
                  <p className="text-supplement1 mb-2">Drop a file here or click to browse</p>
                  <p className="text-sm text-gray-400">
                    Supports: PDF, Word, Excel, PowerPoint, Text, Markdown, Images, and more
                  </p>
                </div>
              </div>
            )}
            
            <input
              type="file"
              onChange={handleFileSelect}
              className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
              accept="*/*"
            />
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={!selectedFile || isProcessing}
            className={`
              w-full py-3 px-6 rounded-lg font-medium transition-colors flex items-center justify-center gap-2
              ${!selectedFile || isProcessing
                ? 'bg-gray-700 text-gray-400 cursor-not-allowed'
                : 'bg-primary text-gray-900 hover:bg-primary/80'
              }
            `}
          >
            {isProcessing ? (
              <>
                <FontAwesomeIcon icon={faSpinner} className="animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <FontAwesomeIcon icon={faUpload} />
                Test Direct Parsing
              </>
            )}
          </button>
        </form>

        {/* Results Section */}
        {testResult && (
          <div className="mt-8 p-6 bg-gray-800 rounded-lg">
            <div className="flex items-center gap-3 mb-4">
              {testResult.success ? (
                <FontAwesomeIcon icon={faCheckCircle} className="text-green-400 text-xl" />
              ) : (
                <FontAwesomeIcon icon={faExclamationTriangle} className="text-red-400 text-xl" />
              )}
              <h3 className="text-lg font-semibold">
                {testResult.success ? 'Parsing Successful!' : 'Parsing Failed'}
              </h3>
            </div>

            {testResult.success ? (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-400">File Type:</span>
                    <span className="ml-2 text-supplement1">{testResult.fileType}</span>
                  </div>
                  <div>
                    <span className="text-gray-400">Text Length:</span>
                    <span className="ml-2 text-supplement1">{testResult.textLength?.toLocaleString()} characters</span>
                  </div>
                </div>
                
                {testResult.firstChars && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-400 mb-2">First 500 Characters:</h4>
                    <div className="bg-gray-900 p-3 rounded text-sm font-mono text-supplement1 max-h-32 overflow-y-auto">
                      {testResult.firstChars}
                    </div>
                  </div>
                )}

                {testResult.metadata && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-400 mb-2">Metadata:</h4>
                    <div className="bg-gray-900 p-3 rounded text-sm font-mono text-supplement1 max-h-32 overflow-y-auto">
                      <pre>{JSON.stringify(testResult.metadata, null, 2)}</pre>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-red-400">
                <p className="font-medium">Error:</p>
                <p className="text-sm mt-1">{testResult.error}</p>
              </div>
            )}
          </div>
        )}

        {/* Instructions */}
        <div className="mt-8 p-4 bg-gray-800/50 rounded-lg">
          <h3 className="font-medium text-supplement1 mb-2">How to use:</h3>
          <ol className="text-sm text-gray-400 space-y-1 list-decimal list-inside">
            <li>Select a file by clicking the upload area or dragging and dropping</li>
            <li>Click "Test Direct Parsing" to process the file</li>
            <li>View the extracted text content and metadata</li>
            <li>Check the console for detailed logging information</li>
          </ol>
        </div>
      </div>
    </div>
  )
}

export default DirectParsingPopup
