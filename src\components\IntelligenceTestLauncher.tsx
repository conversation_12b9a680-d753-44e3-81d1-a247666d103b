import React from 'react'

/**
 * Simple launcher for the Intelligence Test popup window
 * Digital Twin approach - isolated testing environment
 */
export const IntelligenceTestLauncher: React.FC = () => {
  const openTestWindow = () => {
    // Open the VSCode-style admin dashboard
    const adminUrl = `${window.location.origin}/admin-dashboard.html`
    const adminWindow = window.open(
      adminUrl,
      'chatloAdmin',
      'width=1400,height=900,scrollbars=yes,resizable=yes,menubar=no,toolbar=no,location=no,status=no'
    )
    
    if (adminWindow) {
      adminWindow.focus()
    } else {
      alert('Please allow popups for this site to open the admin dashboard')
    }
  }

  return (
    <div className="bg-gray-800 rounded-lg p-6">
      <div className="mb-6">
        <h2 className="text-xl font-bold text-white mb-2">🧠 Intelligence System Test</h2>
        <p className="text-gray-400">Test the intelligence extraction and performance monitoring systems.</p>
      </div>

      <div className="bg-blue-600/10 border border-blue-600/30 rounded-lg p-4 mb-6">
        <div className="flex items-center gap-3 mb-3">
          <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
            🚀
          </div>
          <div>
            <h3 className="font-semibold text-blue-400">VSCode-Style Admin Dashboard</h3>
            <p className="text-sm text-gray-400">Extension-based admin interface with modular architecture</p>
          </div>
        </div>
        
        <div className="space-y-2 text-sm text-gray-300 mb-4">
          <div className="flex items-center gap-2">
            <span className="w-2 h-2 bg-green-400 rounded-full"></span>
            <span>VSCode-style left vertical bar with extensions</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="w-2 h-2 bg-green-400 rounded-full"></span>
            <span>Modular plugin architecture for future expansion</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="w-2 h-2 bg-green-400 rounded-full"></span>
            <span>Intelligence testing, system prompts, pipeline config</span>
          </div>
          <div className="flex items-center gap-2">
            <span className="w-2 h-2 bg-green-400 rounded-full"></span>
            <span>Performance monitoring and deployment management</span>
          </div>
        </div>

        <button
          onClick={openTestWindow}
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2"
        >
          <span>🏛️</span>
          Open Admin Dashboard
        </button>
      </div>

      <div className="bg-gray-700/50 rounded-lg p-4">
        <h3 className="font-semibold text-gray-300 mb-2">How It Works:</h3>
        <ol className="text-sm text-gray-400 space-y-1 list-decimal list-inside">
          <li>Click "Open Admin Dashboard" to launch the VSCode-style interface</li>
          <li>Use the left vertical bar to switch between extensions</li>
          <li>🧪 Intelligence Testing - Test LLM models with real content</li>
          <li>📝 System Prompts - Manage prompts and templates</li>
          <li>⚙️ Pipeline Config - Configure processing pipelines</li>
          <li>📊 Performance Monitor - Track system metrics</li>
          <li>🚀 Deployment Manager - Handle deployments and rollbacks</li>
        </ol>
      </div>

      <div className="mt-4 p-3 bg-yellow-600/10 border border-yellow-600/30 rounded-lg">
        <div className="flex items-center gap-2 text-yellow-400 text-sm">
          <span>⚡</span>
          <span className="font-medium">VSCode Extension Architecture Benefits:</span>
        </div>
        <ul className="text-xs text-gray-400 mt-2 space-y-1">
          <li>• Modular design - each feature is an independent extension</li>
          <li>• Easy to add new functionality without touching core</li>
          <li>• Community can develop custom extensions</li>
          <li>• Familiar interface for developers</li>
        </ul>
      </div>
    </div>
  )
}
