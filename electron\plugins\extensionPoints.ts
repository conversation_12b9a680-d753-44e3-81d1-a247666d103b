/**
 * Extension Points
 * Defines all available hooks for plugins to extend functionality
 */

// Type definitions
export interface ProcessedFileContent {
  text?: string
  metadata?: Record<string, any>
  error?: string
}

export interface MenuItem {
  id: string
  label: string
  icon?: string
  onClick: () => void
  submenu?: MenuItem[]
}

export interface ToolbarItem {
  id: string
  label: string
  icon?: string
  onClick: () => void
  tooltip?: string
}

export interface SettingsPanel {
  id: string
  title: string
  component: React.ComponentType<any>
}

export interface APIRegistry {
  registerCategory: (category: string, middleware?: Function[]) => void
  registerEndpoint: (category: string, endpoint: string, handler: Function, options?: {
    validator?: Function
    middleware?: Function[]
    description?: string
  }) => void
  addGlobalMiddleware: (middleware: Function) => void
}

export interface Middleware {
  name: string
  handler: (req: any, res: any, next: Function) => void
}

export interface PluginEndpoint {
  name: string
  handler: Function
  validator?: Function
  description?: string
  middleware?: Function[]
}

export interface PluginAPINamespace {
  pluginId: string
  namespace: string // Will be `plugin_${pluginId}`
  endpoints: PluginEndpoint[]
  middleware: Function[]
}

// File Processing Extension Point (existing, to be refactored)
export interface FileProcessorExtension {
  supportedTypes: string[]
  supportedExtensions: string[]
  canProcess(filePath: string, fileType: string): boolean
  process(filePath: string): Promise<ProcessedFileContent>
}

// Chat Enhancement Extension Point
export interface ChatExtension {
  // Message processing hooks
  beforeMessageSend?(message: any): Promise<any>
  afterMessageReceived?(message: any): Promise<any>

  // Context enhancement
  provideContextData?(): Promise<any>
  enhancePrompt?(prompt: string, context: any): Promise<string>

  // Response processing
  processResponse?(response: any): Promise<any>
}

// Model Integration Extension Point
export interface ModelExtension {
  // Model discovery and registration
  discoverModels?(): Promise<any[]>

  // Request/response interception
  interceptModelRequest?(request: any): Promise<any>
  processModelResponse?(response: any): Promise<any>

  // Custom model providers
  provideCustomModel?(): Promise<any>
}

// UI Extension Point
export interface UIExtension {
  // Component injection
  provideComponent?(location: UILocation, props?: any): React.ComponentType<any>

  // Menu and toolbar extensions
  provideMenuItem?(location: MenuLocation): MenuItem
  provideToolbarItem?(location: ToolbarLocation): ToolbarItem

  // Settings panels
  provideSettingsPanel?(): SettingsPanel
}

// Enhanced API Extension Point with namespace support
export interface APIExtension {
  // Plugin namespace (automatically generated as `plugin_${pluginId}`)
  getNamespace(): string

  // Custom API endpoints registration
  registerEndpoints(apiRegistry: APIRegistry): PluginAPINamespace

  // Plugin-specific middleware (returns Function[] to match APIRegistry)
  provideMiddleware?(): Function[]

  // Dynamic endpoint discovery
  discoverEndpoints?(): Promise<PluginEndpoint[]>

  // Endpoint validation
  validateEndpoint?(endpoint: string, args: any[]): boolean
}

// Intelligence Processing Extension Point
export interface IntelligenceExtension {
  // Entity extraction
  extractEntities?(content: string): Promise<any[]>
  
  // Topic analysis
  analyzeTopics?(content: string): Promise<any[]>
  
  // Content enhancement
  enhanceContent?(content: string): Promise<string>
}

export enum UILocation {
  SIDEBAR_TOP = 'sidebar_top',
  SIDEBAR_BOTTOM = 'sidebar_bottom',
  CHAT_INPUT_TOOLBAR = 'chat_input_toolbar',
  MESSAGE_ACTIONS = 'message_actions',
  SETTINGS_TABS = 'settings_tabs'
}

export enum MenuLocation {
  MAIN_MENU = 'main_menu',
  CONTEXT_MENU = 'context_menu',
  FILE_MENU = 'file_menu'
}

export enum ToolbarLocation {
  CHAT_INPUT_TOOLBAR = 'chat_input_toolbar',
  MESSAGE_TOOLBAR = 'message_toolbar',
  MAIN_TOOLBAR = 'main_toolbar'
}