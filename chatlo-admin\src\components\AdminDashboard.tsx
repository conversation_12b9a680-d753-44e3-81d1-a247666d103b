import React, { useState, useEffect } from 'react'
import { IntelligenceTestEnvironment } from './IntelligenceTestEnvironment'
import { SystemPromptManager } from './SystemPromptManager'
import { PipelineConfigurator } from './PipelineConfigurator'
import { PerformanceMonitor } from './PerformanceMonitor'
import { DeploymentManager } from './DeploymentManager'
import { ExtensionManager } from './ExtensionManager'
import { chatLoAPI } from '../services/ChatLoAPI'
import { SystemHealth, AdminOperation } from '../types/api'

// VSCode-style Extension Interface
interface AdminExtension {
  id: string
  name: string
  icon: string
  component: React.ComponentType
  category: 'core' | 'testing' | 'deployment' | 'analytics' | 'community'
  version: string
  enabled: boolean
  description: string
  author?: string
  badges?: string[]
}

interface AdminDashboardProps {
  onClose?: () => void
}

export const AdminDashboard: React.FC<AdminDashboardProps> = ({ onClose }) => {
  const [activeExtension, setActiveExtension] = useState('intelligence-testing')
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null)
  const [operations, setOperations] = useState<AdminOperation[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [extensions, setExtensions] = useState<AdminExtension[]>([])
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)

  useEffect(() => {
    initializeExtensions()
    loadDashboardData()
    const interval = setInterval(loadDashboardData, 30000) // Refresh every 30s
    return () => clearInterval(interval)
  }, [])

  const initializeExtensions = () => {
    // Core extensions (always available)
    const coreExtensions: AdminExtension[] = [
      {
        id: 'intelligence-testing',
        name: 'Intelligence Testing',
        icon: '🧪',
        component: IntelligenceTestEnvironment,
        category: 'core',
        version: '1.0.0',
        enabled: true,
        description: 'Test intelligence extraction with real LLM models',
        author: 'ChatLo Team',
        badges: ['Core', 'Testing']
      },
      {
        id: 'system-prompts',
        name: 'System Prompts',
        icon: '📝',
        component: SystemPromptManager,
        category: 'core',
        version: '1.0.0',
        enabled: true,
        description: 'Manage system prompts and templates',
        author: 'ChatLo Team',
        badges: ['Core', 'Configuration']
      },
      {
        id: 'pipeline-config',
        name: 'Pipeline Config',
        icon: '⚙️',
        component: PipelineConfigurator,
        category: 'core',
        version: '1.0.0',
        enabled: true,
        description: 'Configure LangChain processing pipelines',
        author: 'ChatLo Team',
        badges: ['Core', 'Pipeline']
      },
      {
        id: 'performance-monitor',
        name: 'Performance Monitor',
        icon: '📊',
        component: PerformanceMonitor,
        category: 'analytics',
        version: '1.0.0',
        enabled: true,
        description: 'Monitor system performance and metrics',
        author: 'ChatLo Team',
        badges: ['Core', 'Analytics']
      },
      {
        id: 'deployment-manager',
        name: 'Deployment Manager',
        icon: '🚀',
        component: DeploymentManager,
        category: 'deployment',
        version: '1.0.0',
        enabled: true,
        description: 'Manage deployments and rollbacks',
        author: 'ChatLo Team',
        badges: ['Core', 'Deployment']
      },
      {
        id: 'extension-manager',
        name: 'Extensions',
        icon: '🔌',
        component: ExtensionManager,
        category: 'core',
        version: '1.0.0',
        enabled: true,
        description: 'Manage and install extensions',
        author: 'ChatLo Team',
        badges: ['Core', 'Extensions']
      }
    ]

    // Load community extensions from localStorage or API
    const communityExtensions = loadCommunityExtensions()

    setExtensions([...coreExtensions, ...communityExtensions])
  }

  const loadCommunityExtensions = (): AdminExtension[] => {
    // This would load from API or localStorage
    // For now, return empty array
    return []
  }

  const loadDashboardData = async () => {
    try {
      const [healthData, operationsData] = await Promise.all([
        chatLoAPI.getSystemHealth(),
        chatLoAPI.getOperations()
      ])
      
      setSystemHealth(healthData)
      setOperations(operationsData)
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getHealthStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-400'
      case 'degraded': return 'text-yellow-400'
      case 'unhealthy': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  const renderExtensionContent = () => {
    const extension = extensions.find(ext => ext.id === activeExtension)
    if (!extension || !extension.enabled) {
      return (
        <div className="flex items-center justify-center h-full">
          <div className="text-center text-gray-400">
            <div className="text-4xl mb-4">🔌</div>
            <p>Extension not found or disabled</p>
          </div>
        </div>
      )
    }

    const ExtensionComponent = extension.component
    return <ExtensionComponent />
  }

  const getExtensionsByCategory = (category: string) => {
    return extensions.filter(ext => ext.category === category && ext.enabled)
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading ChatLo Admin Dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Header */}
      <header className="bg-gray-800 border-b border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h1 className="text-2xl font-bold text-white">
              🏛️ ChatLo Admin Dashboard
            </h1>
            <span className="text-sm bg-blue-600/20 text-blue-400 px-2 py-1 rounded">
              v1.0.0
            </span>
          </div>
          
          <div className="flex items-center gap-4">
            {/* System Health Indicator */}
            {systemHealth && (
              <div className="flex items-center gap-2">
                <div className={`w-2 h-2 rounded-full ${
                  systemHealth.status === 'healthy' ? 'bg-green-400' :
                  systemHealth.status === 'degraded' ? 'bg-yellow-400' : 'bg-red-400'
                }`}></div>
                <span className={`text-sm ${getHealthStatusColor(systemHealth.status)}`}>
                  System {systemHealth.status}
                </span>
              </div>
            )}
            
            {/* Active Operations */}
            {operations.length > 0 && (
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-blue-400 animate-pulse"></div>
                <span className="text-sm text-blue-400">
                  {operations.filter(op => op.status === 'running').length} active operations
                </span>
              </div>
            )}
            
            {onClose && (
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-white transition-colors"
              >
                ✕
              </button>
            )}
          </div>
        </div>
      </header>

      <div className="flex h-[calc(100vh-80px)]">
        {/* Sidebar Navigation */}
        <nav className="w-64 bg-gray-800 border-r border-gray-700 p-4">
          <div className="space-y-2">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`w-full flex items-center gap-3 px-4 py-3 rounded-lg text-left transition-colors ${
                  activeTab === tab.id
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                }`}
              >
                <span className="text-lg">{tab.icon}</span>
                <span className="font-medium">{tab.name}</span>
              </button>
            ))}
          </div>

          {/* Quick Stats */}
          <div className="mt-8 p-4 bg-gray-700/50 rounded-lg">
            <h3 className="text-sm font-medium text-gray-300 mb-3">Quick Stats</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-400">Active Tests:</span>
                <span className="text-white">
                  {operations.filter(op => op.type === 'test' && op.status === 'running').length}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Deployments:</span>
                <span className="text-white">
                  {operations.filter(op => op.type === 'deploy').length}
                </span>
              </div>
              {systemHealth && (
                <div className="flex justify-between">
                  <span className="text-gray-400">Components:</span>
                  <span className="text-white">
                    {systemHealth.components.filter(c => c.status === 'healthy').length}/
                    {systemHealth.components.length}
                  </span>
                </div>
              )}
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto">
          <div className="p-6">
            {renderTabContent()}
          </div>
        </main>
      </div>

      {/* Status Bar */}
      <footer className="bg-gray-800 border-t border-gray-700 px-6 py-2">
        <div className="flex items-center justify-between text-sm text-gray-400">
          <div className="flex items-center gap-4">
            <span>ChatLo Admin Module</span>
            <span>•</span>
            <span>Connected to: localhost:3000</span>
          </div>
          <div className="flex items-center gap-4">
            <span>Last updated: {new Date().toLocaleTimeString()}</span>
            {systemHealth && (
              <span>
                CPU: {systemHealth.metrics.cpuUsage.toFixed(1)}% | 
                Memory: {systemHealth.metrics.memoryUsage.toFixed(1)}%
              </span>
            )}
          </div>
        </div>
      </footer>
    </div>
  )
}
