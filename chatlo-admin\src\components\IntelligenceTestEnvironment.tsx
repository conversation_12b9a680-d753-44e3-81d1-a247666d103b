import React, { useState, useEffect } from 'react'
import { chatLoAPI } from '../services/ChatLoAPI'
import { ProcessingOutput, ModelInfo, TestCase, ValidationResult } from '../types/api'

export const IntelligenceTestEnvironment: React.FC = () => {
  const [models, setModels] = useState<ModelInfo[]>([])
  const [selectedModel, setSelectedModel] = useState<string>('')
  const [testContent, setTestContent] = useState<string>('')
  const [isProcessing, setIsProcessing] = useState(false)
  const [results, setResults] = useState<ProcessingOutput | null>(null)
  const [processingTime, setProcessingTime] = useState<number>(0)
  const [testHistory, setTestHistory] = useState<any[]>([])

  // Test content templates
  const testTemplates = {
    english: 'China oil industry research - analyzing market trends, key players like CNPC and Sinopec, production capacity, import dependencies, and investment opportunities in the petroleum sector.',
    chineseSimplified: '中国石油行业研究 - 分析市场趋势，中石油和中石化等主要企业，生产能力，进口依赖性，以及石油行业的投资机会。研究包括原油价格波动，炼油技术发展，能源政策影响，以及可持续发展战略。',
    chineseTraditional: '人工智慧技術趨勢 - 機器學習演算法，神經網路，深度學習應用，自然語言處理，電腦視覺，以及各行業中新興的人工智慧技術。包括數據分析，智能自動化，以及數位轉型策略。',
    mixed: '可再生能源市场分析 - solar power太阳能装置，wind energy風能容量，green technology绿色技术投资，sustainable energy可持续能源政策，carbon reduction碳减排倡议，以及clean energy清洁能源转型策略。'
  }

  useEffect(() => {
    loadModels()
    loadTestHistory()
  }, [])

  const loadModels = async () => {
    try {
      // Try to load from API first, fallback to direct detection
      try {
        const apiModels = await chatLoAPI.listModels()
        setModels(apiModels)
      } catch (error) {
        console.log('API not available, detecting models directly...')
        await detectModelsDirectly()
      }
    } catch (error) {
      console.error('Failed to load models:', error)
    }
  }

  const detectModelsDirectly = async () => {
    const detectedModels: ModelInfo[] = []

    // Check Ollama
    try {
      const response = await fetch('http://localhost:11434/api/tags')
      if (response.ok) {
        const data = await response.json()
        data.models?.forEach((model: any) => {
          detectedModels.push({
            id: `ollama:${model.name}`,
            name: model.name,
            provider: 'ollama',
            version: model.modified_at || 'unknown',
            capabilities: ['text-generation', 'intelligence-extraction'],
            performance: {
              averageLatency: 0,
              throughput: 0,
              accuracy: 0,
              memoryUsage: 0,
              lastUpdated: new Date().toISOString()
            },
            status: 'available'
          })
        })
      }
    } catch (error) {
      console.log('Ollama not available')
    }

    // Check LM Studio
    try {
      const response = await fetch('http://localhost:1234/v1/models')
      if (response.ok) {
        const data = await response.json()
        data.data?.forEach((model: any) => {
          detectedModels.push({
            id: `lmstudio:${model.id}`,
            name: model.id,
            provider: 'lmstudio',
            version: 'unknown',
            capabilities: ['text-generation', 'intelligence-extraction'],
            performance: {
              averageLatency: 0,
              throughput: 0,
              accuracy: 0,
              memoryUsage: 0,
              lastUpdated: new Date().toISOString()
            },
            status: 'available'
          })
        })
      }
    } catch (error) {
      console.log('LM Studio not available')
    }

    setModels(detectedModels)
  }

  const loadTestHistory = () => {
    const history = localStorage.getItem('chatlo-admin-test-history')
    if (history) {
      setTestHistory(JSON.parse(history))
    }
  }

  const saveTestResult = (result: any) => {
    const newHistory = [result, ...testHistory.slice(0, 9)] // Keep last 10 tests
    setTestHistory(newHistory)
    localStorage.setItem('chatlo-admin-test-history', JSON.stringify(newHistory))
  }

  const runIntelligenceTest = async () => {
    if (!selectedModel || !testContent.trim()) {
      alert('Please select a model and enter test content')
      return
    }

    setIsProcessing(true)
    const startTime = Date.now()

    try {
      // Try API first, fallback to direct LLM call
      let result: ProcessingOutput

      try {
        result = await chatLoAPI.extractIntelligence(testContent, selectedModel)
      } catch (error) {
        console.log('API not available, calling LLM directly...')
        result = await callLLMDirectly(selectedModel, testContent)
      }

      const endTime = Date.now()
      const processingTimeMs = endTime - startTime

      setResults(result)
      setProcessingTime(processingTimeMs)

      // Save to history
      const testResult = {
        id: `test_${Date.now()}`,
        timestamp: new Date().toISOString(),
        model: selectedModel,
        content: testContent.substring(0, 100) + '...',
        results: result,
        processingTime: processingTimeMs
      }
      saveTestResult(testResult)

    } catch (error) {
      console.error('Test failed:', error)
      alert(`Test failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsProcessing(false)
    }
  }

  const callLLMDirectly = async (modelId: string, content: string): Promise<ProcessingOutput> => {
    const [provider, modelName] = modelId.split(':')
    
    const prompt = `Analyze the following content and extract structured intelligence:

CONTENT:
${content}

Please provide a JSON response with the following structure:
{
  "entities": [{"name": "entity", "type": "person|place|concept|technology|organization|other", "confidence": 0.8}],
  "topics": [{"name": "topic", "relevance": 0.9, "category": "business|technology|research|finance|energy|healthcare|general"}],
  "summary": "brief analysis"
}`

    let llmResponse: string

    if (provider === 'ollama') {
      llmResponse = await chatLoAPI.callOllamaModel(modelName, prompt)
    } else if (provider === 'lmstudio') {
      llmResponse = await chatLoAPI.callLMStudioModel(modelName, prompt)
    } else {
      throw new Error(`Unsupported provider: ${provider}`)
    }

    // Parse LLM response
    return parseLLMResponse(llmResponse, content)
  }

  const parseLLMResponse = (llmResponse: string, originalContent: string): ProcessingOutput => {
    let entities: any[] = []
    let topics: any[] = []
    let summary = ''

    try {
      const jsonMatch = llmResponse.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0])
        entities = parsed.entities || []
        topics = parsed.topics || []
        summary = parsed.summary || ''
      }
    } catch (error) {
      console.log('JSON parsing failed, using fallback extraction')
      // Fallback extraction logic here
    }

    const confidence = Math.min((entities.length * 0.1) + (topics.length * 0.1) + 0.3, 1.0)

    return {
      entities,
      topics,
      artifacts: [],
      confidence,
      domain: 'test',
      processingMetadata: {
        version: '1.0',
        method: 'direct_llm',
        timestamp: new Date().toISOString(),
        modelUsed: selectedModel,
        rawResponse: llmResponse
      }
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white mb-2">🧪 Intelligence Test Environment</h2>
          <p className="text-gray-400">Test intelligence extraction with real LLM models</p>
        </div>
        <div className="flex items-center gap-2">
          <div className={`w-2 h-2 rounded-full ${models.length > 0 ? 'bg-green-400' : 'bg-red-400'}`}></div>
          <span className="text-sm text-gray-400">{models.length} models available</span>
        </div>
      </div>

      {/* Model Selection */}
      <div className="bg-gray-800 rounded-lg p-4">
        <label className="block text-sm font-medium text-gray-300 mb-2">Select Model</label>
        <div className="flex gap-4">
          <select
            value={selectedModel}
            onChange={(e) => setSelectedModel(e.target.value)}
            className="flex-1 bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white"
            disabled={isProcessing}
          >
            <option value="">Choose a model...</option>
            {models.map((model) => (
              <option key={model.id} value={model.id}>
                {model.provider === 'ollama' ? '🏠' : '🏠'} {model.name} ({model.provider})
              </option>
            ))}
          </select>
          <button
            onClick={loadModels}
            disabled={isProcessing}
            className="bg-blue-600 hover:bg-blue-700 disabled:opacity-50 px-4 py-2 rounded text-white"
          >
            🔄 Refresh
          </button>
        </div>
      </div>

      {/* Test Content */}
      <div className="bg-gray-800 rounded-lg p-4">
        <div className="flex justify-between items-center mb-3">
          <label className="block text-sm font-medium text-gray-300">Test Content</label>
          <div className="flex gap-2 flex-wrap">
            <button
              onClick={() => setTestContent(testTemplates.english)}
              className="text-xs px-2 py-1 bg-blue-600/20 text-blue-400 rounded hover:bg-blue-600/30"
            >
              English
            </button>
            <button
              onClick={() => setTestContent(testTemplates.chineseSimplified)}
              className="text-xs px-2 py-1 bg-yellow-600/20 text-yellow-400 rounded hover:bg-yellow-600/30"
            >
              中文简体
            </button>
            <button
              onClick={() => setTestContent(testTemplates.chineseTraditional)}
              className="text-xs px-2 py-1 bg-yellow-600/20 text-yellow-400 rounded hover:bg-yellow-600/30"
            >
              中文繁體
            </button>
            <button
              onClick={() => setTestContent(testTemplates.mixed)}
              className="text-xs px-2 py-1 bg-green-600/20 text-green-400 rounded hover:bg-green-600/30"
            >
              Mixed 混合
            </button>
          </div>
        </div>
        <textarea
          value={testContent}
          onChange={(e) => setTestContent(e.target.value)}
          className="w-full h-32 p-3 bg-gray-700 border border-gray-600 rounded text-white resize-none"
          placeholder="Enter content to test intelligence extraction..."
          disabled={isProcessing}
        />
      </div>

      {/* Test Controls */}
      <div className="flex gap-4">
        <button
          onClick={runIntelligenceTest}
          disabled={isProcessing || !selectedModel || !testContent.trim()}
          className="bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed px-6 py-2 rounded text-white font-medium"
        >
          {isProcessing ? '⏳ Processing...' : '▶️ Run Intelligence Test'}
        </button>
        <button
          onClick={() => {
            setResults(null)
            setTestContent('')
          }}
          className="bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded text-white"
        >
          🗑️ Clear
        </button>
      </div>

      {/* Results will be added in the next part... */}
    </div>
  )
}
