/**
 * Structured Error Handling System
 * Provides unified error handling with error codes, timestamps, and structured responses
 */

export enum ErrorCode {
  // General errors
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  INVALID_INPUT = 'INVALID_INPUT',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  TIMEOUT = 'TIMEOUT',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  
  // Authentication/Authorization errors
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  AUTHENTICATION_REQUIRED = 'AUTHENTICATION_REQUIRED',
  
  // Database errors
  DATABASE_ERROR = 'DATABASE_ERROR',
  DATABASE_CONNECTION_ERROR = 'DATABASE_CONNECTION_ERROR',
  RECORD_NOT_FOUND = 'RECORD_NOT_FOUND',
  DUPLICATE_RECORD = 'DUPLICATE_RECORD',
  CONSTRAINT_VIOLATION = 'CONSTRAINT_VIOLATION',
  
  // File system errors
  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
  FILE_ACCESS_DENIED = 'FILE_ACCESS_DENIED',
  DIRECTORY_NOT_FOUND = 'DIRECTORY_NOT_FOUND',
  DISK_SPACE_ERROR = 'DISK_SPACE_ERROR',
  FILE_SYSTEM_ERROR = 'FILE_SYSTEM_ERROR',
  
  // Plugin errors
  PLUGIN_NOT_FOUND = 'PLUGIN_NOT_FOUND',
  PLUGIN_LOAD_ERROR = 'PLUGIN_LOAD_ERROR',
  PLUGIN_EXECUTION_ERROR = 'PLUGIN_EXECUTION_ERROR',
  PLUGIN_CONFIG_ERROR = 'PLUGIN_CONFIG_ERROR',
  PLUGIN_API_ERROR = 'PLUGIN_API_ERROR',
  
  // Network errors
  NETWORK_ERROR = 'NETWORK_ERROR',
  CONNECTION_TIMEOUT = 'CONNECTION_TIMEOUT',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  
  // API errors
  API_ENDPOINT_NOT_FOUND = 'API_ENDPOINT_NOT_FOUND',
  API_METHOD_NOT_ALLOWED = 'API_METHOD_NOT_ALLOWED',
  API_VERSION_MISMATCH = 'API_VERSION_MISMATCH',
  
  // Vault errors
  VAULT_ERROR = 'VAULT_ERROR',
  VAULT_ACCESS_DENIED = 'VAULT_ACCESS_DENIED',
  VAULT_CORRUPTION = 'VAULT_CORRUPTION'
}

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface StructuredError {
  code: ErrorCode
  message: string
  details?: any
  timestamp: string
  severity: ErrorSeverity
  category: string
  endpoint?: string
  userId?: string
  sessionId?: string
  stackTrace?: string
  context?: Record<string, any>
}

export interface ErrorResponse {
  success: false
  error: StructuredError
  requestId?: string
}

export interface SuccessResponse<T = any> {
  success: true
  data: T
  timestamp?: string
  requestId?: string
}

export type APIResponse<T = any> = SuccessResponse<T> | ErrorResponse

export class StructuredAPIError extends Error {
  public readonly structuredError: StructuredError
  public cause?: Error

  constructor(
    code: ErrorCode,
    message: string,
    options: {
      details?: any
      severity?: ErrorSeverity
      category?: string
      endpoint?: string
      userId?: string
      sessionId?: string
      context?: Record<string, any>
      cause?: Error
    } = {}
  ) {
    super(message)
    this.name = 'StructuredAPIError'

    const {
      details,
      severity = ErrorSeverity.MEDIUM,
      category = 'general',
      endpoint,
      userId,
      sessionId,
      context,
      cause
    } = options

    this.structuredError = {
      code,
      message,
      details,
      timestamp: new Date().toISOString(),
      severity,
      category,
      endpoint,
      userId,
      sessionId,
      stackTrace: this.stack,
      context
    }

    if (cause) {
      this.cause = cause
    }
  }

  toJSON(): StructuredError {
    return this.structuredError
  }

  toResponse(requestId?: string): ErrorResponse {
    return {
      success: false,
      error: this.structuredError,
      requestId
    }
  }
}

export class ErrorHandler {
  private static errorCounts: Map<ErrorCode, number> = new Map()
  private static errorHistory: StructuredError[] = []
  private static maxHistorySize = 1000

  /**
   * Create a structured error from various error types
   */
  static createStructuredError(
    error: any,
    context: {
      category: string
      endpoint?: string
      userId?: string
      sessionId?: string
      additionalContext?: Record<string, any>
    }
  ): StructuredAPIError {
    if (error instanceof StructuredAPIError) {
      return error
    }

    let code: ErrorCode = ErrorCode.UNKNOWN_ERROR
    let message: string = 'An unknown error occurred'
    let severity: ErrorSeverity = ErrorSeverity.MEDIUM
    let details: any = undefined

    if (error instanceof Error) {
      message = error.message

      // Map common error patterns to error codes
      if (error.message.includes('ENOENT') || error.message.includes('not found')) {
        code = context.category === 'filesystem' ? ErrorCode.FILE_NOT_FOUND : ErrorCode.RECORD_NOT_FOUND
      } else if (error.message.includes('EACCES') || error.message.includes('permission denied')) {
        code = ErrorCode.PERMISSION_DENIED
        severity = ErrorSeverity.HIGH
      } else if (error.message.includes('timeout')) {
        code = ErrorCode.TIMEOUT
      } else if (error.message.includes('rate limit')) {
        code = ErrorCode.RATE_LIMIT_EXCEEDED
      } else if (error.message.includes('validation')) {
        code = ErrorCode.VALIDATION_ERROR
      } else if (error.message.includes('database')) {
        code = ErrorCode.DATABASE_ERROR
        severity = ErrorSeverity.HIGH
      } else if (error.message.includes('plugin')) {
        code = ErrorCode.PLUGIN_EXECUTION_ERROR
      } else if (error.message.includes('network') || error.message.includes('connection')) {
        code = ErrorCode.NETWORK_ERROR
      }

      details = {
        originalError: error.name,
        stack: error.stack
      }
    } else if (typeof error === 'string') {
      message = error
    } else if (typeof error === 'object' && error !== null) {
      message = error.message || JSON.stringify(error)
      details = error
    }

    return new StructuredAPIError(code, message, {
      details,
      severity,
      category: context.category,
      endpoint: context.endpoint,
      userId: context.userId,
      sessionId: context.sessionId,
      context: context.additionalContext,
      cause: error instanceof Error ? error : undefined
    })
  }

  /**
   * Handle and log an error
   */
  static handleError(error: any, context: {
    category: string
    endpoint?: string
    userId?: string
    sessionId?: string
    additionalContext?: Record<string, any>
  }): StructuredAPIError {
    const structuredError = this.createStructuredError(error, context)
    
    // Update error counts
    const currentCount = this.errorCounts.get(structuredError.structuredError.code) || 0
    this.errorCounts.set(structuredError.structuredError.code, currentCount + 1)

    // Add to error history
    this.errorHistory.push(structuredError.structuredError)
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory.shift()
    }

    // Log the error
    this.logError(structuredError.structuredError)

    return structuredError
  }

  /**
   * Log error based on severity
   */
  private static logError(error: StructuredError): void {
    const logMessage = `[${error.severity.toUpperCase()}] ${error.code}: ${error.message}`
    const logContext = {
      category: error.category,
      endpoint: error.endpoint,
      timestamp: error.timestamp,
      details: error.details,
      context: error.context
    }

    switch (error.severity) {
      case ErrorSeverity.CRITICAL:
        console.error(logMessage, logContext)
        break
      case ErrorSeverity.HIGH:
        console.error(logMessage, logContext)
        break
      case ErrorSeverity.MEDIUM:
        console.warn(logMessage, logContext)
        break
      case ErrorSeverity.LOW:
        console.info(logMessage, logContext)
        break
    }
  }

  /**
   * Create a success response
   */
  static createSuccessResponse<T>(data: T, requestId?: string): SuccessResponse<T> {
    return {
      success: true,
      data,
      timestamp: new Date().toISOString(),
      requestId
    }
  }

  /**
   * Wrap an async function with error handling
   */
  static wrapAsync<TArgs extends any[], TReturn>(
    fn: (...args: TArgs) => Promise<TReturn>,
    context: {
      category: string
      endpoint?: string
    }
  ) {
    return async (...args: TArgs): Promise<APIResponse<TReturn>> => {
      try {
        const result = await fn(...args)
        return this.createSuccessResponse(result)
      } catch (error) {
        const structuredError = this.handleError(error, context)
        return structuredError.toResponse()
      }
    }
  }

  /**
   * Get error statistics
   */
  static getErrorStatistics(): {
    totalErrors: number
    errorsByCode: Record<string, number>
    recentErrors: StructuredError[]
    topErrors: Array<{ code: ErrorCode; count: number }>
  } {
    const totalErrors = Array.from(this.errorCounts.values()).reduce((sum, count) => sum + count, 0)
    const errorsByCode: Record<string, number> = {}
    
    this.errorCounts.forEach((count, code) => {
      errorsByCode[code] = count
    })

    const topErrors = Array.from(this.errorCounts.entries())
      .map(([code, count]) => ({ code, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)

    const recentErrors = this.errorHistory.slice(-50)

    return {
      totalErrors,
      errorsByCode,
      recentErrors,
      topErrors
    }
  }

  /**
   * Clear error history and statistics
   */
  static clearErrorHistory(): void {
    this.errorCounts.clear()
    this.errorHistory.length = 0
  }

  /**
   * Check if error is retryable
   */
  static isRetryableError(error: StructuredAPIError): boolean {
    const retryableCodes = [
      ErrorCode.TIMEOUT,
      ErrorCode.NETWORK_ERROR,
      ErrorCode.CONNECTION_TIMEOUT,
      ErrorCode.SERVICE_UNAVAILABLE,
      ErrorCode.DATABASE_CONNECTION_ERROR
    ]

    return retryableCodes.includes(error.structuredError.code)
  }

  /**
   * Get suggested retry delay based on error type
   */
  static getRetryDelay(error: StructuredAPIError, attempt: number): number {
    const baseDelay = 1000 // 1 second
    const maxDelay = 30000 // 30 seconds

    let multiplier = 1

    switch (error.structuredError.code) {
      case ErrorCode.RATE_LIMIT_EXCEEDED:
        multiplier = 5
        break
      case ErrorCode.DATABASE_CONNECTION_ERROR:
        multiplier = 3
        break
      case ErrorCode.NETWORK_ERROR:
        multiplier = 2
        break
      default:
        multiplier = 1
    }

    const delay = Math.min(baseDelay * multiplier * Math.pow(2, attempt), maxDelay)
    return delay
  }
}
