# ChatLo Intelligence System Testing Framework v0.1

## Overview
This testing framework validates the ChatLo intelligence system's ability to use local LLMs for data orchestration, context linking, and research workflow enablement. The focus is on practical, valuable actions that local LLMs can perform reliably on baseline hardware.

## Testing Philosophy
**Local LLM as Data Orchestrator**: We test the system's ability to orchestrate data capture and utilize tools, not provide general AI assistance. The intelligence system should perform unsupervised actions that deliver immediate value through context linking and file organization.

## Primary Use Case Example
**Research Context Creation**: User inputs "China oil industry research" → System recognizes intent → Maps related chat history → Requests permission to classify → Links relevant files (markdown, PDF, docx, excel) → Generates master.md for research synthesis → Enables follow-up tasks (reports, outlines, charts, presentations).

## Hardware Constraints
**Target System**: 9th gen Intel i7 + RTX 2060 (baseline hardware)
**Performance Requirements**: 
- Intent recognition: < 10ms
- Historical mapping: < 50ms  
- File linking: < 200ms
- Master.md generation: < 500ms

## Testing Levels

### Level 1: Intent Recognition Tests
**Objective**: Validate system's ability to identify research contexts from conversational input

**Test Cases**:
1. **Research Domain Recognition**
   - Input: "China oil industry research"
   - Expected: Identify "China oil industry" as research domain
   - Success Criteria: 90% accuracy on domain extraction

2. **Multi-word Context Detection**
   - Input: "AI development trends analysis"
   - Expected: Recognize complete research context
   - Success Criteria: Preserve full context meaning

3. **Ambiguous Input Handling**
   - Input: "Looking into renewable energy"
   - Expected: Request clarification or suggest context
   - Success Criteria: Graceful handling without false classification

### Level 2: Historical Mapping Tests
**Objective**: Verify accurate identification of related previous conversations

**Test Cases**:
1. **Semantic Similarity Matching**
   - Previous chats about "petroleum industry", "oil prices", "energy sector"
   - New input: "China oil industry research"
   - Expected: Identify related conversations with confidence scores
   - Success Criteria: 85% precision, 80% recall

2. **Temporal Relevance Filtering**
   - Mix of recent and old conversations on similar topics
   - Expected: Prioritize recent, relevant discussions
   - Success Criteria: Proper temporal weighting

3. **False Positive Prevention**
   - Unrelated conversations with similar keywords
   - Expected: Avoid incorrect associations
   - Success Criteria: < 10% false positive rate

### Level 3: Permission Workflow Tests
**Objective**: Ensure user-friendly classification prompts and responses

**Test Cases**:
1. **Clear Permission Requests**
   - Prompt: "Add to China oil industry research?"
   - Expected: Clear, actionable user interface
   - Success Criteria: User comprehension > 95%

2. **Batch Classification Options**
   - Multiple related conversations found
   - Expected: Efficient bulk classification interface
   - Success Criteria: < 3 clicks for bulk actions

3. **Context Creation Workflow**
   - No existing context for new research domain
   - Expected: Smooth new context creation process
   - Success Criteria: < 30 seconds to create new context

### Level 4: File Linking Tests
**Objective**: Validate multi-format document relevance detection

**Test Cases**:
1. **Multi-Format Support**
   - Test files: PDF reports, Word documents, Excel spreadsheets, Markdown notes
   - Expected: Accurate relevance scoring across formats
   - Success Criteria: 80% accuracy in relevance detection

2. **Content-Based Linking**
   - Files with relevant content but different naming
   - Expected: Content analysis over filename matching
   - Success Criteria: Content relevance > filename matching

3. **Large File Handling**
   - Files > 10MB with relevant sections
   - Expected: Efficient processing without performance degradation
   - Success Criteria: Processing time < 2 seconds per file

### Level 5: Master.md Generation Tests
**Objective**: Assess research summary quality and usability

**Test Cases**:
1. **Comprehensive Synthesis**
   - Multiple sources: chats, PDFs, documents
   - Expected: Coherent research summary
   - Success Criteria: Human evaluator rates > 4/5 for usefulness

2. **Structured Output**
   - Generated master.md with clear sections
   - Expected: Organized, scannable content structure
   - Success Criteria: Follows consistent template format

3. **Actionable Insights**
   - Summary enables follow-up research tasks
   - Expected: Clear next steps and key findings
   - Success Criteria: Users can create reports from summary

### Level 6: End-to-End Research Tests
**Objective**: Complete workflow from chat to deliverable creation

**Test Cases**:
1. **Complete Research Workflow**
   - Start: Casual chat about research topic
   - Process: Intent recognition → mapping → linking → synthesis
   - End: Usable research context with master.md
   - Success Criteria: 90% workflow completion rate

2. **Follow-up Task Enablement**
   - Use master.md for report creation, outline generation
   - Expected: Generated content supports research deliverables
   - Success Criteria: Users successfully create final outputs

3. **Multi-Session Continuity**
   - Research context persists across chat sessions
   - Expected: Seamless continuation of research work
   - Success Criteria: Context state maintained correctly

## Quality Metrics

### Technical Accuracy
- **Entity Extraction**: 90% accuracy threshold
- **Semantic Matching**: 85% precision, 80% recall
- **File Relevance**: 80% accuracy in content analysis
- **Processing Speed**: Meet hardware constraint limits

### User Experience
- **Workflow Completion**: 90% success rate for end-to-end tests
- **User Satisfaction**: 4/5 rating for generated content usefulness
- **Interface Clarity**: 95% user comprehension of prompts
- **Performance**: No noticeable delays in chat interface

### System Reliability
- **Error Handling**: Graceful degradation in edge cases
- **Memory Usage**: < 100MB for intelligence processing
- **CPU Impact**: < 50% usage during processing
- **Storage Efficiency**: Minimal disk space for intelligence data

## Testing Tools

### Automated Testing Suite
- **Entity Extraction Validator**: Measures accuracy against known datasets
- **Semantic Similarity Scorer**: Validates relationship detection
- **Performance Monitor**: Tracks processing times and resource usage
- **File Analysis Validator**: Tests multi-format content extraction

### Manual Testing Procedures
- **User Journey Tests**: Step-by-step workflow validation
- **Content Quality Review**: Human evaluation of generated summaries
- **Usability Testing**: Interface clarity and user comprehension
- **Edge Case Validation**: Error handling and graceful degradation

## Test Data Sets

### Research Domain Examples
1. **China Oil Industry Research**
   - Sample conversations about petroleum, energy sector, China market
   - Related documents: industry reports, market analysis, news articles
   - Expected outcomes: Comprehensive research context

2. **AI Development Trends**
   - Conversations about machine learning, AI tools, technology trends
   - Related files: research papers, technical documentation, trend reports
   - Expected outcomes: Technology research synthesis

3. **Market Analysis Studies**
   - Discussions about market conditions, competitor analysis, business trends
   - Related documents: financial reports, market data, analysis spreadsheets
   - Expected outcomes: Business intelligence context

## Success Criteria

### Minimum Viable Performance
- Intent recognition accuracy: 85%
- Historical mapping precision: 80%
- File linking accuracy: 75%
- Master.md usefulness rating: 3.5/5
- Workflow completion rate: 80%

### Target Performance
- Intent recognition accuracy: 90%
- Historical mapping precision: 85%
- File linking accuracy: 80%
- Master.md usefulness rating: 4/5
- Workflow completion rate: 90%

### Exceptional Performance
- Intent recognition accuracy: 95%
- Historical mapping precision: 90%
- File linking accuracy: 85%
- Master.md usefulness rating: 4.5/5
- Workflow completion rate: 95%

## How to Run Tests

### Prerequisites
- ChatLo application running with intelligence system enabled
- Test data sets loaded in appropriate directories
- Performance monitoring tools configured
- Baseline hardware setup (9th gen i7 + RTX 2060 or equivalent)

### Test Execution Steps
1. **Setup**: Load test data and configure monitoring
2. **Level 1-6**: Execute tests in sequence, recording results
3. **Analysis**: Compare results against success criteria
4. **Reporting**: Generate comprehensive test report with recommendations

### Continuous Testing
- **Daily**: Automated technical accuracy tests
- **Weekly**: User experience validation tests
- **Monthly**: Complete end-to-end workflow tests
- **Release**: Full test suite execution before deployment

## Master.md Structure and Content Framework

### Purpose of Master.md
Master.md serves as the **intelligent interface between human and AI LLM** for each research context. It transforms from a static document into a dynamic knowledge hub that continuously learns from user interactions, chat conversations, and file relationships.

### Master.md Template Structure

#### Example: China Oil Industry Research Context

```markdown
# China Oil Industry Research

## Research Overview
**Created**: 2025-01-24
**Last Updated**: 2025-01-24 15:30:22
**Status**: Active Research
**Context ID**: china_oil_industry_research_001

**Research Objective**: Comprehensive analysis of China's oil industry for business intelligence and investment decision-making.

## Key Findings Summary
- **Market Size**: China is the world's second-largest oil consumer (15.4 million barrels/day in 2024)
- **Major Players**: CNPC (45% market share), Sinopec (35%), CNOOC (20%)
- **Growth Trends**: Domestic production declining (-2.1% annually), import dependency increasing (73% in 2024)
- **Investment Focus**: Renewable energy transition, offshore exploration, refining capacity expansion

## Core Entities and Concepts
### Organizations
- **China National Petroleum Corporation (CNPC)**: State-owned, largest oil company
- **China Petrochemical Corporation (Sinopec)**: Integrated oil and gas company
- **China National Offshore Oil Corporation (CNOOC)**: Offshore exploration specialist
- **National Energy Administration (NEA)**: Regulatory oversight body

### Key Metrics
- **Production**: 4.1 million barrels/day (2024)
- **Consumption**: 15.4 million barrels/day (2024)
- **Import Dependency**: 73% (increasing from 45% in 2010)
- **Refining Capacity**: 18.3 million barrels/day

### Market Dynamics
- **Pricing**: Influenced by Brent crude, government subsidies, and strategic reserves
- **Supply Sources**: Russia (19%), Saudi Arabia (16%), Iraq (11%), Angola (9%)
- **Infrastructure**: 95,000 km pipeline network, 600+ million barrel strategic reserves

## Related Conversations
### Recent Discussions (Last 30 Days)
1. **"CNPC quarterly earnings analysis"** - 2025-01-20
   - Key insights: Production costs rising, overseas expansion challenges
   - Relevance: 95% - Direct company analysis

2. **"China energy transition policies"** - 2025-01-18
   - Key insights: Government push for renewables, oil demand peak by 2030
   - Relevance: 88% - Policy impact on oil industry

3. **"Petroleum pricing mechanisms in Asia"** - 2025-01-15
   - Key insights: Regional pricing dynamics, China's influence on Asian markets
   - Relevance: 82% - Market structure analysis

### Historical Context (Older Discussions)
4. **"Oil import dependency risks"** - 2024-12-10
   - Key insights: Geopolitical vulnerabilities, strategic reserve importance
   - Relevance: 79% - Strategic considerations

## Linked Documents and Files
### Primary Sources
1. **china_energy_report_2024.pdf** (2.3MB)
   - Content: Official government energy statistics and projections
   - Key sections: Oil production data, consumption trends, policy framework
   - Relevance: 98% - Authoritative data source

2. **oil_market_analysis.xlsx** (1.8MB)
   - Content: Financial analysis, pricing models, market forecasts
   - Key sheets: Production costs, import data, company financials
   - Relevance: 95% - Quantitative analysis foundation

3. **petroleum_industry_notes.md** (45KB)
   - Content: Research notes from industry conferences and expert interviews
   - Key topics: Technology trends, regulatory changes, investment patterns
   - Relevance: 90% - Qualitative insights and expert opinions

### Supporting Materials
4. **cnpc_financial_data.docx** (890KB)
   - Content: Detailed financial analysis of China's largest oil company
   - Key metrics: Revenue trends, profit margins, capital expenditure
   - Relevance: 85% - Company-specific deep dive

## Research Insights and Analysis
### Market Opportunities
1. **Downstream Integration**: Refining and petrochemical expansion opportunities
2. **Technology Partnerships**: Advanced extraction and processing technologies
3. **Strategic Investments**: Overseas asset acquisitions in stable regions
4. **Green Transition**: Renewable energy integration and carbon capture

### Risk Factors
1. **Import Dependency**: Increasing reliance on volatile international markets
2. **Regulatory Changes**: Environmental policies affecting operations
3. **Geopolitical Tensions**: Supply chain disruption risks
4. **Energy Transition**: Long-term demand decline as renewables grow

### Investment Thesis
**Recommendation**: Cautious optimism with focus on integrated players
- **Short-term (1-2 years)**: Stable cash flows from existing operations
- **Medium-term (3-5 years)**: Consolidation opportunities and efficiency gains
- **Long-term (5+ years)**: Transition to energy services and renewables

## Action Items and Next Steps
### Immediate Research Needs
- [ ] Update Q4 2024 production and consumption data
- [ ] Analyze impact of recent US-China trade agreements on oil imports
- [ ] Research CNPC's overseas expansion strategy in Middle East

### Planned Deliverables
1. **Executive Summary Report** (Due: 2025-02-01)
   - Target audience: Investment committee
   - Key focus: Market opportunities and risk assessment

2. **Market Analysis Presentation** (Due: 2025-02-05)
   - Target audience: Senior management
   - Key focus: Strategic positioning recommendations

3. **Investment Recommendation Outline** (Due: 2025-02-10)
   - Target audience: Portfolio managers
   - Key focus: Specific investment opportunities and allocation

## Research Methodology Notes
### Data Sources
- **Primary**: Government statistics, company financial reports, industry associations
- **Secondary**: Research reports, news analysis, expert interviews
- **Validation**: Cross-referencing multiple sources, fact-checking key claims

### Analysis Framework
- **Quantitative**: Financial modeling, trend analysis, comparative metrics
- **Qualitative**: Expert opinions, policy analysis, strategic assessment
- **Scenario Planning**: Best case, base case, worst case projections

## Collaboration and Sharing
### Internal Stakeholders
- **Research Team**: Primary contributors and reviewers
- **Investment Committee**: Final decision makers
- **Risk Management**: Compliance and risk assessment

### External Experts
- **Industry Consultants**: Specialized knowledge and market insights
- **Academic Researchers**: Long-term trend analysis and policy implications
- **Regional Specialists**: China-specific cultural and regulatory expertise

---
*This master.md is automatically updated by the ChatLo intelligence system based on new conversations, document additions, and research progress. Last intelligence update: 2025-01-24 15:30:22*
```

### Master.md Content Categories

#### 1. **Research Overview Section**
- Context metadata (creation date, status, objectives)
- High-level research summary and current focus
- Key research questions and hypotheses

#### 2. **Knowledge Synthesis Section**
- **Key Findings**: Most important discoveries and insights
- **Core Entities**: People, organizations, concepts, metrics
- **Market Dynamics**: Trends, patterns, relationships

#### 3. **Source Integration Section**
- **Related Conversations**: Linked chat discussions with relevance scores
- **Linked Documents**: Connected files with content summaries
- **Cross-References**: Relationships between different sources

#### 4. **Analysis and Insights Section**
- **Research Insights**: Synthesized analysis from all sources
- **Opportunities and Risks**: Strategic implications
- **Investment/Decision Framework**: Actionable recommendations

#### 5. **Action-Oriented Section**
- **Next Steps**: Immediate research priorities
- **Planned Deliverables**: Reports, presentations, analyses to create
- **Collaboration Notes**: Stakeholders and external experts

## System Prompt for Master.md Generation

### Primary System Prompt
```
You are an intelligent research assistant specializing in creating comprehensive, professional master.md documents for research contexts. Your role is to synthesize information from multiple sources (conversations, documents, files) into a coherent, actionable research summary.

CONTEXT: You are working with local LLM capabilities optimized for data orchestration and research workflow enablement. Focus on practical, valuable synthesis rather than general AI assistance.

OBJECTIVE: Generate a master.md document that serves as the intelligent interface between human researchers and AI systems for a specific research context.

INPUT SOURCES:
- Chat conversations with extracted entities and topics
- Linked documents (PDF, DOCX, XLSX, MD) with content summaries
- File metadata and relevance scores
- User research objectives and context information

OUTPUT REQUIREMENTS:
1. **Professional Quality**: Suitable for business/academic use with minimal editing
2. **Actionable Content**: Enables follow-up tasks like report creation, presentations
3. **Structured Format**: Clear sections with consistent organization
4. **Source Attribution**: Proper citation and relevance scoring
5. **Research Continuity**: Supports ongoing research workflow

CONTENT STRUCTURE:
Follow the template structure with these sections:
- Research Overview (metadata, objectives, status)
- Key Findings Summary (most important insights)
- Core Entities and Concepts (organizations, metrics, dynamics)
- Related Conversations (linked chats with relevance scores)
- Linked Documents and Files (connected materials with summaries)
- Research Insights and Analysis (synthesized analysis)
- Action Items and Next Steps (immediate priorities and deliverables)
- Research Methodology Notes (sources, framework, validation)
- Collaboration and Sharing (stakeholders, experts)

SYNTHESIS GUIDELINES:
1. **Accuracy First**: Only include information that can be verified from sources
2. **Relevance Filtering**: Focus on content directly related to research objectives
3. **Insight Generation**: Identify patterns, relationships, and implications
4. **Professional Tone**: Business/academic writing style appropriate for stakeholders
5. **Actionable Focus**: Emphasize insights that enable decision-making

QUALITY STANDARDS:
- Information density: 80% useful content ratio
- Source attribution: 95% of claims properly cited
- Structural consistency: Follow template format exactly
- Professional readability: Suitable for executive/academic audiences
- Research utility: Enables creation of reports, presentations, analyses

CONSTRAINTS:
- Processing time: Complete generation within 500ms
- Content length: 2000-4000 words optimal for comprehensive coverage
- Source integration: Include all relevant linked materials
- Update capability: Support incremental updates as new sources are added

Remember: You are creating a living document that serves as the central hub for ongoing research. Focus on synthesis, insight generation, and enabling follow-up research tasks rather than just summarizing individual sources.
```

### Specialized Prompts by Research Domain

#### Business/Market Analysis Prompt Addition
```
DOMAIN SPECIALIZATION: Business and Market Analysis
Focus on: Market dynamics, competitive landscape, financial metrics, investment implications, strategic opportunities, risk assessment, regulatory environment, industry trends.

Key sections to emphasize:
- Market size, growth rates, and projections
- Competitive positioning and market share analysis
- Financial performance and key metrics
- Investment opportunities and risk factors
- Regulatory and policy implications
- Strategic recommendations and action items
```

#### Technology Research Prompt Addition
```
DOMAIN SPECIALIZATION: Technology Research and Development
Focus on: Technical capabilities, development trends, adoption patterns, innovation cycles, competitive technologies, implementation challenges, future roadmaps.

Key sections to emphasize:
- Technology capabilities and limitations
- Development trends and innovation patterns
- Adoption rates and market penetration
- Competitive technology landscape
- Implementation challenges and solutions
- Future development roadmap and predictions
```

#### Academic Research Prompt Addition
```
DOMAIN SPECIALIZATION: Academic and Scientific Research
Focus on: Research methodologies, literature review, theoretical frameworks, empirical findings, research gaps, future research directions, academic contributions.

Key sections to emphasize:
- Literature review and theoretical background
- Research methodologies and data sources
- Key findings and empirical evidence
- Research gaps and limitations
- Theoretical contributions and implications
- Future research directions and opportunities
```

## Next Steps
1. Implement automated testing tools
2. Create comprehensive test data sets
3. Establish continuous testing pipeline
4. Begin Level 1 testing with intent recognition validation
5. Test master.md generation with system prompts
6. Iterate based on test results and user feedback
