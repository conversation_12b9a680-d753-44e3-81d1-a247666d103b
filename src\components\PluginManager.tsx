/**
 * Plugin Management UI Component
 */

import React, { useState, useEffect } from 'react'
import { Settings, RefreshCw } from './Icons'

interface PluginInfo {
  id: string
  name: string
  version: string
  state: string
  capabilities: string[]
  description?: string
}

export const PluginManager: React.FC = () => {
  const [plugins, setPlugins] = useState<PluginInfo[]>([])
  const [loading, setLoading] = useState(true)
  
  useEffect(() => {
    loadPlugins()
  }, [])
  
  const loadPlugins = async () => {
    try {
      if (window.electronAPI?.plugins) {
        const pluginList = await window.electronAPI.plugins.getAll()
        setPlugins(pluginList)
      }
    } catch (error) {
      console.error('Error loading plugins:', error)
    } finally {
      setLoading(false)
    }
  }
  
  const togglePlugin = async (pluginId: string, enabled: boolean) => {
    try {
      if (window.electronAPI?.plugins) {
        await window.electronAPI.plugins.enable(pluginId, enabled)
        await loadPlugins() // Refresh list
      }
    } catch (error) {
      console.error('Error toggling plugin:', error)
    }
  }
  
  const discoverPlugins = async () => {
    try {
      if (window.electronAPI?.plugins) {
        await window.electronAPI.plugins.discover()
        await loadPlugins() // Refresh list
      }
    } catch (error) {
      console.error('Error discovering plugins:', error)
    }
  }
  
  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-gray-400">Manage plugin functionality and discover new plugins.</p>
        </div>
        <button
          onClick={discoverPlugins}
          className="flex items-center gap-2 px-4 py-2 bg-primary text-gray-900 rounded-lg hover:bg-primary/80 transition-colors font-medium"
        >
          <RefreshCw className="w-4 h-4" />
          Refresh Plugins
        </button>
      </div>
      
      <div className="space-y-4">
        {plugins.map(plugin => (
          <div key={plugin.id} className="bg-neutral-900/50 border border-neutral-800 rounded-lg p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h3 className="text-lg font-semibold text-supplement1">{plugin.name}</h3>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    plugin.state === 'active' ? 'bg-green-900/50 text-green-300 border border-green-800' :
                    plugin.state === 'disabled' ? 'bg-gray-900/50 text-gray-300 border border-gray-800' :
                    plugin.state === 'error' ? 'bg-red-900/50 text-red-300 border border-red-800' :
                    'bg-yellow-900/50 text-yellow-300 border border-yellow-800'
                  }`}>
                    {plugin.state}
                  </span>
                </div>
                <p className="text-sm text-gray-400 mb-2">v{plugin.version} • {plugin.id}</p>
                {plugin.description && (
                  <p className="text-gray-300 text-sm mb-3">{plugin.description}</p>
                )}
              </div>

              <div className="flex items-center gap-3">
                {/* Toggle Switch */}
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={plugin.state === 'active'}
                    onChange={(e) => togglePlugin(plugin.id, e.target.checked)}
                    className="sr-only peer"
                    disabled={plugin.state === 'error'}
                  />
                  <div className="w-11 h-6 bg-gray-700 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary peer-disabled:opacity-50 peer-disabled:cursor-not-allowed"></div>
                </label>
              </div>
            </div>
            
            {plugin.description && (
              <p className="text-gray-300 text-sm mb-3">{plugin.description}</p>
            )}

            <div className="flex flex-wrap gap-2">
              {plugin.capabilities.map(capability => (
                <span
                  key={capability}
                  className="px-2 py-1 bg-primary/20 text-primary rounded text-xs font-medium border border-primary/30"
                >
                  {capability.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                </span>
              ))}
            </div>
          </div>
        ))}
      </div>

      {plugins.length === 0 && (
        <div className="bg-neutral-900/50 border border-neutral-800 rounded-lg p-8">
          <div className="text-center">
            <Settings className="h-12 w-12 text-neutral-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-neutral-400 mb-2">No Plugins Found</h3>
            <p className="text-sm text-neutral-500 mb-4">
              No plugins are currently available. Try refreshing to discover plugins.
            </p>
            <button
              onClick={discoverPlugins}
              className="px-4 py-2 bg-primary text-gray-900 rounded-lg hover:bg-primary/80 transition-colors font-medium"
            >
              Refresh Plugins
            </button>
          </div>
        </div>
      )}
    </div>
  )
}