# Unified IPC Handler System Documentation

## Overview

This document describes the unified IPC (Inter-Process Communication) handler system that integrates seamlessly with ChatLo's plugin architecture. The system consolidates all IPC endpoints through a centralized APIRegistry, eliminating duplicate handler registrations and providing a consistent interface for both core functionality and plugin extensions.

## Architecture Integration

### Core Components

1. **APIRegistry** - Central hub for all IPC endpoint registration
2. **PluginManager** - Manages plugin lifecycle and API integration
3. **Unified IPC Handler** - Single point of IPC registration and management
4. **Plugin Extension Points** - Standardized interfaces for plugin API extensions

### System Flow

```mermaid
graph TD
    A[Main Process Startup] --> B[Initialize APIRegistry]
    B --> C[Register Core APIs]
    C --> D[Initialize PluginManager]
    D --> E[Discover Plugins]
    E --> F[Load Plugin APIs]
    F --> G[Unified IPC Registration]
    G --> H[Ready for Frontend Calls]
```

## Implementation

### 1. APIRegistry Core Structure

```typescript
// electron/api/APIRegistry.ts
export class APIRegistry {
  private categories = new Map<string, APICategory>()
  private middleware: Middleware[] = []

  registerCategory(name: string): void {
    if (!this.categories.has(name)) {
      this.categories.set(name, {
        name,
        endpoints: new Map(),
        middleware: []
      })
    }
  }

  registerEndpoint<T = any>(
    category: string,
    name: string,
    handler: (...args: any[]) => Promise<T> | T,
    options: EndpointOptions = {}
  ): void {
    const categoryObj = this.categories.get(category)
    if (!categoryObj) {
      throw new Error(`Category '${category}' not found`)
    }

    categoryObj.endpoints.set(name, {
      handler,
      validator: options.validator,
      middleware: options.middleware || [],
      description: options.description,
      permissions: options.permissions || []
    })
  }

  initialize(): void {
    console.log('[APIRegistry] Initializing IPC handlers...')
    
    for (const [categoryName, category] of this.categories.entries()) {
      for (const [endpointName, endpoint] of category.endpoints.entries()) {
        const channelName = `${categoryName}:${endpointName}`
        
        ipcMain.handle(channelName, async (event, ...args) => {
          try {
            // Apply validation
            if (endpoint.validator) {
              endpoint.validator(...args)
            }

            // Apply middleware
            for (const middleware of endpoint.middleware) {
              await middleware(event, ...args)
            }

            // Execute handler
            return await endpoint.handler(...args)
          } catch (error) {
            console.error(`[APIRegistry] Error in ${channelName}:`, error)
            throw error
          }
        })
      }
    }
  }
}
```

### 2. Unified IPC Handler Registration

```typescript
// electron/main.ts
export class ChatLoMain {
  private apiRegistry: APIRegistry
  private pluginManager: UniversalPluginManager

  constructor() {
    this.apiRegistry = new APIRegistry()
    this.pluginManager = new UniversalPluginManager(this.apiRegistry)
  }

  private setupIPC(): void {
    console.log('[MAIN] Setting up unified IPC system...')
    
    // Step 1: Register core API categories and endpoints
    this.registerCoreAPIs()
    
    // Step 2: Initialize plugin system and register plugin APIs
    this.initializePluginAPIs()
    
    // Step 3: Initialize all IPC handlers through APIRegistry
    this.apiRegistry.initialize()
    
    console.log('[MAIN] Unified IPC system ready')
  }

  private registerCoreAPIs(): void {
    // Database APIs
    this.apiRegistry.registerCategory('db')
    this.registerDatabaseEndpoints()

    // File System APIs
    this.apiRegistry.registerCategory('files')
    this.registerFileSystemEndpoints()

    // Vault APIs
    this.apiRegistry.registerCategory('vault')
    this.registerVaultEndpoints()

    // Settings APIs
    this.apiRegistry.registerCategory('settings')
    this.registerSettingsEndpoints()

    // Intelligence APIs
    this.apiRegistry.registerCategory('intelligence')
    this.registerIntelligenceEndpoints()

    // Plugin Management APIs
    this.apiRegistry.registerCategory('plugins')
    this.registerPluginEndpoints()
  }

  private async initializePluginAPIs(): Promise<void> {
    try {
      // Discover and load plugins
      const manifests = await this.pluginManager.discoverPlugins()
      
      for (const manifest of manifests) {
        await this.pluginManager.loadPlugin(manifest)
      }

      // Register plugin API extensions
      await this.pluginManager.registerPluginAPIs()
      
    } catch (error) {
      console.error('[MAIN] Plugin API initialization failed:', error)
    }
  }
}
```

### 3. Plugin API Integration

```typescript
// electron/plugins/PluginManager.ts
export class UniversalPluginManager {
  constructor(private apiRegistry: APIRegistry) {}

  async registerPluginAPIs(): Promise<void> {
    for (const [pluginId, plugin] of this.loadedPlugins.entries()) {
      try {
        // Check if plugin implements API extension
        if (this.implementsInterface(plugin, 'APIExtension')) {
          const apiExtension = plugin as APIExtension
          
          // Register plugin-specific API category
          const categoryName = `plugin_${pluginId}`
          this.apiRegistry.registerCategory(categoryName)
          
          // Let plugin register its endpoints
          apiExtension.registerEndpoints(this.apiRegistry, categoryName)
          
          console.log(`[PluginManager] Registered APIs for plugin: ${pluginId}`)
        }
      } catch (error) {
        console.error(`[PluginManager] Failed to register APIs for ${pluginId}:`, error)
      }
    }
  }
}
```

## Plugin API Extension Interface

### APIExtension Interface

```typescript
// electron/plugins/extensionPoints.ts
export interface APIExtension {
  registerEndpoints(apiRegistry: APIRegistry, categoryName: string): void
  provideMiddleware?(): Middleware[]
}
```

### Example Plugin Implementation

```typescript
// plugins/my-plugin/index.ts
export default class MyPlugin implements BasePlugin, APIExtension {
  // ... base plugin implementation

  registerEndpoints(apiRegistry: APIRegistry, categoryName: string): void {
    // Register custom endpoints
    apiRegistry.registerEndpoint(
      categoryName,
      'processData',
      async (data: any) => this.processData(data),
      {
        validator: (data: any) => {
          if (!data || typeof data !== 'object') {
            throw new Error('Invalid data object')
          }
        },
        description: 'Process custom data through plugin'
      }
    )

    apiRegistry.registerEndpoint(
      categoryName,
      'getStatus',
      () => this.getPluginStatus(),
      { description: 'Get plugin status information' }
    )
  }

  provideMiddleware(): Middleware[] {
    return [
      async (event, ...args) => {
        console.log(`[${this.name}] API call with args:`, args)
      }
    ]
  }

  private async processData(data: any): Promise<any> {
    // Plugin-specific data processing
    return { processed: true, result: data }
  }

  private getPluginStatus(): any {
    return {
      name: this.name,
      version: this.version,
      active: true,
      lastActivity: new Date().toISOString()
    }
  }
}
```

## Frontend Integration

### Unified API Client

```typescript
// src/lib/api.ts
class UnifiedAPIClient {
  async call<T = any>(
    category: string,
    endpoint: string,
    ...args: any[]
  ): Promise<T> {
    const channel = `${category}:${endpoint}`
    return window.electronAPI.invoke(channel, ...args)
  }

  // Core API methods
  async getConversations() {
    return this.call('db', 'getConversations')
  }

  async getIndexedFiles() {
    return this.call('files', 'getIndexedFiles')
  }

  async getAllPlugins() {
    return this.call('plugins', 'getAll')
  }

  // Plugin API methods
  async callPluginAPI<T = any>(
    pluginId: string,
    endpoint: string,
    ...args: any[]
  ): Promise<T> {
    return this.call(`plugin_${pluginId}`, endpoint, ...args)
  }
}

export const api = new UnifiedAPIClient()
```

## Benefits of Unified System

### 1. Eliminates Duplicate Handlers
- Single registration point prevents conflicts
- Clear separation between core and plugin APIs
- Consistent error handling across all endpoints

### 2. Plugin Integration
- Seamless API extension for plugins
- Automatic discovery and registration
- Standardized plugin API patterns

### 3. Maintainability
- Centralized endpoint management
- Easy debugging and monitoring
- Consistent validation and middleware

### 4. Scalability
- Easy addition of new API categories
- Plugin APIs don't interfere with core APIs
- Flexible middleware system

## Error Handling and Debugging

### Comprehensive Error Handling

```typescript
// Enhanced error handling in APIRegistry
async handleIPCCall(channelName: string, event: any, ...args: any[]) {
  try {
    const [category, endpoint] = channelName.split(':')
    const categoryObj = this.categories.get(category)
    
    if (!categoryObj) {
      throw new APIError(`Category '${category}' not found`, 'CATEGORY_NOT_FOUND')
    }

    const endpointObj = categoryObj.endpoints.get(endpoint)
    if (!endpointObj) {
      throw new APIError(`Endpoint '${endpoint}' not found in category '${category}'`, 'ENDPOINT_NOT_FOUND')
    }

    // Apply validation, middleware, and execute handler
    return await this.executeEndpoint(endpointObj, event, ...args)
    
  } catch (error) {
    console.error(`[APIRegistry] Error in ${channelName}:`, error)
    
    // Send structured error to frontend
    throw {
      message: error.message,
      code: error.code || 'UNKNOWN_ERROR',
      channel: channelName,
      timestamp: new Date().toISOString()
    }
  }
}
```

This unified IPC handler system provides a robust, scalable foundation that perfectly integrates with ChatLo's plugin architecture, ensuring seamless communication between the frontend and backend while supporting unlimited plugin extensibility.

## Advanced Integration Patterns

### 1. Plugin API Namespacing

```typescript
// Automatic plugin API namespacing prevents conflicts
export class PluginAPIManager {
  private generatePluginNamespace(pluginId: string): string {
    return `plugin_${pluginId.toLowerCase().replace(/[^a-z0-9]/g, '_')}`
  }

  async registerPluginAPI(plugin: BasePlugin & APIExtension): Promise<void> {
    const namespace = this.generatePluginNamespace(plugin.id)

    // Create isolated category for plugin
    this.apiRegistry.registerCategory(namespace)

    // Register plugin endpoints with validation
    plugin.registerEndpoints(this.apiRegistry, namespace)

    // Track plugin API registration
    this.pluginAPIRegistry.set(plugin.id, {
      namespace,
      endpoints: this.getPluginEndpoints(namespace),
      registeredAt: new Date()
    })
  }
}
```

### 2. Dynamic API Discovery

```typescript
// Frontend can discover available plugin APIs dynamically
export class DynamicAPIDiscovery {
  async discoverPluginAPIs(): Promise<PluginAPIInfo[]> {
    const plugins = await api.call('plugins', 'getAll')
    const apiInfo: PluginAPIInfo[] = []

    for (const plugin of plugins) {
      if (plugin.capabilities.includes('API_EXTENSION')) {
        const endpoints = await api.call('plugins', 'getAPIEndpoints', plugin.id)
        apiInfo.push({
          pluginId: plugin.id,
          pluginName: plugin.name,
          namespace: `plugin_${plugin.id}`,
          endpoints,
          version: plugin.version
        })
      }
    }

    return apiInfo
  }

  // Generate TypeScript interfaces for plugin APIs
  generatePluginAPITypes(apiInfo: PluginAPIInfo[]): string {
    return apiInfo.map(plugin => `
      interface ${plugin.pluginName.replace(/\s+/g, '')}API {
        ${plugin.endpoints.map(endpoint =>
          `${endpoint.name}(${endpoint.parameters.map(p => `${p.name}: ${p.type}`).join(', ')}): Promise<${endpoint.returnType}>`
        ).join('\n        ')}
      }
    `).join('\n')
  }
}
```

### 3. Plugin API Middleware Chain

```typescript
// Advanced middleware system for plugin APIs
export class PluginMiddlewareChain {
  private globalMiddleware: Middleware[] = []
  private pluginMiddleware = new Map<string, Middleware[]>()

  addGlobalMiddleware(middleware: Middleware): void {
    this.globalMiddleware.push(middleware)
  }

  addPluginMiddleware(pluginId: string, middleware: Middleware): void {
    if (!this.pluginMiddleware.has(pluginId)) {
      this.pluginMiddleware.set(pluginId, [])
    }
    this.pluginMiddleware.get(pluginId)!.push(middleware)
  }

  async executeMiddlewareChain(
    pluginId: string,
    event: any,
    ...args: any[]
  ): Promise<void> {
    // Execute global middleware first
    for (const middleware of this.globalMiddleware) {
      await middleware(event, ...args)
    }

    // Execute plugin-specific middleware
    const pluginMW = this.pluginMiddleware.get(pluginId) || []
    for (const middleware of pluginMW) {
      await middleware(event, ...args)
    }
  }
}
```

### 4. Real-time Plugin API Monitoring

```typescript
// Monitor plugin API usage and performance
export class PluginAPIMonitor {
  private metrics = new Map<string, APIMetrics>()

  trackAPICall(pluginId: string, endpoint: string, duration: number, success: boolean): void {
    const key = `${pluginId}:${endpoint}`

    if (!this.metrics.has(key)) {
      this.metrics.set(key, {
        pluginId,
        endpoint,
        totalCalls: 0,
        successfulCalls: 0,
        failedCalls: 0,
        averageDuration: 0,
        lastCalled: null
      })
    }

    const metric = this.metrics.get(key)!
    metric.totalCalls++
    metric.lastCalled = new Date()

    if (success) {
      metric.successfulCalls++
    } else {
      metric.failedCalls++
    }

    // Update average duration
    metric.averageDuration = (metric.averageDuration * (metric.totalCalls - 1) + duration) / metric.totalCalls
  }

  getPluginAPIMetrics(pluginId: string): APIMetrics[] {
    return Array.from(this.metrics.values()).filter(m => m.pluginId === pluginId)
  }

  generatePerformanceReport(): PerformanceReport {
    const plugins = new Map<string, PluginPerformance>()

    for (const metric of this.metrics.values()) {
      if (!plugins.has(metric.pluginId)) {
        plugins.set(metric.pluginId, {
          pluginId: metric.pluginId,
          totalAPICalls: 0,
          averageResponseTime: 0,
          successRate: 0,
          endpoints: []
        })
      }

      const plugin = plugins.get(metric.pluginId)!
      plugin.totalAPICalls += metric.totalCalls
      plugin.endpoints.push(metric)
    }

    // Calculate aggregated metrics
    for (const plugin of plugins.values()) {
      const totalSuccess = plugin.endpoints.reduce((sum, e) => sum + e.successfulCalls, 0)
      const totalCalls = plugin.endpoints.reduce((sum, e) => sum + e.totalCalls, 0)
      const avgDuration = plugin.endpoints.reduce((sum, e) => sum + e.averageDuration, 0) / plugin.endpoints.length

      plugin.successRate = totalCalls > 0 ? (totalSuccess / totalCalls) * 100 : 0
      plugin.averageResponseTime = avgDuration
    }

    return {
      generatedAt: new Date(),
      plugins: Array.from(plugins.values()),
      summary: this.generateSummary(Array.from(plugins.values()))
    }
  }
}
```

## Complete Integration Example

### Plugin with Full API Integration

```typescript
// plugins/advanced-analytics/index.ts
export default class AdvancedAnalyticsPlugin implements BasePlugin, APIExtension, ChatExtension {
  id = 'advanced-analytics'
  name = 'Advanced Analytics Plugin'
  version = '2.0.0'

  private analyticsEngine: AnalyticsEngine
  private dataCache = new Map<string, any>()

  async initialize(): Promise<void> {
    this.analyticsEngine = new AnalyticsEngine()
    await this.analyticsEngine.initialize()
  }

  getCapabilities(): PluginCapability[] {
    return [
      PluginCapability.API_EXTENSION,
      PluginCapability.CHAT_ENHANCEMENT,
      PluginCapability.INTELLIGENCE_PROCESSING
    ]
  }

  // API Extension Implementation
  registerEndpoints(apiRegistry: APIRegistry, categoryName: string): void {
    // Analytics endpoints
    apiRegistry.registerEndpoint(
      categoryName,
      'analyzeConversation',
      async (conversationId: string) => this.analyzeConversation(conversationId),
      {
        validator: (conversationId: string) => {
          if (!conversationId || typeof conversationId !== 'string') {
            throw new Error('Valid conversation ID required')
          }
        },
        description: 'Analyze conversation patterns and insights'
      }
    )

    apiRegistry.registerEndpoint(
      categoryName,
      'getAnalyticsDashboard',
      async (timeRange?: string) => this.getAnalyticsDashboard(timeRange),
      {
        description: 'Get analytics dashboard data'
      }
    )

    apiRegistry.registerEndpoint(
      categoryName,
      'exportAnalytics',
      async (format: 'json' | 'csv' | 'pdf') => this.exportAnalytics(format),
      {
        validator: (format: string) => {
          if (!['json', 'csv', 'pdf'].includes(format)) {
            throw new Error('Invalid export format')
          }
        },
        description: 'Export analytics data in specified format'
      }
    )
  }

  // Chat Enhancement Implementation
  async afterMessageReceived(message: any): Promise<any> {
    // Analyze message for insights
    const insights = await this.analyticsEngine.analyzeMessage(message)

    // Store insights for dashboard
    this.dataCache.set(`message_${message.id}`, insights)

    return {
      ...message,
      analytics: insights
    }
  }

  private async analyzeConversation(conversationId: string): Promise<ConversationAnalytics> {
    const conversation = await this.getConversation(conversationId)
    return this.analyticsEngine.analyzeConversation(conversation)
  }

  private async getAnalyticsDashboard(timeRange?: string): Promise<AnalyticsDashboard> {
    return this.analyticsEngine.generateDashboard(timeRange)
  }

  private async exportAnalytics(format: string): Promise<ExportResult> {
    const data = await this.analyticsEngine.getAllAnalytics()
    return this.analyticsEngine.export(data, format)
  }
}
```

### Frontend Integration with Plugin APIs

```typescript
// src/components/PluginAnalytics.tsx
export const PluginAnalytics: React.FC = () => {
  const [analytics, setAnalytics] = useState<AnalyticsDashboard | null>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    loadAnalytics()
  }, [])

  const loadAnalytics = async () => {
    setLoading(true)
    try {
      // Call plugin API through unified system
      const data = await api.callPluginAPI(
        'advanced-analytics',
        'getAnalyticsDashboard',
        '30d'
      )
      setAnalytics(data)
    } catch (error) {
      console.error('Failed to load analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  const exportData = async (format: 'json' | 'csv' | 'pdf') => {
    try {
      const result = await api.callPluginAPI(
        'advanced-analytics',
        'exportAnalytics',
        format
      )

      // Handle export result
      if (result.downloadUrl) {
        window.open(result.downloadUrl)
      }
    } catch (error) {
      console.error('Export failed:', error)
    }
  }

  if (loading) return <div>Loading analytics...</div>

  return (
    <div className="plugin-analytics">
      <h2>Advanced Analytics</h2>
      {analytics && (
        <div>
          <div className="metrics-grid">
            {analytics.metrics.map(metric => (
              <MetricCard key={metric.name} metric={metric} />
            ))}
          </div>

          <div className="export-controls">
            <button onClick={() => exportData('json')}>Export JSON</button>
            <button onClick={() => exportData('csv')}>Export CSV</button>
            <button onClick={() => exportData('pdf')}>Export PDF</button>
          </div>
        </div>
      )}
    </div>
  )
}
```

This comprehensive unified IPC handler system seamlessly integrates with ChatLo's plugin architecture, providing a robust foundation for both core functionality and unlimited plugin extensibility while maintaining clean separation of concerns and excellent developer experience.
