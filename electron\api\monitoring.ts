/**
 * API Monitoring and Metrics System
 * Provides real-time performance tracking, health monitoring, and analytics
 */

export interface APIMetric {
  endpoint: string
  category: string
  totalCalls: number
  successfulCalls: number
  failedCalls: number
  totalTime: number
  averageTime: number
  minTime: number
  maxTime: number
  lastCall: number
  errorRate: number
  callsPerMinute: number
  recentCalls: Array<{
    timestamp: number
    duration: number
    success: boolean
    error?: string
  }>
}

export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy'
  uptime: number
  totalRequests: number
  errorRate: number
  averageResponseTime: number
  memoryUsage: NodeJS.MemoryUsage
  activeConnections: number
  lastHealthCheck: number
}

export interface AlertRule {
  id: string
  name: string
  condition: 'error_rate' | 'response_time' | 'request_count'
  threshold: number
  timeWindow: number // in milliseconds
  enabled: boolean
  lastTriggered?: number
}

export interface Alert {
  id: string
  ruleId: string
  message: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  timestamp: number
  resolved: boolean
  resolvedAt?: number
}

export class APIMonitor {
  private metrics: Map<string, APIMetric> = new Map()
  private alerts: Alert[] = []
  private alertRules: AlertRule[] = []
  private startTime: number = Date.now()
  private healthCheckInterval?: NodeJS.Timeout
  private maxRecentCalls = 100 // Keep last 100 calls per endpoint

  constructor() {
    this.setupDefaultAlertRules()
    this.startHealthChecking()
  }

  // Record API call metrics
  recordCall(
    category: string,
    endpoint: string,
    duration: number,
    success: boolean,
    error?: string
  ): void {
    const key = `${category}:${endpoint}`
    let metric = this.metrics.get(key)

    if (!metric) {
      metric = {
        endpoint,
        category,
        totalCalls: 0,
        successfulCalls: 0,
        failedCalls: 0,
        totalTime: 0,
        averageTime: 0,
        minTime: duration,
        maxTime: duration,
        lastCall: Date.now(),
        errorRate: 0,
        callsPerMinute: 0,
        recentCalls: []
      }
    }

    // Update basic metrics
    metric.totalCalls++
    metric.totalTime += duration
    metric.averageTime = metric.totalTime / metric.totalCalls
    metric.minTime = Math.min(metric.minTime, duration)
    metric.maxTime = Math.max(metric.maxTime, duration)
    metric.lastCall = Date.now()

    if (success) {
      metric.successfulCalls++
    } else {
      metric.failedCalls++
    }

    metric.errorRate = (metric.failedCalls / metric.totalCalls) * 100

    // Add to recent calls (keep only last N calls)
    metric.recentCalls.push({
      timestamp: Date.now(),
      duration,
      success,
      error
    })

    if (metric.recentCalls.length > this.maxRecentCalls) {
      metric.recentCalls.shift()
    }

    // Calculate calls per minute based on recent calls
    const oneMinuteAgo = Date.now() - 60000
    const recentCallsInLastMinute = metric.recentCalls.filter(
      call => call.timestamp > oneMinuteAgo
    ).length
    metric.callsPerMinute = recentCallsInLastMinute

    this.metrics.set(key, metric)

    // Check alert rules
    this.checkAlertRules(metric)
  }

  // Get all metrics
  getAllMetrics(): Record<string, APIMetric> {
    const result: Record<string, APIMetric> = {}
    for (const [key, metric] of this.metrics.entries()) {
      result[key] = { ...metric }
    }
    return result
  }

  // Get metrics for specific endpoint
  getEndpointMetrics(category: string, endpoint: string): APIMetric | null {
    const key = `${category}:${endpoint}`
    const metric = this.metrics.get(key)
    return metric ? { ...metric } : null
  }

  // Get system health status
  getSystemHealth(): SystemHealth {
    const now = Date.now()
    const totalRequests = Array.from(this.metrics.values()).reduce(
      (sum, metric) => sum + metric.totalCalls, 0
    )
    const totalErrors = Array.from(this.metrics.values()).reduce(
      (sum, metric) => sum + metric.failedCalls, 0
    )
    const totalTime = Array.from(this.metrics.values()).reduce(
      (sum, metric) => sum + metric.totalTime, 0
    )

    const errorRate = totalRequests > 0 ? (totalErrors / totalRequests) * 100 : 0
    const averageResponseTime = totalRequests > 0 ? totalTime / totalRequests : 0

    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy'
    if (errorRate > 10 || averageResponseTime > 5000) {
      status = 'unhealthy'
    } else if (errorRate > 5 || averageResponseTime > 2000) {
      status = 'degraded'
    }

    return {
      status,
      uptime: now - this.startTime,
      totalRequests,
      errorRate,
      averageResponseTime,
      memoryUsage: process.memoryUsage(),
      activeConnections: 0, // This would need to be tracked separately
      lastHealthCheck: now
    }
  }

  // Get top endpoints by various metrics
  getTopEndpoints(metric: 'calls' | 'errors' | 'time', limit = 10): Array<{
    endpoint: string
    category: string
    value: number
  }> {
    const endpoints = Array.from(this.metrics.values())
    
    let sortFn: (a: APIMetric, b: APIMetric) => number
    let valueFn: (metric: APIMetric) => number

    switch (metric) {
      case 'calls':
        sortFn = (a, b) => b.totalCalls - a.totalCalls
        valueFn = (m) => m.totalCalls
        break
      case 'errors':
        sortFn = (a, b) => b.failedCalls - a.failedCalls
        valueFn = (m) => m.failedCalls
        break
      case 'time':
        sortFn = (a, b) => b.averageTime - a.averageTime
        valueFn = (m) => m.averageTime
        break
    }

    return endpoints
      .sort(sortFn)
      .slice(0, limit)
      .map(m => ({
        endpoint: m.endpoint,
        category: m.category,
        value: valueFn(m)
      }))
  }

  // Alert management
  private setupDefaultAlertRules(): void {
    this.alertRules = [
      {
        id: 'high-error-rate',
        name: 'High Error Rate',
        condition: 'error_rate',
        threshold: 10, // 10%
        timeWindow: 300000, // 5 minutes
        enabled: true
      },
      {
        id: 'slow-response',
        name: 'Slow Response Time',
        condition: 'response_time',
        threshold: 5000, // 5 seconds
        timeWindow: 300000, // 5 minutes
        enabled: true
      },
      {
        id: 'high-request-volume',
        name: 'High Request Volume',
        condition: 'request_count',
        threshold: 1000, // 1000 requests
        timeWindow: 60000, // 1 minute
        enabled: true
      }
    ]
  }

  private checkAlertRules(metric: APIMetric): void {
    const now = Date.now()

    for (const rule of this.alertRules) {
      if (!rule.enabled) continue

      // Don't trigger the same rule too frequently
      if (rule.lastTriggered && (now - rule.lastTriggered) < rule.timeWindow) {
        continue
      }

      let shouldTrigger = false
      let message = ''

      switch (rule.condition) {
        case 'error_rate':
          if (metric.errorRate > rule.threshold) {
            shouldTrigger = true
            message = `High error rate detected: ${metric.errorRate.toFixed(2)}% for ${metric.category}:${metric.endpoint}`
          }
          break
        case 'response_time':
          if (metric.averageTime > rule.threshold) {
            shouldTrigger = true
            message = `Slow response time detected: ${metric.averageTime.toFixed(2)}ms for ${metric.category}:${metric.endpoint}`
          }
          break
        case 'request_count':
          if (metric.callsPerMinute > rule.threshold) {
            shouldTrigger = true
            message = `High request volume detected: ${metric.callsPerMinute} requests/min for ${metric.category}:${metric.endpoint}`
          }
          break
      }

      if (shouldTrigger) {
        this.triggerAlert(rule, message)
        rule.lastTriggered = now
      }
    }
  }

  private triggerAlert(rule: AlertRule, message: string): void {
    const alert: Alert = {
      id: `alert-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      ruleId: rule.id,
      message,
      severity: this.getSeverityForRule(rule),
      timestamp: Date.now(),
      resolved: false
    }

    this.alerts.push(alert)
    console.warn(`[API ALERT] ${alert.severity.toUpperCase()}: ${message}`)

    // Keep only last 1000 alerts
    if (this.alerts.length > 1000) {
      this.alerts = this.alerts.slice(-1000)
    }
  }

  private getSeverityForRule(rule: AlertRule): Alert['severity'] {
    switch (rule.condition) {
      case 'error_rate':
        return rule.threshold > 20 ? 'critical' : rule.threshold > 10 ? 'high' : 'medium'
      case 'response_time':
        return rule.threshold > 10000 ? 'critical' : rule.threshold > 5000 ? 'high' : 'medium'
      case 'request_count':
        return rule.threshold > 5000 ? 'critical' : rule.threshold > 1000 ? 'high' : 'medium'
      default:
        return 'medium'
    }
  }

  private startHealthChecking(): void {
    this.healthCheckInterval = setInterval(() => {
      const health = this.getSystemHealth()
      if (health.status !== 'healthy') {
        console.warn(`[SYSTEM HEALTH] Status: ${health.status}, Error Rate: ${health.errorRate.toFixed(2)}%, Avg Response: ${health.averageResponseTime.toFixed(2)}ms`)
      }
    }, 60000) // Check every minute
  }

  // Get active alerts
  getActiveAlerts(): Alert[] {
    return this.alerts.filter(alert => !alert.resolved)
  }

  // Get all alerts
  getAllAlerts(limit = 100): Alert[] {
    return this.alerts.slice(-limit)
  }

  // Resolve alert
  resolveAlert(alertId: string): boolean {
    const alert = this.alerts.find(a => a.id === alertId)
    if (alert && !alert.resolved) {
      alert.resolved = true
      alert.resolvedAt = Date.now()
      return true
    }
    return false
  }

  // Reset all metrics
  resetMetrics(): void {
    this.metrics.clear()
    this.alerts = []
    this.startTime = Date.now()
  }

  // Cleanup resources
  cleanup(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval)
    }
  }
}
