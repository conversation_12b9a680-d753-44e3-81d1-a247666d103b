import React from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { Message, IntelligenceExtractionData, VaultAssignment } from '../types'
import { <PERSON><PERSON>, User, Co<PERSON>, Check, ChevronDown, ChevronRight, Brain, <PERSON>fresh<PERSON><PERSON>, Pin } from './Icons'
import { useState, useEffect } from 'react'
import FileAttachmentDisplay from './FileAttachmentDisplay'
import { useArtifactDetection } from '../hooks/useArtifactDetection'
import { InlineArtifactButton, FloatingArtifactIndicator } from './artifacts/controls/InlineArtifactButton'
import { VaultSuggestionModal } from './VaultSuggestionModal'
import { intelligenceService } from '../services/intelligenceService'
import { intelligenceAnalytics } from '../services/intelligenceAnalytics'

interface MessageBubbleProps {
  message: Message;
  onRegenerate: (messageId: string) => void;
  onPinMessage: (messageId: string) => void;
}

interface ReasoningSection {
  type: 'thinking' | 'reasoning' | 'analysis'
  content: string
  title: string
}

interface ParsedContent {
  hasReasoning: boolean
  reasoningSections: ReasoningSection[]
  finalAnswer: string
}

// Utility function to parse reasoning content
const parseReasoningContent = (content: string): ParsedContent => {
  const reasoningPatterns = [
    // OpenAI o1 style thinking tags
    { pattern: /<thinking>([\s\S]*?)<\/thinking>/gi, type: 'thinking' as const, title: 'Thinking Process' },
    // DeepSeek R1 style thinking tags
    { pattern: /<think>([\s\S]*?)<\/think>/gi, type: 'thinking' as const, title: 'Reasoning' },
    // Generic reasoning patterns
    { pattern: /\*\*Reasoning:\*\*([\s\S]*?)(?=\*\*|$)/gi, type: 'reasoning' as const, title: 'Reasoning' },
    { pattern: /\*\*Analysis:\*\*([\s\S]*?)(?=\*\*|$)/gi, type: 'analysis' as const, title: 'Analysis' },
    // Step-by-step thinking
    { pattern: /Let me think through this step by step:([\s\S]*?)(?=\n\n|\n[A-Z]|$)/gi, type: 'thinking' as const, title: 'Step-by-step Analysis' },
    // Chain of thought patterns
    { pattern: /(?:First|Initially|Let me consider|I need to think about)([\s\S]*?)(?=\n\n(?:[A-Z]|In conclusion|Therefore|So)|$)/gi, type: 'thinking' as const, title: 'Chain of Thought' }
  ]

  const reasoningSections: ReasoningSection[] = []
  let processedContent = content

  // Extract reasoning sections
  for (const { pattern, type, title } of reasoningPatterns) {
    let match
    while ((match = pattern.exec(content)) !== null) {
      reasoningSections.push({
        type,
        content: match[1].trim(),
        title
      })
      // Remove the reasoning section from the final answer
      processedContent = processedContent.replace(match[0], '').trim()
    }
  }

  // Clean up the final answer
  const finalAnswer = processedContent
    .replace(/\n{3,}/g, '\n\n') // Remove excessive line breaks
    .trim()

  return {
    hasReasoning: reasoningSections.length > 0,
    reasoningSections,
    finalAnswer: finalAnswer || content // Fallback to original content if nothing left
  }
}

const MessageBubble: React.FC<MessageBubbleProps> = ({ message, onRegenerate, onPinMessage }) => {
  const [copied, setCopied] = useState(false)
  const [expandedSections, setExpandedSections] = useState<Set<number>>(new Set())
  const [detectedArtifacts, setDetectedArtifacts] = useState<any[]>([])
  const [showVaultModal, setShowVaultModal] = useState(false)
  const [extractionData, setExtractionData] = useState<IntelligenceExtractionData | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const isUser = message.role === 'user'
  const { processMessageForArtifacts } = useArtifactDetection()

  // Detect artifacts when message content changes
  useEffect(() => {
    if (!isUser && message.content) {
      const result = processMessageForArtifacts(message.content, message.id)
      setDetectedArtifacts(result.artifacts)
    }
  }, [message.content, message.id, isUser, processMessageForArtifacts])

  const handlePin = async () => {
    if (message.is_pinned) {
      // If already pinned, just toggle off
      onPinMessage(message.id)
      return
    }

    // If not pinned, extract intelligence first
    setIsProcessing(true)

    try {
      const result = await intelligenceService.extractIntelligence(
        message.content,
        message.attachments?.map(att => att.file).filter(Boolean)
      )

      setExtractionData(result.data)

      // Update message with intelligence data
      await window.electronAPI.db.updateMessageIntelligence(
        message.id,
        JSON.stringify(result.data.entities),
        JSON.stringify(result.data.topics),
        result.data.entities.length > 0 || result.data.topics.length > 0 ? 0.8 : 0.3
      )

      // Show vault suggestion modal
      setShowVaultModal(true)

    } catch (error) {
      console.error('Error extracting intelligence:', error)
      // Fallback to simple pin
      onPinMessage(message.id)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleVaultAssignment = async (assignment: VaultAssignment) => {
    try {
      // Pin the message first
      onPinMessage(message.id)

      // Store intelligence data if vault was assigned
      if (assignment.vault_id && extractionData) {
        await window.electronAPI.db.addPinnedIntelligence(
          message.id,
          JSON.stringify(extractionData),
          JSON.stringify(assignment),
          JSON.stringify({
            extraction_time_ms: 0, // Will be updated by the service
            model_used: 'lightweight_nlp',
            processing_version: '1.0'
          })
        )

        // Update vault's master.md file
        if (assignment.vault_path && extractionData) {
          const masterUpdateSuccess = await intelligenceService.updateVaultMasterFile(
            assignment.vault_path,
            message.content,
            extractionData,
            message.id
          )

          if (masterUpdateSuccess) {
            console.log('Master.md updated successfully for vault:', assignment.vault_id)
          } else {
            console.warn('Failed to update master.md for vault:', assignment.vault_id)
          }
        }

        console.log('Message pinned and assigned to vault:', assignment.vault_id)
      }

      // Track analytics for message pinning
      if (extractionData) {
        intelligenceAnalytics.trackMessagePin(message.id, extractionData, assignment)
      }

    } catch (error) {
      console.error('Error handling vault assignment:', error)
    }

    setShowVaultModal(false)
    setExtractionData(null)
  }
  
  // Parse content for reasoning sections
  const parsedContent = !isUser ? parseReasoningContent(message.content) : null

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(message.content)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy message:', error)
    }
  }

  const toggleSection = (index: number) => {
    const newExpanded = new Set(expandedSections)
    if (newExpanded.has(index)) {
      newExpanded.delete(index)
    } else {
      newExpanded.add(index)
    }
    setExpandedSections(newExpanded)
  }

  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
  }

  const MarkdownComponents = {
    // Custom component for links
    a: ({ ...props }: any) => (
      <a {...props} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline" />
    ),
    // Custom component for code blocks
    code({ inline, className, children, ...props }: any) {
      const match = /language-(\w+)/.exec(className || '')
      return !inline && match ? (
        <div className="bg-gray-900 rounded-md my-2">
          <div className="flex items-center justify-between px-4 py-1 bg-gray-700/50 rounded-t-md">
            <span className="text-xs font-sans text-gray-400">{match[1]}</span>
            <button
              onClick={() => navigator.clipboard.writeText(String(children))}
              className="text-xs text-gray-400 hover:text-supplement1"
            >
              Copy
            </button>
          </div>
          <pre className="p-4 overflow-x-auto">
            <code {...props} className="text-sm">
              {children}
            </code>
          </pre>
        </div>
      ) : (
        <code {...props} className="bg-gray-700 text-primary rounded-sm px-1 py-0.5 text-sm">
          {children}
        </code>
      )
    },
    // Add more custom components as needed for other elements
    h1: ({...props}: any) => <h1 {...props} className="text-2xl font-bold my-4" />,
    h2: ({...props}: any) => <h2 {...props} className="text-xl font-bold my-3" />,
    h3: ({...props}: any) => <h3 {...props} className="text-lg font-bold my-2" />,
    ul: ({...props}: any) => <ul {...props} className="list-disc list-inside my-2" />,
    ol: ({...props}: any) => <ol {...props} className="list-decimal list-inside my-2" />,
    li: ({...props}: any) => <li {...props} className="my-1" />,
    blockquote: ({...props}: any) => <blockquote {...props} className="border-l-4 border-tertiary pl-4 my-2 italic" />,
  }

  return (
    <div className={`flex items-start gap-3 ${isUser ? 'justify-end' : ''} animate-fade-in-up`}>
      {!isUser && (
        <div className="h-8 w-8 rounded-full bg-secondary flex items-center justify-center shrink-0">
          <Bot className="h-4 w-4 text-white" />
        </div>
      )}
      
      <div className={`
        ${!isUser && detectedArtifacts.length > 0 ? 'max-w-xl' : 'max-w-md'}
        ${isUser ? 'text-right' : ''}
      `}>
        <div className="group relative">
          <div className={`
            text-sm rounded-lg px-4 py-2 mb-1 break-words
            ${isUser
              ? 'u1-chat-bubble-user'
              : 'u1-chat-bubble-assistant'
            }
          `}>
            {/* Render reasoning sections if present */}
            {!isUser && parsedContent?.hasReasoning ? (
              <div className="space-y-3">
                {/* Reasoning sections */}
                {parsedContent.reasoningSections.map((section, index) => (
                  <div key={index} className="border border-tertiary rounded-lg overflow-hidden">
                    <button
                      onClick={() => toggleSection(index)}
                      className="w-full flex items-center gap-2 p-3 bg-gray-700/50 hover:bg-gray-700 transition-colors text-left"
                    >
                      {expandedSections.has(index) ? (
                        <ChevronDown className="h-4 w-4 text-neutral-400" />
                      ) : (
                        <ChevronRight className="h-4 w-4 text-neutral-400" />
                      )}
                      <Brain className="h-4 w-4 text-primary" />
                      <span className="font-medium text-supplement1">{section.title}</span>
                      <span className="text-xs text-gray-500 ml-auto">
                        {section.content.length} chars
                      </span>
                    </button>

                    {expandedSections.has(index) && (
                      <div className="p-3 bg-gray-800/50 border-t border-tertiary">
                        <div className="prose prose-invert prose-sm max-w-none">
                          <ReactMarkdown remarkPlugins={[remarkGfm]} components={MarkdownComponents}>
                            {section.content}
                          </ReactMarkdown>
                        </div>
                      </div>
                    )}
                  </div>
                ))}

                {/* Final answer */}
                {parsedContent.finalAnswer && (
                  <div className="pt-3 border-t border-tertiary">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="h-2 w-2 bg-green-400 rounded-full"></div>
                      <span className="text-xs font-medium text-primary uppercase tracking-wide">Final Answer</span>
                    </div>
                    <div className="prose prose-invert prose-sm max-w-none">
                      <ReactMarkdown remarkPlugins={[remarkGfm]} components={MarkdownComponents}>
                        {parsedContent.finalAnswer}
                      </ReactMarkdown>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              /* Regular message content */
              <div className="prose prose-invert prose-sm max-w-none">
                <ReactMarkdown remarkPlugins={[remarkGfm]} components={MarkdownComponents}>
                  {message.content}
                </ReactMarkdown>
              </div>
            )}

            {/* File attachments */}
            {message.attachments && message.attachments.length > 0 && (
              <FileAttachmentDisplay
                files={message.attachments.map(att => att.file!).filter(Boolean)}
                mode="full"
                showPreview={true}
              />
            )}

            {/* Artifact buttons */}
            {!isUser && detectedArtifacts && detectedArtifacts.length > 0 && (
              <div className="mt-3 pt-3 border-t border-tertiary">
                <div className="flex flex-wrap gap-2">
                  {detectedArtifacts.map((artifact) => (
                    <InlineArtifactButton
                      key={artifact.id}
                      artifact={artifact}
                      variant="default"
                    />
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Floating artifact indicator */}
          {!isUser && detectedArtifacts && detectedArtifacts.length > 0 && (
            <FloatingArtifactIndicator artifacts={detectedArtifacts} />
          )}

          {/* Action buttons */}
          <div className={`
            absolute top-2 ${isUser ? 'left-2' : 'right-2'}
            opacity-0 group-hover:opacity-100 transition-opacity
            flex gap-1
          `}>
            <button
              onClick={handleCopy}
              className="p-1 rounded hover:bg-black/20 text-xs"
              title="Copy message"
            >
              {copied ? (
                <Check className="h-3 w-3" />
              ) : (
                <Copy className="h-3 w-3" />
              )}
            </button>
            {!isUser && (
              <button
                onClick={handlePin}
                disabled={isProcessing}
                className={`p-1 rounded hover:bg-black/20 text-xs ${
                  message.is_pinned ? 'text-yellow-400' : ''
                } ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}`}
                title={
                  isProcessing
                    ? "Processing..."
                    : message.is_pinned
                      ? "Unpin message"
                      : "Pin message"
                }
              >
                {isProcessing ? (
                  <Brain className="h-3 w-3 animate-pulse" />
                ) : (
                  <Pin className="h-3 w-3" />
                )}
              </button>
            )}
          </div>
        </div>
        
        <div className={`flex items-center gap-2 text-xs text-gray-500 ${isUser ? 'justify-end' : ''}`}>
          <span>{formatTime(message.created_at)}</span>
          {message.model && !isUser && (
            <>
              <span>•</span>
              <span className="text-primary">{message.model}</span>
            </>
          )}
          {message.is_pinned === 1 && (
            <>
              <span>•</span>
              <span className="text-secondary flex items-center gap-1">
                <Pin className="h-3 w-3" />
                Pinned
              </span>
            </>
          )}
          {!isUser && (
            <div className="flex items-center gap-2">
              <button onClick={() => onRegenerate(message.id)} className="hover:text-white">
                <RefreshCw className="h-3 w-3" />
              </button>
              <button onClick={handleCopy} className="hover:text-white">
                {copied ? <Check className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
              </button>
            </div>
          )}
        </div>
      </div>
      
      {isUser && (
        <div className="h-8 w-8 rounded-full bg-gray-700 flex items-center justify-center shrink-0">
          <User className="h-4 w-4 text-supplement1" />
        </div>
      )}

      {/* Vault Suggestion Modal */}
      {showVaultModal && extractionData && (
        <VaultSuggestionModal
          isOpen={showVaultModal}
          onClose={() => {
            setShowVaultModal(false)
            setExtractionData(null)
          }}
          extractionData={extractionData}
          onVaultSelected={handleVaultAssignment}
          messageId={message.id}
        />
      )}
    </div>
  )
}

export default MessageBubble
