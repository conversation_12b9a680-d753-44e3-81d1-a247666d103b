/**
 * React hooks for unified API access
 * Provides convenient hooks for common API patterns with loading states and error handling
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { apiClient, APIError, APICallOptions } from '../api/UnifiedAPIClient'

export interface UseAPIState<T> {
  data: T | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

export interface UseAPIOptions extends APICallOptions {
  immediate?: boolean
  dependencies?: any[]
}

/**
 * Generic hook for API calls with loading states
 */
export function useAPI<T = any>(
  apiCall: () => Promise<T>,
  options: UseAPIOptions = {}
): UseAPIState<T> {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const mountedRef = useRef(true)

  const { immediate = true, dependencies = [] } = options

  const fetchData = useCallback(async () => {
    if (!mountedRef.current) return

    setLoading(true)
    setError(null)

    try {
      const result = await apiCall()
      if (mountedRef.current) {
        setData(result)
      }
    } catch (err: any) {
      if (mountedRef.current) {
        setError(err instanceof APIError ? err.message : 'An error occurred')
      }
    } finally {
      if (mountedRef.current) {
        setLoading(false)
      }
    }
  }, [apiCall, ...dependencies])

  useEffect(() => {
    if (immediate) {
      fetchData()
    }
  }, [fetchData, immediate])

  useEffect(() => {
    return () => {
      mountedRef.current = false
    }
  }, [])

  return {
    data,
    loading,
    error,
    refetch: fetchData
  }
}

/**
 * Hook for conversations
 */
export function useConversations() {
  return useAPI(() => apiClient.getConversations())
}

/**
 * Hook for a specific conversation
 */
export function useConversation(id: string | null) {
  return useAPI(
    () => id ? apiClient.getConversation(id) : Promise.resolve(null),
    { dependencies: [id], immediate: !!id }
  )
}

/**
 * Hook for messages in a conversation
 */
export function useMessages(conversationId: string | null) {
  return useAPI(
    () => conversationId ? apiClient.getMessages(conversationId) : Promise.resolve([]),
    { dependencies: [conversationId], immediate: !!conversationId }
  )
}

/**
 * Hook for files
 */
export function useFiles() {
  return useAPI(() => apiClient.getFiles())
}

/**
 * Hook for plugins
 */
export function usePlugins() {
  return useAPI(() => apiClient.getAllPlugins())
}

/**
 * Hook for plugin API endpoints
 */
export function usePluginAPIEndpoints(pluginId: string | null) {
  return useAPI(
    () => pluginId ? apiClient.getPluginAPIEndpoints(pluginId) : Promise.resolve(null),
    { dependencies: [pluginId], immediate: !!pluginId }
  )
}

/**
 * Hook for system monitoring data
 */
export function useMonitoringData() {
  return useAPI(() => apiClient.getMonitoringData())
}

/**
 * Hook for API mutations with loading states
 */
export function useAPIMutation<TArgs extends any[], TResult = any>(
  mutationFn: (...args: TArgs) => Promise<TResult>
) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [data, setData] = useState<TResult | null>(null)

  const mutate = useCallback(async (...args: TArgs): Promise<TResult> => {
    setLoading(true)
    setError(null)

    try {
      const result = await mutationFn(...args)
      setData(result)
      return result
    } catch (err: any) {
      const errorMessage = err instanceof APIError ? err.message : 'An error occurred'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [mutationFn])

  const reset = useCallback(() => {
    setData(null)
    setError(null)
    setLoading(false)
  }, [])

  return {
    mutate,
    loading,
    error,
    data,
    reset
  }
}

/**
 * Mutation hooks for common operations
 */
export function useCreateConversation() {
  return useAPIMutation((title: string) => apiClient.createConversation(title))
}

export function useUpdateConversation() {
  return useAPIMutation((id: string, title: string) => apiClient.updateConversation(id, title))
}

export function useDeleteConversation() {
  return useAPIMutation((id: string) => apiClient.deleteConversation(id))
}

export function useAddMessage() {
  return useAPIMutation((conversationId: string, message: any) => 
    apiClient.addMessage(conversationId, message)
  )
}

export function useAddFile() {
  return useAPIMutation((file: any) => apiClient.addFile(file))
}

export function useUpdateFile() {
  return useAPIMutation((id: string, updates: any) => apiClient.updateFile(id, updates))
}

export function useDeleteFile() {
  return useAPIMutation((id: string) => apiClient.deleteFile(id))
}

/**
 * Hook for vault operations
 */
export function useVaultOperation() {
  return useAPIMutation(async (operation: string, ...args: any[]) => {
    switch (operation) {
      case 'createDirectory':
        return apiClient.createDirectory(args[0])
      case 'writeFile':
        return apiClient.writeFile(args[0], args[1])
      case 'readFile':
        return apiClient.readFile(args[0])
      case 'readDirectory':
        return apiClient.readDirectory(args[0])
      case 'removeDirectory':
        return apiClient.removeDirectory(args[0])
      case 'removeFile':
        return apiClient.removeFile(args[0])
      case 'pathExists':
        return apiClient.pathExists(args[0])
      default:
        throw new Error(`Unknown vault operation: ${operation}`)
    }
  })
}

/**
 * Hook for plugin operations
 */
export function usePluginOperation() {
  return useAPIMutation(async (operation: string, ...args: any[]) => {
    switch (operation) {
      case 'enable':
        return apiClient.enablePlugin(args[0], args[1])
      case 'disable':
        return apiClient.disablePlugin(args[0])
      case 'getConfig':
        return apiClient.getPluginConfig(args[0])
      case 'updateConfig':
        return apiClient.updatePluginConfig(args[0], args[1])
      case 'callAPI':
        return apiClient.callPluginAPI(args[0], args[1], ...args.slice(2))
      default:
        throw new Error(`Unknown plugin operation: ${operation}`)
    }
  })
}

/**
 * Hook for batch API calls
 */
export function useBatchAPI() {
  return useAPIMutation((calls: Array<{
    category: string
    endpoint: string
    args?: any[]
    options?: APICallOptions
  }>) => apiClient.batchCall(calls))
}

/**
 * Hook for API health monitoring
 */
export function useAPIHealth(interval: number = 30000) {
  const [health, setHealth] = useState<{
    healthy: boolean
    latency: number
    errors: string[]
  } | null>(null)

  useEffect(() => {
    const checkHealth = async () => {
      try {
        const healthData = await apiClient.healthCheck()
        setHealth(healthData)
      } catch (error) {
        setHealth({
          healthy: false,
          latency: -1,
          errors: ['Health check failed']
        })
      }
    }

    checkHealth()
    const intervalId = setInterval(checkHealth, interval)

    return () => clearInterval(intervalId)
  }, [interval])

  return health
}

/**
 * Hook for real-time data with polling
 */
export function usePolling<T>(
  apiCall: () => Promise<T>,
  interval: number = 5000,
  options: UseAPIOptions = {}
) {
  const apiState = useAPI(apiCall, options)
  const intervalRef = useRef<NodeJS.Timeout | undefined>(undefined)

  useEffect(() => {
    if (interval > 0) {
      intervalRef.current = setInterval(() => {
        apiState.refetch()
      }, interval)

      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current)
        }
      }
    }
  }, [interval, apiState.refetch])

  return apiState
}
