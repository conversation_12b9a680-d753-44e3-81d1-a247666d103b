/**
 * Example component demonstrating the Unified API Client usage
 * Shows how to replace direct electronAPI calls with the new unified approach
 */

import React, { useState } from 'react'
import { 
  useConversations, 
  useMessages, 
  useCreateConversation, 
  useAddMessage,
  useAPIHealth,
  useMonitoringData
} from '../hooks/useAPI'
import { apiClient, db, vault, plugins, system } from '../api/UnifiedAPIClient'

export const APIClientExample: React.FC = () => {
  const [selectedConversationId, setSelectedConversationId] = useState<string | null>(null)
  const [newConversationTitle, setNewConversationTitle] = useState('')
  const [newMessageContent, setNewMessageContent] = useState('')

  // Using hooks for data fetching
  const { data: conversations, loading: conversationsLoading, error: conversationsError, refetch: refetchConversations } = useConversations()
  const { data: messages, loading: messagesLoading, error: messagesError, refetch: refetchMessages } = useMessages(selectedConversationId)
  const { data: monitoringData, loading: monitoringLoading } = useMonitoringData()
  const apiHealth = useAPIHealth(10000) // Check every 10 seconds

  // Using mutation hooks
  const createConversation = useCreateConversation()
  const addMessage = useAddMessage()

  const handleCreateConversation = async () => {
    if (!newConversationTitle.trim()) return

    try {
      const result = await createConversation.mutate(newConversationTitle)
      console.log('Created conversation:', result)
      setNewConversationTitle('')
      refetchConversations()
    } catch (error) {
      console.error('Failed to create conversation:', error)
    }
  }

  const handleAddMessage = async () => {
    if (!selectedConversationId || !newMessageContent.trim()) return

    try {
      const message = {
        content: newMessageContent,
        role: 'user',
        timestamp: new Date().toISOString()
      }
      
      await addMessage.mutate(selectedConversationId, message)
      setNewMessageContent('')
      refetchMessages()
    } catch (error) {
      console.error('Failed to add message:', error)
    }
  }

  // Example of direct API client usage
  const handleDirectAPICall = async () => {
    try {
      // Using the singleton instance
      const files = await apiClient.getFiles()
      console.log('Files:', files)

      // Using convenience methods
      const allConversations = await db.conversations.getAll()
      console.log('Conversations:', allConversations)

      // Vault operations
      const vaultRegistry = await vault.getRegistry()
      console.log('Vault registry:', vaultRegistry)

      // Plugin operations
      const allPlugins = await plugins.getAll()
      console.log('Plugins:', allPlugins)

      // System operations
      const systemHealth = await system.healthCheck()
      console.log('System health:', systemHealth)

      // Batch operations
      const batchResults = await apiClient.batchCall([
        { category: 'db', endpoint: 'getConversations' },
        { category: 'db', endpoint: 'getFiles' },
        { category: 'system', endpoint: 'getAPIRegistry' }
      ])
      console.log('Batch results:', batchResults)

    } catch (error) {
      console.error('Direct API call failed:', error)
    }
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Unified API Client Example</h1>

      {/* API Health Status */}
      <div className="mb-6 p-4 border rounded-lg">
        <h2 className="text-lg font-semibold mb-2">API Health Status</h2>
        {apiHealth ? (
          <div className={`p-2 rounded ${apiHealth.healthy ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
            <div>Status: {apiHealth.healthy ? 'Healthy' : 'Unhealthy'}</div>
            <div>Latency: {apiHealth.latency}ms</div>
            {apiHealth.errors.length > 0 && (
              <div>Errors: {apiHealth.errors.join(', ')}</div>
            )}
          </div>
        ) : (
          <div>Checking health...</div>
        )}
      </div>

      {/* Monitoring Data */}
      <div className="mb-6 p-4 border rounded-lg">
        <h2 className="text-lg font-semibold mb-2">API Monitoring</h2>
        {monitoringLoading ? (
          <div>Loading monitoring data...</div>
        ) : monitoringData ? (
          <div className="text-sm">
            <div>Total Calls: {monitoringData.data?.totalCalls || 0}</div>
            <div>Success Rate: {monitoringData.data?.successRate || 0}%</div>
            <div>Average Response Time: {monitoringData.data?.averageResponseTime || 0}ms</div>
          </div>
        ) : (
          <div>No monitoring data available</div>
        )}
      </div>

      {/* Create Conversation */}
      <div className="mb-6 p-4 border rounded-lg">
        <h2 className="text-lg font-semibold mb-2">Create Conversation</h2>
        <div className="flex gap-2">
          <input
            type="text"
            value={newConversationTitle}
            onChange={(e) => setNewConversationTitle(e.target.value)}
            placeholder="Conversation title"
            className="flex-1 px-3 py-2 border rounded"
          />
          <button
            onClick={handleCreateConversation}
            disabled={createConversation.loading}
            className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
          >
            {createConversation.loading ? 'Creating...' : 'Create'}
          </button>
        </div>
        {createConversation.error && (
          <div className="mt-2 text-red-600">{createConversation.error}</div>
        )}
      </div>

      {/* Conversations List */}
      <div className="mb-6 p-4 border rounded-lg">
        <h2 className="text-lg font-semibold mb-2">Conversations</h2>
        {conversationsLoading ? (
          <div>Loading conversations...</div>
        ) : conversationsError ? (
          <div className="text-red-600">Error: {conversationsError}</div>
        ) : conversations ? (
          <div className="space-y-2">
            {conversations.map((conv: any) => (
              <div
                key={conv.id}
                onClick={() => setSelectedConversationId(conv.id)}
                className={`p-2 border rounded cursor-pointer ${
                  selectedConversationId === conv.id ? 'bg-blue-100' : 'hover:bg-gray-100'
                }`}
              >
                {conv.title}
              </div>
            ))}
          </div>
        ) : (
          <div>No conversations found</div>
        )}
      </div>

      {/* Messages */}
      {selectedConversationId && (
        <div className="mb-6 p-4 border rounded-lg">
          <h2 className="text-lg font-semibold mb-2">Messages</h2>
          {messagesLoading ? (
            <div>Loading messages...</div>
          ) : messagesError ? (
            <div className="text-red-600">Error: {messagesError}</div>
          ) : messages ? (
            <div className="space-y-2 mb-4">
              {messages.map((msg: any, index: number) => (
                <div key={index} className="p-2 border rounded">
                  <div className="font-semibold">{msg.role}</div>
                  <div>{msg.content}</div>
                </div>
              ))}
            </div>
          ) : null}

          {/* Add Message */}
          <div className="flex gap-2">
            <input
              type="text"
              value={newMessageContent}
              onChange={(e) => setNewMessageContent(e.target.value)}
              placeholder="Message content"
              className="flex-1 px-3 py-2 border rounded"
            />
            <button
              onClick={handleAddMessage}
              disabled={addMessage.loading}
              className="px-4 py-2 bg-green-500 text-white rounded disabled:opacity-50"
            >
              {addMessage.loading ? 'Adding...' : 'Add Message'}
            </button>
          </div>
          {addMessage.error && (
            <div className="mt-2 text-red-600">{addMessage.error}</div>
          )}
        </div>
      )}

      {/* Direct API Call Example */}
      <div className="mb-6 p-4 border rounded-lg">
        <h2 className="text-lg font-semibold mb-2">Direct API Calls</h2>
        <button
          onClick={handleDirectAPICall}
          className="px-4 py-2 bg-purple-500 text-white rounded"
        >
          Test Direct API Calls
        </button>
        <div className="mt-2 text-sm text-gray-600">
          Check the browser console for results
        </div>
      </div>

      {/* Migration Guide */}
      <div className="p-4 border rounded-lg bg-gray-50">
        <h2 className="text-lg font-semibold mb-2">Migration Guide</h2>
        <div className="text-sm space-y-2">
          <div>
            <strong>Before:</strong> <code>window.electronAPI.invoke('db:getConversations')</code>
          </div>
          <div>
            <strong>After:</strong> <code>apiClient.getConversations()</code> or <code>db.conversations.getAll()</code>
          </div>
          <div>
            <strong>With hooks:</strong> <code>const {`{data, loading, error}`} = useConversations()</code>
          </div>
        </div>
      </div>
    </div>
  )
}

export default APIClientExample
