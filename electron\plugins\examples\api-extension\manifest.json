{"id": "api-extension-example", "name": "API Extension Example", "version": "1.0.0", "description": "Example plugin demonstrating API extension capabilities", "author": "ChatLo Team", "main": "./ApiExtensionPlugin.ts", "capabilities": ["api_extension"], "dependencies": {}, "config": {"enabled": true, "maxRequests": 100, "rateLimitWindow": 60000}, "api": {"namespace": "plugin_api-extension-example", "endpoints": [{"name": "greet", "description": "Returns a greeting message", "parameters": [{"name": "name", "type": "string", "required": false, "description": "Name to greet"}]}, {"name": "processData", "description": "Processes an array of data items", "parameters": [{"name": "data", "type": "array", "required": true, "description": "Array of data items to process"}]}, {"name": "getStatus", "description": "Returns plugin status and configuration", "parameters": []}]}}