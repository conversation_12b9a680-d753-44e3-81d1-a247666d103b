import React, { useState, useRef, useCallback } from 'react'
import { Artifact } from '../../../types'

interface ImageArtifactViewerProps {
  artifact: Artifact
}

export function ImageArtifactViewer({ artifact }: ImageArtifactViewerProps) {
  const [zoom, setZoom] = useState(1)
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)
  const imageRef = useRef<HTMLImageElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  const handleImageLoad = () => {
    setImageLoaded(true)
    setImageError(false)
  }

  const handleImageError = () => {
    setImageError(true)
    setImageLoaded(false)
  }

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev * 1.5, 5))
  }

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev / 1.5, 0.1))
  }

  const handleResetZoom = () => {
    setZoom(1)
    setPosition({ x: 0, y: 0 })
  }

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (zoom > 1) {
      setIsDragging(true)
      setDragStart({
        x: e.clientX - position.x,
        y: e.clientY - position.y
      })
    }
  }, [zoom, position])

  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (isDragging && zoom > 1) {
      setPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      })
    }
  }, [isDragging, dragStart, zoom])

  const handleMouseUp = useCallback(() => {
    setIsDragging(false)
  }, [])

  const handleWheel = useCallback((e: React.WheelEvent) => {
    e.preventDefault()
    const delta = e.deltaY > 0 ? 0.9 : 1.1
    setZoom(prev => Math.max(0.1, Math.min(5, prev * delta)))
  }, [])

  const handleDownload = () => {
    const link = document.createElement('a')
    link.href = artifact.content
    link.download = `${artifact.title.replace(/[^a-zA-Z0-9]/g, '_')}.png`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const handleCopyImage = async () => {
    try {
      // For base64 images, we need to convert to blob first
      if (artifact.content.startsWith('data:')) {
        const response = await fetch(artifact.content)
        const blob = await response.blob()
        await navigator.clipboard.write([
          new ClipboardItem({ [blob.type]: blob })
        ])
      } else {
        // For URLs, copy the URL
        await navigator.clipboard.writeText(artifact.content)
      }
      console.log('Image copied to clipboard')
    } catch (error) {
      console.error('Failed to copy image:', error)
    }
  }

  const handleEdit = () => {
    // TODO: Integrate with existing image editing features
    console.log('Edit image:', artifact.content)
  }

  return (
    <div className="h-full flex flex-col bg-neutral-900">
      {/* Minimal zoom controls */}
      <div className="flex-shrink-0 flex items-center justify-between p-2 bg-neutral-800/30 border-b border-neutral-700/50">
        <div className="flex items-center space-x-3">
          <span className="text-xs text-neutral-400">
            {Math.round(zoom * 100)}%
          </span>
          {imageLoaded && imageRef.current && (
            <span className="text-xs text-neutral-500">
              {imageRef.current.naturalWidth} × {imageRef.current.naturalHeight}
            </span>
          )}
        </div>

        <div className="flex items-center space-x-1">
          {/* Zoom controls */}
          <button
            onClick={handleZoomOut}
            className="p-1 text-neutral-400 hover:text-white hover:bg-neutral-700 rounded transition-colors"
            title="Zoom out"
          >
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM13 10H7" />
            </svg>
          </button>

          <button
            onClick={handleZoomIn}
            className="p-1 text-neutral-400 hover:text-white hover:bg-neutral-700 rounded transition-colors"
            title="Zoom in"
          >
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
            </svg>
          </button>

          <button
            onClick={handleResetZoom}
            className="px-2 py-1 text-xs bg-neutral-700 hover:bg-neutral-600 text-white rounded transition-colors"
            title="Reset zoom"
          >
            Reset
          </button>

          {/* Action buttons */}
          <div className="w-px h-4 bg-neutral-600 mx-2"></div>
          <button
            onClick={handleCopyImage}
            className="px-2 py-1 text-xs bg-neutral-700 hover:bg-neutral-600 text-white rounded transition-colors"
            title="Copy image"
          >
            Copy
          </button>
          <button
            onClick={handleDownload}
            className="px-2 py-1 text-xs bg-neutral-700 hover:bg-neutral-600 text-white rounded transition-colors"
            title="Download image"
          >
            Download
          </button>
          <button
            onClick={handleEdit}
            className="px-2 py-1 text-xs bg-purple-600 hover:bg-purple-500 text-white rounded transition-colors"
            title="Edit image"
          >
            Edit
          </button>
        </div>
      </div>

      {/* Image container */}
      <div 
        ref={containerRef}
        className="flex-1 overflow-hidden relative bg-gray-800 cursor-move"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onWheel={handleWheel}
      >
        {imageError ? (
          <div className="flex items-center justify-center h-full text-gray-400">
            <div className="text-center">
              <div className="text-4xl mb-4">🖼️</div>
              <div className="text-lg font-medium mb-2">Failed to Load Image</div>
              <div className="text-sm">
                The image could not be displayed
              </div>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center h-full">
            <img
              ref={imageRef}
              src={artifact.content}
              alt={artifact.title}
              className="max-w-none transition-transform duration-200"
              style={{
                transform: `scale(${zoom}) translate(${position.x / zoom}px, ${position.y / zoom}px)`,
                cursor: zoom > 1 ? (isDragging ? 'grabbing' : 'grab') : 'default'
              }}
              onLoad={handleImageLoad}
              onError={handleImageError}
              draggable={false}
            />
          </div>
        )}

        {/* Loading indicator */}
        {!imageLoaded && !imageError && (
          <div className="absolute inset-0 flex items-center justify-center bg-neutral-800">
            <div className="text-center text-neutral-400">
              <div className="animate-spin w-8 h-8 border-2 border-indigo-500 border-t-transparent rounded-full mx-auto mb-2"></div>
              <div>Loading image...</div>
            </div>
          </div>
        )}
      </div>

      {/* Status bar */}
      <div className="flex-shrink-0 flex items-center justify-between px-4 py-2 bg-neutral-800 border-t border-neutral-700 text-xs text-neutral-400">
        <div>
          {imageLoaded && imageRef.current && (
            <>
              {imageRef.current.naturalWidth} × {imageRef.current.naturalHeight} pixels
            </>
          )}
        </div>
        <div>
          {artifact.metadata.mimeType || 'Image'}
        </div>
      </div>
    </div>
  )
}
