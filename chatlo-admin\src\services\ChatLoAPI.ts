/**
 * ChatLo Admin API Service
 * Central API gateway for admin operations and plugin management
 */

import { 
  ProcessingInput, 
  ProcessingOutput, 
  TestSuite, 
  ValidationResult,
  ModelInfo,
  SystemPrompts,
  PipelineConfig,
  DeploymentResult,
  ApiResponse,
  SystemHealth,
  AdminOperation
} from '../types/api'

export class ChatLoAPI {
  private baseUrl: string
  private apiKey?: string

  constructor(baseUrl: string = 'http://localhost:3000', apiKey?: string) {
    this.baseUrl = baseUrl
    this.apiKey = apiKey
  }

  // Intelligence Operations
  async extractIntelligence(
    content: string, 
    modelId: string,
    config?: Partial<ProcessingInput>
  ): Promise<ProcessingOutput> {
    const input: ProcessingInput = {
      content,
      contentType: 'text',
      language: 'auto',
      ...config
    }

    const response = await this.post<ProcessingOutput>('/api/v1/intelligence/extract', {
      input,
      modelId,
      timestamp: new Date().toISOString()
    })

    return response.data!
  }

  async testExtraction(testSuite: TestSuite): Promise<ValidationResult[]> {
    const response = await this.post<ValidationResult[]>('/api/v1/intelligence/test', {
      testSuite,
      timestamp: new Date().toISOString()
    })

    return response.data!
  }

  async validateLogic(testCaseId: string, expectedOutput: any): Promise<ValidationResult> {
    const response = await this.post<ValidationResult>('/api/v1/intelligence/validate', {
      testCaseId,
      expectedOutput,
      timestamp: new Date().toISOString()
    })

    return response.data!
  }

  // Model Operations
  async listModels(): Promise<ModelInfo[]> {
    const response = await this.get<ModelInfo[]>('/api/v1/models/list')
    return response.data!
  }

  async getModelPerformance(modelId: string): Promise<any> {
    const response = await this.get(`/api/v1/models/${modelId}/performance`)
    return response.data!
  }

  async deployModel(modelConfig: any): Promise<DeploymentResult> {
    const response = await this.post<DeploymentResult>('/api/v1/models/deploy', {
      modelConfig,
      timestamp: new Date().toISOString()
    })

    return response.data!
  }

  // Admin Operations
  async updateSystemPrompts(prompts: SystemPrompts): Promise<any> {
    const response = await this.put('/api/v1/admin/prompts', {
      prompts,
      timestamp: new Date().toISOString()
    })

    return response.data!
  }

  async configurePipeline(pipeline: PipelineConfig): Promise<any> {
    const response = await this.put('/api/v1/admin/pipeline', {
      pipeline,
      timestamp: new Date().toISOString()
    })

    return response.data!
  }

  async getSystemHealth(): Promise<SystemHealth> {
    const response = await this.get<SystemHealth>('/api/v1/admin/health')
    return response.data!
  }

  async getOperations(): Promise<AdminOperation[]> {
    const response = await this.get<AdminOperation[]>('/api/v1/admin/operations')
    return response.data!
  }

  async createOperation(operation: Partial<AdminOperation>): Promise<AdminOperation> {
    const response = await this.post<AdminOperation>('/api/v1/admin/operations', {
      ...operation,
      timestamp: new Date().toISOString()
    })

    return response.data!
  }

  // Direct LLM Integration (for testing)
  async callOllamaModel(modelName: string, prompt: string): Promise<string> {
    try {
      const response = await fetch('http://localhost:11434/api/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json; charset=utf-8'
        },
        body: JSON.stringify({
          model: modelName,
          prompt: prompt,
          stream: false
        })
      })

      if (!response.ok) {
        throw new Error(`Ollama API error: ${response.status}`)
      }

      const data = await response.json()
      return data.response || ''
    } catch (error) {
      console.error('Ollama call failed:', error)
      throw error
    }
  }

  async callLMStudioModel(modelName: string, prompt: string): Promise<string> {
    try {
      const response = await fetch('http://localhost:1234/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json; charset=utf-8'
        },
        body: JSON.stringify({
          model: modelName,
          messages: [{ role: 'user', content: prompt }],
          temperature: 0.3,
          max_tokens: 2000
        })
      })

      if (!response.ok) {
        throw new Error(`LM Studio API error: ${response.status}`)
      }

      const data = await response.json()
      return data.choices?.[0]?.message?.content || ''
    } catch (error) {
      console.error('LM Studio call failed:', error)
      throw error
    }
  }

  // HTTP Helper Methods
  private async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>('GET', endpoint)
  }

  private async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>('POST', endpoint, data)
  }

  private async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>('PUT', endpoint, data)
  }

  private async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>('DELETE', endpoint)
  }

  private async request<T>(
    method: string, 
    endpoint: string, 
    data?: any
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`
    const headers: Record<string, string> = {
      'Content-Type': 'application/json; charset=utf-8'
    }

    if (this.apiKey) {
      headers['Authorization'] = `Bearer ${this.apiKey}`
    }

    const config: RequestInit = {
      method,
      headers,
      body: data ? JSON.stringify(data) : undefined
    }

    try {
      const response = await fetch(url, config)
      const responseData = await response.json()

      if (!response.ok) {
        throw new Error(responseData.error || `HTTP ${response.status}`)
      }

      return {
        success: true,
        data: responseData,
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    } catch (error) {
      console.error(`API request failed: ${method} ${endpoint}`, error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
        requestId: this.generateRequestId()
      }
    }
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}

// Singleton instance
export const chatLoAPI = new ChatLoAPI()
