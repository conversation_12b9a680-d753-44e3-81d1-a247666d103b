/**
 * Unified API Client
 * Provides a clean, typed interface for all backend API calls
 * Replaces direct electronAPI usage with a structured approach
 */

declare global {
  interface Window {
    electronAPI: any
  }
}

export interface APIResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  timestamp?: string
}

export interface APICallOptions {
  timeout?: number
  retries?: number
  retryDelay?: number
  validateResponse?: boolean
}

export class APIError extends Error {
  constructor(
    message: string,
    public code?: string,
    public category?: string,
    public endpoint?: string
  ) {
    super(message)
    this.name = 'APIError'
  }
}

export class UnifiedAPIClient {
  private static instance: UnifiedAPIClient
  private defaultTimeout = 30000 // 30 seconds
  private defaultRetries = 3
  private defaultRetryDelay = 1000 // 1 second

  private constructor() {}

  static getInstance(): UnifiedAPIClient {
    if (!UnifiedAPIClient.instance) {
      UnifiedAPIClient.instance = new UnifiedAPIClient()
    }
    return UnifiedAPIClient.instance
  }

  /**
   * Generic API call method with error handling and retries
   */
  private async call<T = any>(
    category: string,
    endpoint: string,
    args: any[] = [],
    options: APICallOptions = {}
  ): Promise<T> {
    const {
      timeout = this.defaultTimeout,
      retries = this.defaultRetries,
      retryDelay = this.defaultRetryDelay,
      validateResponse = true
    } = options

    const channelName = `${category}:${endpoint}`
    let lastError: Error

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        // Create timeout promise
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => reject(new APIError(`API call timeout: ${channelName}`, 'TIMEOUT', category, endpoint)), timeout)
        })

        // Make the API call
        const apiPromise = window.electronAPI.invoke(channelName, ...args)
        const result = await Promise.race([apiPromise, timeoutPromise])

        // Validate response if requested
        if (validateResponse && result && typeof result === 'object') {
          if (result.success === false) {
            throw new APIError(result.error || 'API call failed', result.code, category, endpoint)
          }
        }

        return result
      } catch (error: any) {
        lastError = error
        
        // Don't retry on certain errors
        if (error.code === 'TIMEOUT' || error.code === 'VALIDATION_ERROR' || attempt === retries) {
          break
        }

        // Wait before retry
        if (attempt < retries) {
          await new Promise(resolve => setTimeout(resolve, retryDelay * (attempt + 1)))
        }
      }
    }

    throw lastError!
  }

  // Database APIs
  async getConversations(): Promise<any[]> {
    return this.call('db', 'getConversations')
  }

  async getConversation(id: string): Promise<any> {
    return this.call('db', 'getConversation', [id])
  }

  async createConversation(title: string): Promise<any> {
    return this.call('db', 'createConversation', [title])
  }

  async updateConversation(id: string, title: string): Promise<any> {
    return this.call('db', 'updateConversation', [id, title])
  }

  async deleteConversation(id: string): Promise<any> {
    return this.call('db', 'deleteConversation', [id])
  }

  async getMessages(conversationId: string): Promise<any[]> {
    return this.call('db', 'getMessages', [conversationId])
  }

  async addMessage(conversationId: string, message: any): Promise<any> {
    return this.call('db', 'addMessage', [conversationId, message])
  }

  async getFiles(): Promise<any[]> {
    return this.call('db', 'getFiles')
  }

  async addFile(file: any): Promise<any> {
    return this.call('db', 'addFile', [file])
  }

  async updateFile(id: string, updates: any): Promise<any> {
    return this.call('db', 'updateFile', [id, updates])
  }

  async deleteFile(id: string): Promise<any> {
    return this.call('db', 'deleteFile', [id])
  }

  // Vault APIs
  async createDirectory(dirPath: string): Promise<APIResponse> {
    return this.call('vault', 'createDirectory', [dirPath])
  }

  async writeFile(filePath: string, content: string): Promise<APIResponse> {
    return this.call('vault', 'writeFile', [filePath, content])
  }

  async readFile(filePath: string): Promise<APIResponse<string>> {
    return this.call('vault', 'readFile', [filePath])
  }

  async readDirectory(dirPath: string): Promise<APIResponse<any[]>> {
    return this.call('vault', 'readDirectory', [dirPath])
  }

  async removeDirectory(dirPath: string): Promise<APIResponse> {
    return this.call('vault', 'removeDirectory', [dirPath])
  }

  async removeFile(filePath: string): Promise<APIResponse> {
    return this.call('vault', 'removeFile', [filePath])
  }

  async pathExists(targetPath: string): Promise<APIResponse<boolean>> {
    return this.call('vault', 'pathExists', [targetPath])
  }

  async getVaultRegistry(): Promise<any> {
    return this.call('vault', 'getVaultRegistry')
  }

  async saveVaultRegistry(registry: any): Promise<APIResponse> {
    return this.call('vault', 'saveVaultRegistry', [registry])
  }

  // Plugin APIs
  async getAllPlugins(): Promise<any[]> {
    return this.call('plugins', 'getAll')
  }

  async enablePlugin(pluginId: string, enabled: boolean): Promise<void> {
    return this.call('plugins', 'enable', [pluginId, enabled])
  }

  async disablePlugin(pluginId: string): Promise<void> {
    return this.call('plugins', 'disable', [pluginId])
  }

  async getPluginConfig(pluginId: string): Promise<any> {
    return this.call('plugins', 'getConfig', [pluginId])
  }

  async updatePluginConfig(pluginId: string, config: any): Promise<void> {
    return this.call('plugins', 'updateConfig', [pluginId, config])
  }

  async getPluginAPIEndpoints(pluginId: string): Promise<APIResponse<any>> {
    return this.call('plugins', 'getAPIEndpoints', [pluginId])
  }

  async getAllPluginAPIEndpoints(): Promise<APIResponse<any[]>> {
    return this.call('plugins', 'getAllAPIEndpoints')
  }

  // System APIs
  async getAPIRegistry(): Promise<APIResponse<any>> {
    return this.call('system', 'getAPIRegistry')
  }

  async getPerformanceMetrics(): Promise<APIResponse<any>> {
    return this.call('system', 'getPerformanceMetrics')
  }

  async getMonitoringData(): Promise<APIResponse<any>> {
    return this.call('system', 'getMonitoringData')
  }

  async getEndpointMetrics(category: string, endpoint: string): Promise<APIResponse<any>> {
    return this.call('system', 'getEndpointMetrics', [category, endpoint])
  }

  async resetMonitoring(): Promise<APIResponse> {
    return this.call('system', 'resetMonitoring')
  }

  async cleanupMiddleware(): Promise<APIResponse> {
    return this.call('system', 'cleanupMiddleware')
  }

  // Updater APIs
  async checkForUpdates(): Promise<APIResponse> {
    return this.call('updater', 'checkForUpdates')
  }

  async downloadUpdate(): Promise<APIResponse> {
    return this.call('updater', 'downloadUpdate')
  }

  async installUpdate(): Promise<APIResponse> {
    return this.call('updater', 'installUpdate')
  }

  // Generic plugin API call
  async callPluginAPI(pluginId: string, endpoint: string, ...args: any[]): Promise<any> {
    const channelName = `plugin_${pluginId}:${endpoint}`
    return window.electronAPI.invoke(channelName, ...args)
  }

  // Batch API calls
  async batchCall<T = any>(calls: Array<{
    category: string
    endpoint: string
    args?: any[]
    options?: APICallOptions
  }>): Promise<Array<{ success: boolean; data?: T; error?: string }>> {
    const promises = calls.map(async call => {
      try {
        const data = await this.call<T>(call.category, call.endpoint, call.args, call.options)
        return { success: true, data }
      } catch (error: any) {
        return { success: false, error: error.message }
      }
    })

    return Promise.all(promises)
  }

  // Health check
  async healthCheck(): Promise<{ healthy: boolean; latency: number; errors: string[] }> {
    const startTime = Date.now()
    const errors: string[] = []
    let healthy = true

    try {
      // Test basic API connectivity
      await this.call('system', 'getAPIRegistry', [], { timeout: 5000, retries: 1 })
    } catch (error: any) {
      healthy = false
      errors.push(`API connectivity failed: ${error.message}`)
    }

    const latency = Date.now() - startTime

    return { healthy, latency, errors }
  }

  // Configuration
  setDefaultTimeout(timeout: number): void {
    this.defaultTimeout = timeout
  }

  setDefaultRetries(retries: number): void {
    this.defaultRetries = retries
  }

  setDefaultRetryDelay(delay: number): void {
    this.defaultRetryDelay = delay
  }
}

// Export singleton instance
export const apiClient = UnifiedAPIClient.getInstance()

// Export convenience methods for common patterns
export const db = {
  conversations: {
    getAll: () => apiClient.getConversations(),
    get: (id: string) => apiClient.getConversation(id),
    create: (title: string) => apiClient.createConversation(title),
    update: (id: string, title: string) => apiClient.updateConversation(id, title),
    delete: (id: string) => apiClient.deleteConversation(id)
  },
  messages: {
    get: (conversationId: string) => apiClient.getMessages(conversationId),
    add: (conversationId: string, message: any) => apiClient.addMessage(conversationId, message)
  },
  files: {
    getAll: () => apiClient.getFiles(),
    add: (file: any) => apiClient.addFile(file),
    update: (id: string, updates: any) => apiClient.updateFile(id, updates),
    delete: (id: string) => apiClient.deleteFile(id)
  }
}

export const vault = {
  createDirectory: (path: string) => apiClient.createDirectory(path),
  writeFile: (path: string, content: string) => apiClient.writeFile(path, content),
  readFile: (path: string) => apiClient.readFile(path),
  readDirectory: (path: string) => apiClient.readDirectory(path),
  removeDirectory: (path: string) => apiClient.removeDirectory(path),
  removeFile: (path: string) => apiClient.removeFile(path),
  pathExists: (path: string) => apiClient.pathExists(path),
  getRegistry: () => apiClient.getVaultRegistry(),
  saveRegistry: (registry: any) => apiClient.saveVaultRegistry(registry)
}

export const plugins = {
  getAll: () => apiClient.getAllPlugins(),
  enable: (id: string, enabled: boolean) => apiClient.enablePlugin(id, enabled),
  disable: (id: string) => apiClient.disablePlugin(id),
  getConfig: (id: string) => apiClient.getPluginConfig(id),
  updateConfig: (id: string, config: any) => apiClient.updatePluginConfig(id, config),
  getAPIEndpoints: (id: string) => apiClient.getPluginAPIEndpoints(id),
  getAllAPIEndpoints: () => apiClient.getAllPluginAPIEndpoints(),
  callAPI: (id: string, endpoint: string, ...args: any[]) => apiClient.callPluginAPI(id, endpoint, ...args)
}

export const system = {
  getAPIRegistry: () => apiClient.getAPIRegistry(),
  getPerformanceMetrics: () => apiClient.getPerformanceMetrics(),
  getMonitoringData: () => apiClient.getMonitoringData(),
  getEndpointMetrics: (category: string, endpoint: string) => apiClient.getEndpointMetrics(category, endpoint),
  resetMonitoring: () => apiClient.resetMonitoring(),
  cleanupMiddleware: () => apiClient.cleanupMiddleware(),
  healthCheck: () => apiClient.healthCheck()
}
