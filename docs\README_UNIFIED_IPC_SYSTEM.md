# ChatLo Unified IPC System - Complete Documentation

## 📋 Overview

The ChatLo Unified IPC System is a comprehensive inter-process communication framework built on Electron's IPC mechanism. It provides a centralized, secure, and extensible API system that enables seamless communication between the main process and renderer processes, with full support for plugin extensions.

## 🎯 Key Features

- **🔄 Unified API Registry**: Single point of API registration and management
- **🔌 Plugin System**: Extensible architecture supporting custom API endpoints
- **🛡️ Security Layer**: Built-in authentication, authorization, and input validation
- **📊 Monitoring**: Real-time performance tracking and error monitoring
- **⚡ Performance**: Optimized for high-throughput operations with middleware support
- **🔧 Type Safety**: Full TypeScript support throughout the system
- **🎣 React Integration**: Comprehensive hooks and client library for frontend

## 📚 Documentation Structure

This documentation is organized into several comprehensive guides:

### 📖 Core Documentation

1. **[System Design & Architecture](./SYSTEM_DESIGN_ARCHITECTURE.md)**
   - Complete system architecture with detailed diagrams
   - Application design system and component hierarchy
   - Core module structure and data flow diagrams
   - Middleware architecture and plugin system design
   - Security architecture and performance considerations

2. **[API Reference](./API_REFERENCE.md)**
   - Complete API endpoint documentation
   - Frontend API client reference
   - React hooks documentation
   - Error handling and middleware system
   - Monitoring, metrics, and security features

3. **[Internal Developer Documentation](./internal/UNIFIED_IPC_SYSTEM_INTERNAL.md)**
   - Detailed implementation guide for core developers
   - Architecture patterns and development guidelines
   - Code examples and best practices
   - Troubleshooting and maintenance procedures

### 🔌 Plugin Development

4. **[Plugin API Development Guide](./external/CHATLO_PLUGIN_API_GUIDE.md)**
   - Complete guide for plugin developers
   - API extension system documentation
   - Frontend integration examples
   - Security, validation, and testing guidelines
   - Best practices and design patterns

## 🚀 Quick Start

### For Application Developers

```typescript
// Import the unified API client
import { apiClient } from '../api/UnifiedAPIClient'

// Use the API client directly
const conversations = await apiClient.getConversations()
const newConversation = await apiClient.createConversation('My Chat')

// Or use React hooks
import { useConversations, useCreateConversation } from '../hooks/useAPI'

function MyComponent() {
  const { data: conversations, loading, error } = useConversations()
  const createConversation = useCreateConversation()
  
  const handleCreate = async () => {
    await createConversation.mutate('New Chat')
  }
  
  if (loading) return <div>Loading...</div>
  if (error) return <div>Error: {error}</div>
  
  return (
    <div>
      {conversations?.map(conv => (
        <div key={conv.id}>{conv.title}</div>
      ))}
      <button onClick={handleCreate}>Create New</button>
    </div>
  )
}
```

### For Plugin Developers

```typescript
import { Plugin, PluginCapability } from '../types'
import { APIExtension, PluginAPINamespace } from '../extensionPoints'

export class MyPlugin implements Plugin, APIExtension {
  id = 'myPlugin'
  name = 'My Awesome Plugin'
  capabilities = [PluginCapability.API_EXTENSION]

  getNamespace(): string {
    return `plugin_${this.id}`
  }

  registerEndpoints(apiRegistry: APIRegistry): PluginAPINamespace {
    const namespace: PluginAPINamespace = {
      namespace: this.getNamespace(),
      endpoints: new Map(),
      middleware: []
    }

    namespace.endpoints.set('processData', {
      handler: this.processData.bind(this),
      description: 'Process custom data',
      validationSchema: {
        data: { type: 'object' },
        options: { type: 'object', allowEmpty: true }
      }
    })

    return namespace
  }

  private async processData(data: any, options: any = {}): Promise<any> {
    // Your custom logic here
    return {
      success: true,
      processedData: data,
      options: options
    }
  }
}
```

## 🏗️ System Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Frontend (Renderer Process)"
        A[React Components] --> B[API Hooks]
        B --> C[UnifiedAPIClient]
        C --> D[Electron IPC]
    end
    
    subgraph "Backend (Main Process)"
        D --> E[APIRegistry]
        E --> F[Middleware Stack]
        F --> G[Core Services]
        F --> H[Plugin System]
        
        G --> I[Database Manager]
        G --> J[File System Manager]
        G --> K[Vault Manager]
        
        H --> L[Plugin Loader]
        L --> M[API Extension Plugins]
    end
    
    subgraph "Security & Monitoring"
        F --> N[Security Manager]
        F --> O[API Monitor]
        F --> P[Error Handler]
    end
```

### Core Components

- **APIRegistry**: Central hub for all API endpoint registration and routing
- **Middleware Stack**: Comprehensive middleware system for security, validation, and monitoring
- **Plugin System**: Extensible architecture for custom API endpoints
- **UnifiedAPIClient**: Frontend client with TypeScript support and error handling
- **Security Layer**: Authentication, authorization, and input validation
- **Monitoring System**: Real-time performance tracking and error monitoring

## 🔧 Core API Categories

### Database APIs (`db`)
- Conversation management (CRUD operations)
- Message handling
- File metadata management
- Intelligence and artifact storage

### Vault APIs (`vault`)
- File system operations
- Directory management
- Content reading/writing
- Registry management

### Plugin APIs (`plugins`)
- Plugin lifecycle management
- Configuration handling
- API endpoint discovery
- Dynamic plugin loading

### System APIs (`system`)
- Health monitoring
- Performance metrics
- Error statistics
- API registry inspection

## 🔌 Plugin System

### Plugin Capabilities
- **API_EXTENSION**: Plugins can register custom API endpoints
- **Namespace Isolation**: Each plugin gets its own API namespace (`plugin_${pluginId}`)
- **Middleware Support**: Plugins can provide custom middleware
- **Dynamic Discovery**: Runtime endpoint discovery and registration

### Plugin API Pattern
```
Plugin ID: dataProcessor
Namespace: plugin_dataProcessor
Endpoint: processData
Full Channel: plugin_dataProcessor:processData
```

## 🛡️ Security Features

### Authentication & Authorization
- User context validation
- Permission-based access control
- Session management
- Role-based permissions

### Input Validation
- Schema-based validation
- Type checking and sanitization
- Pattern matching
- Range validation

### Rate Limiting
- Per-endpoint rate limits
- User-based throttling
- Burst protection
- Configurable windows

## 📊 Monitoring & Observability

### Performance Metrics
- Response times (average, P95, P99)
- Request throughput
- Success/error rates
- System health indicators

### Error Tracking
- Structured error logging
- Error code classification
- Error frequency analysis
- Real-time error monitoring

### Health Monitoring
- API endpoint health checks
- System resource monitoring
- Plugin status tracking
- Alert generation

## 🎣 Frontend Integration

### React Hooks
- `useAPI`: Generic API hook with loading states
- `useAPIMutation`: Mutation hook for write operations
- `useConversations`: Specialized conversation hook
- `usePluginAPI`: Plugin-specific API hook
- `useAPIHealth`: Real-time health monitoring

### API Client Features
- Automatic retry logic
- Request/response interceptors
- Batch operation support
- TypeScript type safety
- Error handling and recovery

## 📈 Performance Optimizations

### Caching Strategy
- Response caching for expensive operations
- Intelligent cache invalidation
- Memory-efficient storage
- Configurable TTL

### Batch Operations
- Multiple API calls in single request
- Reduced IPC overhead
- Transaction-like semantics
- Error isolation

### Connection Management
- Connection pooling for database operations
- Efficient resource utilization
- Automatic cleanup
- Health monitoring

## 🧪 Testing & Quality Assurance

### Testing Strategy
- Unit tests for all API endpoints
- Integration tests for plugin system
- Performance benchmarking
- Security vulnerability testing

### Code Quality
- TypeScript strict mode
- ESLint configuration
- Automated code formatting
- Comprehensive error handling

## 📋 Migration Guide

### From Direct IPC to Unified System

**Before:**
```typescript
// Direct IPC calls
const result = await window.electronAPI.invoke('db-get-conversations')
```

**After:**
```typescript
// Unified API client
const conversations = await apiClient.getConversations()

// Or with hooks
const { data: conversations } = useConversations()
```

### Plugin Migration
Existing plugins can be migrated to support API extensions by implementing the `APIExtension` interface and registering custom endpoints.

## 🤝 Contributing

### Development Setup
1. Clone the repository
2. Install dependencies: `npm install`
3. Run tests: `npm test`
4. Start development server: `npm run dev`

### Code Standards
- Follow TypeScript best practices
- Write comprehensive tests
- Document all public APIs
- Use structured error handling

## 📞 Support & Resources

### Getting Help
- Check the comprehensive documentation
- Review code examples and patterns
- Use the built-in monitoring tools
- Consult the API reference

### Best Practices
- Always validate input parameters
- Implement proper error handling
- Use appropriate rate limiting
- Monitor performance metrics
- Follow security guidelines

## 🔄 Version History

### Current Version: 2.0.0
- Complete unified IPC system implementation
- Full plugin API extension support
- Comprehensive middleware system
- Real-time monitoring and metrics
- Enhanced security and validation
- React hooks and client library

This unified IPC system provides a robust, scalable, and maintainable foundation for the ChatLo application, enabling seamless communication between processes while maintaining security, performance, and extensibility.
