export interface LocalModel {
  id: string
  name: string
  provider: 'ollama' | 'lmstudio'
  size?: string
  modified?: string
  digest?: string
  details?: {
    format?: string
    family?: string
    families?: string[]
    parameter_size?: string
    quantization_level?: string
  }
}

export interface LocalModelProvider {
  name: string
  baseUrl: string
  isConnected: boolean
  models: LocalModel[]
}

class LocalModelService {
  private ollamaBaseUrl = 'http://localhost:11434'
  private lmStudioBaseUrl = 'http://localhost:1234'

  // Check if Ollama is available and get models
  async checkOllama(): Promise<{ connected: boolean; models: LocalModel[] }> {
    try {
      console.log('🔍 Checking Ollama at:', this.ollamaBaseUrl)

      const response = await fetch(`${this.ollamaBaseUrl}/api/tags`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json; charset=utf-8',
          'Accept': 'application/json; charset=utf-8'
        },
        signal: AbortSignal.timeout(5000)
      })

      if (!response.ok) {
        console.log('❌ Ollama response not OK:', response.status, response.statusText)
        return { connected: false, models: [] }
      }

      const data = await response.json()
      console.log('📦 Ollama raw response:', data)

      const models: LocalModel[] = data.models?.map((model: any) => ({
        id: `ollama:${model.name}`,
        name: model.name,
        provider: 'ollama' as const,
        size: model.size ? this.formatBytes(model.size) : undefined,
        modified: model.modified_at,
        digest: model.digest,
        details: model.details
      })) || []

      console.log(`✅ Ollama connected with ${models.length} models:`, models.map(m => m.name))
      return { connected: true, models }
    } catch (error) {
      console.log('❌ Ollama not available:', error instanceof Error ? error.message : error)
      return { connected: false, models: [] }
    }
  }

  // Check if LM Studio is available and get models
  async checkLMStudio(): Promise<{ connected: boolean; models: LocalModel[] }> {
    try {
      console.log('🔍 Checking LM Studio at:', this.lmStudioBaseUrl)

      const response = await fetch(`${this.lmStudioBaseUrl}/v1/models`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json; charset=utf-8',
          'Accept': 'application/json; charset=utf-8'
        },
        signal: AbortSignal.timeout(5000)
      })

      if (!response.ok) {
        console.log('❌ LM Studio response not OK:', response.status, response.statusText)
        return { connected: false, models: [] }
      }

      const data = await response.json()
      console.log('📦 LM Studio raw response:', data)

      const models: LocalModel[] = data.data?.map((model: any) => ({
        id: `lmstudio:${model.id}`,
        name: model.id,
        provider: 'lmstudio' as const,
        size: undefined, // LM Studio doesn't provide size info
        modified: undefined,
        digest: undefined,
        details: undefined
      })) || []

      console.log(`✅ LM Studio connected with ${models.length} models:`, models.map(m => m.name))
      return { connected: true, models }
    } catch (error) {
      console.log('❌ LM Studio not available:', error instanceof Error ? error.message : error)
      return { connected: false, models: [] }
    }
  }

  // Get all available local models
  async getAllLocalModels(): Promise<LocalModel[]> {
    const [ollamaResult, lmStudioResult] = await Promise.all([
      this.checkOllama(),
      this.checkLMStudio()
    ])

    return [...ollamaResult.models, ...lmStudioResult.models]
  }

  // Get provider status
  async getProviderStatus(): Promise<{
    ollama: LocalModelProvider
    lmstudio: LocalModelProvider
  }> {
    const [ollamaResult, lmStudioResult] = await Promise.all([
      this.checkOllama(),
      this.checkLMStudio()
    ])

    return {
      ollama: {
        name: 'Ollama',
        baseUrl: this.ollamaBaseUrl,
        isConnected: ollamaResult.connected,
        models: ollamaResult.models
      },
      lmstudio: {
        name: 'LM Studio',
        baseUrl: this.lmStudioBaseUrl,
        isConnected: lmStudioResult.connected,
        models: lmStudioResult.models
      }
    }
  }

  // Send message to local model
  async sendMessage(
    modelId: string,
    messages: Array<{ role: string; content: string }>,
    onChunk?: (chunk: string) => void
  ): Promise<string> {
    const [provider, modelName] = modelId.split(':')
    
    if (provider === 'ollama') {
      return this.sendOllamaMessage(modelName, messages, onChunk)
    } else if (provider === 'lmstudio') {
      return this.sendLMStudioMessage(modelName, messages, onChunk)
    } else {
      throw new Error(`Unknown local model provider: ${provider}`)
    }
  }

  // Send message to Ollama
  private async sendOllamaMessage(
    model: string,
    messages: Array<{ role: string; content: string }>,
    onChunk?: (chunk: string) => void
  ): Promise<string> {
    const response = await fetch(`${this.ollamaBaseUrl}/api/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model,
        messages,
        stream: !!onChunk
      })
    })

    if (!response.ok) {
      throw new Error(`Ollama API error: ${response.statusText}`)
    }

    if (onChunk && response.body) {
      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let fullResponse = ''

      try {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value)
          const lines = chunk.split('\n').filter(line => line.trim())

          for (const line of lines) {
            try {
              const data = JSON.parse(line)
              if (data.message?.content) {
                fullResponse += data.message.content
                onChunk(data.message.content)
              }
            } catch (e) {
              // Ignore malformed JSON
            }
          }
        }
      } finally {
        reader.releaseLock()
      }

      return fullResponse
    } else {
      const data = await response.json()
      return data.message?.content || ''
    }
  }

  // Send message to LM Studio
  private async sendLMStudioMessage(
    model: string,
    messages: Array<{ role: string; content: string }>,
    onChunk?: (chunk: string) => void
  ): Promise<string> {
    const response = await fetch(`${this.lmStudioBaseUrl}/v1/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model,
        messages,
        stream: !!onChunk
      })
    })

    if (!response.ok) {
      throw new Error(`LM Studio API error: ${response.statusText}`)
    }

    if (onChunk && response.body) {
      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let fullResponse = ''

      try {
        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          const chunk = decoder.decode(value)
          const lines = chunk.split('\n').filter(line => line.trim())

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6)
              if (data === '[DONE]') continue

              try {
                const parsed = JSON.parse(data)
                const content = parsed.choices?.[0]?.delta?.content
                if (content) {
                  fullResponse += content
                  onChunk(content)
                }
              } catch (e) {
                // Ignore malformed JSON
              }
            }
          }
        }
      } finally {
        reader.releaseLock()
      }

      return fullResponse
    } else {
      const data = await response.json()
      return data.choices?.[0]?.message?.content || ''
    }
  }

  // Utility function to format bytes
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }
}

export const localModelService = new LocalModelService()
