import React, { useEffect, useRef, useState } from 'react'
import { useSearchParams } from 'react-router-dom'
import { useAppStore } from '../store'
import MessageBubble from './MessageBubble'
import StreamingMessageBubble from './StreamingMessageBubble'
import InputArea from './InputArea'
import { Bot } from './Icons'

import { ContextVaultSelector } from './ContextVaultSelector'


const ChatArea: React.FC = () => {
  const [searchParams] = useSearchParams()
  const {
    currentConversationId,
    messages,
    conversations,
    isLoading,
    streamingMessage,
    togglePinMessage,
    setCurrentConversation,
    loadMessages
  } = useAppStore()

  // Context vault state
  const [selectedContextId, setSelectedContextId] = useState<string | null>(null)

  // Check for context parameter in URL
  useEffect(() => {
    const contextFromUrl = searchParams.get('context')
    if (contextFromUrl) {
      setSelectedContextId(contextFromUrl)
    }
  }, [searchParams])

  const handleContextSelect = (contextId: string | null) => {
    setSelectedContextId(contextId)
    // TODO: Update URL params and notify store about context change
    console.log('Selected context:', contextId)
  }

  const messagesEndRef = useRef<HTMLDivElement>(null)
  const [isVirtualized, setIsVirtualized] = useState(false)
  const messagesContainerRef = useRef<HTMLDivElement>(null)
  const [renderRange, setRenderRange] = useState({ start: 0, end: 50 })
  const itemHeight = 80 // Default height estimate for virtual scrolling
  const [containerHeight, setContainerHeight] = useState(0)

  const currentConversation = conversations.find(c => c.id === currentConversationId)

  // Handle conversation parameter from URL (e.g., from history page)
  useEffect(() => {
    const conversationParam = searchParams.get('conversation')
    if (conversationParam && conversationParam !== currentConversationId) {
      const conversation = conversations.find(c => c.id === conversationParam)
      if (conversation) {
        setCurrentConversation(conversationParam)
        loadMessages(conversationParam)
      }
    }
  }, [searchParams, conversations, currentConversationId, setCurrentConversation, loadMessages])

  const scrollToBottom = () => {
    // Use requestAnimationFrame for better timing with DOM updates
    requestAnimationFrame(() => {
      setTimeout(() => {
        if (messagesEndRef.current) {
          messagesEndRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'end',
            inline: 'nearest'
          })
        }
      }, 50)
    })
  }

  const handleRegenerate = async (messageId: string) => {
    console.log('Regenerating message:', messageId)
    const { sendMessage, messages, currentConversationId } = useAppStore.getState()
    const messageToRegenerate = messages.find(m => m.id === messageId)
    console.log('Message to regenerate:', messageToRegenerate)
    
    if (messageToRegenerate && currentConversationId) {
      // Find the user message that preceded the assistant's message
      const precedingUserMessage = messages
        .slice(0, messages.findIndex(m => m.id === messageId))
        .reverse()
        .find(m => m.role === 'user')
      console.log('Preceding user message:', precedingUserMessage)

      if (precedingUserMessage) {
        await sendMessage(precedingUserMessage.content)
      } else {
        console.error('Could not find preceding user message to regenerate from.')
      }
    } else {
      console.error('Could not find message to regenerate or no current conversation.')
    }
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages, streamingMessage])

  // Additional effect to handle streaming message updates
  useEffect(() => {
    if (streamingMessage !== null) {
      scrollToBottom()
    }
  }, [streamingMessage])

  // Virtual scrolling implementation
  useEffect(() => {
    const container = messagesContainerRef.current
    if (!container) return

    const updateContainerHeight = () => {
      setContainerHeight(container.clientHeight)
    }

    updateContainerHeight()
    window.addEventListener('resize', updateContainerHeight)

    return () => window.removeEventListener('resize', updateContainerHeight)
  }, [])

  useEffect(() => {
    // Enable virtualization for large message lists (>100 messages)
    setIsVirtualized(messages.length > 100)
    
    if (isVirtualized) {
      // Calculate visible range based on scroll position
      const calculateVisibleRange = () => {
        const container = messagesContainerRef.current
        if (!container || !containerHeight) return

        const scrollTop = container.scrollTop
        const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - 5)
        const visibleCount = Math.ceil(containerHeight / itemHeight) + 10
        const endIndex = Math.min(messages.length, startIndex + visibleCount)

        setRenderRange({ start: startIndex, end: endIndex })
      }

      calculateVisibleRange()
      
      const container = messagesContainerRef.current
      if (container) {
        container.addEventListener('scroll', calculateVisibleRange)
        return () => container.removeEventListener('scroll', calculateVisibleRange)
      }
    } else {
      // For small lists, render all messages
      setRenderRange({ start: 0, end: messages.length })
    }
  }, [messages.length, isVirtualized, containerHeight, itemHeight])

  const renderVirtualizedMessages = () => {
    const { start, end } = renderRange
    const itemsToRender = messages.slice(start, end)
    
    return itemsToRender.map((message, index) => {
      const actualIndex = start + index
      return (
        <div
          key={message.id}
          style={{
            position: 'absolute',
            top: `${actualIndex * itemHeight}px`,
            width: '100%',
          }}
        >
          <MessageBubble
            message={message}
            onRegenerate={handleRegenerate}
            onPinMessage={togglePinMessage}
          />
        </div>
      )
    })
  }

  if (!currentConversationId) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="flex justify-center mb-6">
            <img src="/logo2_sq.svg" alt="ChatLo Logo" width="150" height="150" />
          </div>
          <h2 className="text-2xl font-semibold mb-3 text-supplement1">Gain Back Control with Local-first AI Chat</h2>
          <p className="text-gray-400 mb-8 max-w-md">
            Experience this Privacy-Focused yet Powerful AI Chatting!
          </p>
          <button
            onClick={() => {
              try {
                console.log('Starting new draft conversation from welcome screen...')
                const store = useAppStore.getState()
                const draftId = store.createDraftConversation('New Conversation')
                console.log('Created draft conversation:', draftId)
                store.setCurrentConversation(draftId)
                console.log('Welcome screen draft conversation setup complete')
              } catch (error) {
                console.error('Failed to create draft conversation from welcome:', error)
                alert('Failed to create conversation: ' + (error instanceof Error ? error.message : 'Unknown error'))
              }
            }}
            className="u1-button-primary"
          >
            Start New Conversation
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col h-full">
      {/* Chat Header with Context Vault Selector */}
      <header className="flex-shrink-0 flex items-center justify-between px-4 md:px-6 py-3 border-b border-tertiary/30 bg-gray-900/40 backdrop-blur-sm">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <h2 className="text-sm font-medium text-supplement1 truncate">
              {currentConversation?.title || 'Conversation'}
            </h2>
            <span className="text-xs text-gray-500">
              • {messages.length}
            </span>
          </div>

          {/* Context Vault Selector */}
          <ContextVaultSelector
            selectedContextId={selectedContextId || undefined}
            onContextChange={handleContextSelect}
          />
        </div>

        <div className="flex items-center gap-2">
          <button className="u1-button-ghost p-1.5" title="More options">
            <i className="fa-solid fa-ellipsis-vertical text-xs"></i>
          </button>
        </div>
      </header>

      {/* Messages */}
      <main
        ref={messagesContainerRef}
        className="flex-1 overflow-y-auto px-4 md:px-8 py-6 min-h-0"
      >
        <div className="max-w-4xl mx-auto">
          {messages.length === 0 ? (
            <div className="text-center py-12">
              <div className="h-12 w-12 rounded-full bg-gray-800 flex items-center justify-center mx-auto mb-4">
                <Bot className="h-6 w-6 text-gray-400" />
              </div>
              <p className="text-gray-400">
                No messages yet. Start the conversation!
              </p>
            </div>
          ) : isVirtualized ? (
            <div
              style={{
                position: 'relative',
                height: `${messages.length * itemHeight}px`,
              }}
            >
              {renderVirtualizedMessages()}
            </div>
          ) : (
            <div className="space-y-6">
              {messages.map((message) => (
                <MessageBubble
                  key={message.id}
                  message={message}
                  onRegenerate={handleRegenerate}
                  onPinMessage={togglePinMessage}
                />
              ))}
            </div>
          )}

          {/* Streaming message */}
          {streamingMessage !== null && (
            <StreamingMessageBubble content={streamingMessage} />
          )}

          {/* Loading indicator (when not streaming) */}
          {isLoading && streamingMessage === null && (
            <div className="flex items-start gap-3">
              <div className="h-8 w-8 rounded-full bg-secondary flex items-center justify-center">
                <Bot className="h-4 w-4 text-white" />
              </div>
              <div className="max-w-md">
                <div className="bg-gray-800 rounded-lg px-4 py-2">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            </div>
          )}
           
          <div ref={messagesEndRef} />
        </div>
      </main>

      {/* Input Area */}
      <div className="flex-shrink-0">
        <InputArea />
      </div>
    </div>
  )
}

export default ChatArea

