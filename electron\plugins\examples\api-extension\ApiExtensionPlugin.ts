/**
 * API Extension Plugin Example
 * Demonstrates how to create custom API endpoints using the plugin system
 */

import { BasePlugin, PluginCapability } from '../../types'
import { APIExtension, APIRegistry, PluginAPINamespace, PluginEndpoint } from '../../extensionPoints'

export default class ApiExtensionPlugin implements BasePlugin, APIExtension {
  id = 'api-extension-example'
  name = 'API Extension Example'
  version = '1.0.0'
  description = 'Example plugin demonstrating API extension capabilities'
  author = 'ChatLo Team'
  
  async initialize(): Promise<void> {
    console.log('API Extension Example Plugin initialized')
  }
  
  getCapabilities(): PluginCapability[] {
    return [PluginCapability.API_EXTENSION]
  }
  
  getDefaultConfig(): Record<string, any> {
    return {
      enabled: true,
      maxRequests: 100,
      rateLimitWindow: 60000 // 1 minute
    }
  }
  
  // Get plugin namespace (automatically generated as plugin_${pluginId})
  getNamespace(): string {
    return `plugin_${this.id}`
  }
  
  // Register custom API endpoints
  registerEndpoints(apiRegistry: APIRegistry): PluginAPINamespace {
    const namespace = this.getNamespace()
    
    // Register a simple greeting endpoint
    apiRegistry.registerEndpoint(
      namespace,
      'greet',
      async (name: string) => {
        return {
          success: true,
          message: `Hello ${name || 'World'} from ${this.name}!`,
          timestamp: new Date().toISOString()
        }
      },
      {
        validator: (name: string) => {
          if (name && typeof name !== 'string') {
            throw new Error('Name must be a string')
          }
        },
        description: 'Returns a greeting message'
      }
    )
    
    // Register a data processing endpoint
    apiRegistry.registerEndpoint(
      namespace,
      'processData',
      async (data: any[]) => {
        try {
          const processed = data.map((item, index) => ({
            id: index,
            original: item,
            processed: typeof item === 'string' ? item.toUpperCase() : String(item),
            timestamp: new Date().toISOString()
          }))
          
          return {
            success: true,
            data: processed,
            count: processed.length
          }
        } catch (error: any) {
          return {
            success: false,
            error: error.message
          }
        }
      },
      {
        validator: (data: any[]) => {
          if (!Array.isArray(data)) {
            throw new Error('Data must be an array')
          }
        },
        description: 'Processes an array of data items'
      }
    )
    
    // Register a configuration endpoint
    apiRegistry.registerEndpoint(
      namespace,
      'getStatus',
      async () => {
        const config = this.getDefaultConfig()
        return {
          success: true,
          plugin: {
            id: this.id,
            name: this.name,
            version: this.version,
            description: this.description
          },
          config,
          status: 'active',
          uptime: process.uptime()
        }
      },
      {
        description: 'Returns plugin status and configuration'
      }
    )
    
    return {
      pluginId: this.id,
      namespace,
      endpoints: [
        {
          name: 'greet',
          handler: () => {}, // Placeholder, actual handler registered above
          description: 'Returns a greeting message'
        },
        {
          name: 'processData',
          handler: () => {}, // Placeholder, actual handler registered above
          description: 'Processes an array of data items'
        },
        {
          name: 'getStatus',
          handler: () => {}, // Placeholder, actual handler registered above
          description: 'Returns plugin status and configuration'
        }
      ],
      middleware: []
    }
  }
  
  // Provide plugin-specific middleware
  provideMiddleware(): Function[] {
    return [
      // Rate limiting middleware
      (req: any, res: any, next: Function) => {
        const config = this.getDefaultConfig()
        // Simple rate limiting logic would go here
        console.log(`[${this.name}] Processing request with rate limit: ${config.maxRequests}/${config.rateLimitWindow}ms`)
        next()
      },
      
      // Logging middleware
      (req: any, res: any, next: Function) => {
        console.log(`[${this.name}] API call: ${req.endpoint} at ${new Date().toISOString()}`)
        next()
      }
    ]
  }
  
  // Dynamic endpoint discovery
  async discoverEndpoints(): Promise<PluginEndpoint[]> {
    // This could dynamically discover endpoints based on configuration,
    // external services, or runtime conditions
    return [
      {
        name: 'dynamicEndpoint',
        handler: async () => ({
          success: true,
          message: 'This endpoint was discovered dynamically!',
          discoveredAt: new Date().toISOString()
        }),
        description: 'A dynamically discovered endpoint'
      }
    ]
  }
  
  // Endpoint validation
  validateEndpoint(endpoint: string, args: any[]): boolean {
    const validEndpoints = ['greet', 'processData', 'getStatus', 'dynamicEndpoint']
    return validEndpoints.includes(endpoint)
  }
}
