/**
 * Plugin API Discovery Service
 * Handles dynamic discovery and management of plugin API endpoints
 */

export interface PluginEndpointInfo {
  name: string
  description?: string
  hasValidator: boolean
  hasMiddleware: boolean
}

export interface PluginAPIInfo {
  pluginId: string
  pluginName?: string
  namespace: string
  endpoints: PluginEndpointInfo[]
  version?: string
}

export interface PluginInfo {
  id: string
  name: string
  version: string
  state: string
  capabilities: string[]
}

export class PluginAPIDiscovery {
  private cachedAPIInfo: PluginAPIInfo[] = []
  private lastDiscoveryTime: number = 0
  private readonly CACHE_DURATION = 30000 // 30 seconds

  /**
   * Discover all available plugin APIs
   */
  async discoverPluginAPIs(forceRefresh: boolean = false): Promise<PluginAPIInfo[]> {
    const now = Date.now()
    
    // Return cached data if still valid and not forcing refresh
    if (!forceRefresh && this.cachedAPIInfo.length > 0 && (now - this.lastDiscoveryTime) < this.CACHE_DURATION) {
      return this.cachedAPIInfo
    }

    try {
      // Get all plugins
      const plugins: PluginInfo[] = await window.electronAPI?.plugins?.getAll() || []
      
      // Get all API endpoints at once for efficiency
      const allEndpointsResult = await window.electronAPI?.plugins?.getAllAPIEndpoints()
      
      if (!allEndpointsResult?.success) {
        console.warn('Failed to get plugin API endpoints:', allEndpointsResult?.error)
        return []
      }

      const apiInfo: PluginAPIInfo[] = []

      // Match plugins with their API endpoints
      for (const endpointInfo of allEndpointsResult.apiInfo || []) {
        const plugin = plugins.find(p => p.id === endpointInfo.pluginId)
        
        if (plugin && plugin.capabilities.includes('api_extension')) {
          apiInfo.push({
            pluginId: plugin.id,
            pluginName: plugin.name,
            namespace: endpointInfo.namespace,
            endpoints: endpointInfo.endpoints,
            version: plugin.version
          })
        }
      }

      // Cache the results
      this.cachedAPIInfo = apiInfo
      this.lastDiscoveryTime = now

      console.log(`[PluginAPIDiscovery] Discovered ${apiInfo.length} plugins with API extensions`)
      return apiInfo

    } catch (error) {
      console.error('Error discovering plugin APIs:', error)
      return []
    }
  }

  /**
   * Get API endpoints for a specific plugin
   */
  async getPluginAPIEndpoints(pluginId: string): Promise<PluginAPIInfo | null> {
    try {
      const result = await window.electronAPI?.plugins?.getAPIEndpoints(pluginId)
      
      if (!result?.success) {
        console.warn(`Failed to get API endpoints for plugin ${pluginId}:`, result?.error)
        return null
      }

      // Get plugin info for additional details
      const plugins: PluginInfo[] = await window.electronAPI?.plugins?.getAll() || []
      const plugin = plugins.find(p => p.id === pluginId)

      return {
        pluginId,
        pluginName: plugin?.name,
        namespace: result.namespace!,
        endpoints: result.endpoints!,
        version: plugin?.version
      }

    } catch (error) {
      console.error(`Error getting API endpoints for plugin ${pluginId}:`, error)
      return null
    }
  }

  /**
   * Call a plugin API endpoint
   */
  async callPluginAPI(pluginId: string, endpoint: string, ...args: any[]): Promise<any> {
    try {
      const namespace = `plugin_${pluginId}`
      const fullEndpoint = `${namespace}:${endpoint}`
      
      // Use the generic IPC invoke to call the plugin endpoint
      const result = await window.electronAPI?.invoke?.(fullEndpoint, ...args)
      
      return result
    } catch (error) {
      console.error(`Error calling plugin API ${pluginId}:${endpoint}:`, error)
      throw error
    }
  }

  /**
   * Check if a plugin has API extension capability
   */
  async hasAPIExtension(pluginId: string): Promise<boolean> {
    try {
      const plugins: PluginInfo[] = await window.electronAPI?.plugins?.getAll() || []
      const plugin = plugins.find(p => p.id === pluginId)
      
      return plugin?.capabilities.includes('api_extension') || false
    } catch (error) {
      console.error(`Error checking API extension for plugin ${pluginId}:`, error)
      return false
    }
  }

  /**
   * Get cached API info (doesn't trigger network calls)
   */
  getCachedAPIInfo(): PluginAPIInfo[] {
    return [...this.cachedAPIInfo]
  }

  /**
   * Clear the cache and force next discovery to refresh
   */
  clearCache(): void {
    this.cachedAPIInfo = []
    this.lastDiscoveryTime = 0
  }

  /**
   * Get available plugin capabilities
   */
  async getAvailableCapabilities(): Promise<string[]> {
    try {
      const result = await window.electronAPI?.plugins?.getCapabilities()
      
      if (result?.success) {
        return result.capabilities || []
      }
      
      return []
    } catch (error) {
      console.error('Error getting plugin capabilities:', error)
      return []
    }
  }

  /**
   * Generate documentation for plugin APIs
   */
  generateAPIDocumentation(apiInfo: PluginAPIInfo[]): string {
    let documentation = '# Plugin API Documentation\n\n'
    
    for (const plugin of apiInfo) {
      documentation += `## ${plugin.pluginName || plugin.pluginId} (v${plugin.version || 'unknown'})\n\n`
      documentation += `**Namespace:** \`${plugin.namespace}\`\n\n`
      
      if (plugin.endpoints.length > 0) {
        documentation += '### Endpoints\n\n'
        
        for (const endpoint of plugin.endpoints) {
          documentation += `#### \`${endpoint.name}\`\n\n`
          
          if (endpoint.description) {
            documentation += `${endpoint.description}\n\n`
          }
          
          const features = []
          if (endpoint.hasValidator) features.push('Input Validation')
          if (endpoint.hasMiddleware) features.push('Custom Middleware')
          
          if (features.length > 0) {
            documentation += `**Features:** ${features.join(', ')}\n\n`
          }
          
          documentation += `**Usage:**\n\`\`\`javascript\n`
          documentation += `const result = await pluginAPIDiscovery.callPluginAPI('${plugin.pluginId}', '${endpoint.name}', ...args)\n`
          documentation += `\`\`\`\n\n`
        }
      } else {
        documentation += 'No endpoints available.\n\n'
      }
      
      documentation += '---\n\n'
    }
    
    return documentation
  }
}

// Create a singleton instance
export const pluginAPIDiscovery = new PluginAPIDiscovery()

// Make it available globally for debugging
if (typeof window !== 'undefined') {
  (window as any).pluginAPIDiscovery = pluginAPIDiscovery
}
