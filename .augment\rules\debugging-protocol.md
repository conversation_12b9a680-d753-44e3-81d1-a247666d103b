---
type: "agent_requested"
description: "ChatLo Debugging Protocol"
priority: "high"
context: ["debugging", "terminal_monitoring", "development_workflow"]
authority: "debugging_oversight"
---

# ChatLo Debugging Protocol

## Overview
This protocol establishes standardized debugging practices for the ChatLo application, emphasizing proactive terminal monitoring and accessible status communication for non-technical stakeholders.

## Core Principles
- **Proactive Monitoring**: Continuous observation of application behavior
- **Accessible Communication**: Clear, non-technical status updates
- **Context Awareness**: Link technical events to user actions and features
- **Efficiency Focus**: Rapid issue identification and resolution

## Terminal Monitoring Protocol

### 1. Automated Monitoring Setup
```bash
# Primary monitoring command
npm run dev

# Monitor both Electron and React processes
# Watch for console output patterns
# Track application state changes
```

### 2. Issue Classification System

#### CRITICAL 🔴
- Application crashes or hangs
- Database corruption or connection failures
- File system access errors
- Security vulnerabilities
- **Action**: Immediate notification with suggested resolution

#### WARNING ⚠️
- Performance degradation (>3s response times)
- API timeouts or rate limiting
- Model connection issues
- Memory usage spikes
- **Action**: Monitor closely, prepare mitigation strategies

#### INFO ℹ️
- Successful operations completion
- State changes and transitions
- User action confirmations
- Feature activations
- **Action**: Log for reference, report on request

#### DEBUG 🔍
- Detailed technical traces
- Variable state dumps
- Function call sequences
- Network request details
- **Action**: Available for deep investigation

## ChatLo-Specific Monitoring Areas

### 1. Model Connection Health
- OpenRouter API connectivity and response times
- Local LM Studio/Ollama service status
- Model loading and switching operations
- Private mode transitions

### 2. File Processing Pipeline
- Document upload and validation
- Vectorization progress and completion
- Context vault operations
- File indexing and search functionality

### 3. Database Operations
- Chat history persistence
- Settings and preferences storage
- Model favorites and configurations
- Performance metrics tracking

### 4. UI Responsiveness
- React component rendering times
- Electron window management
- User interaction response latency
- Memory usage and cleanup

### 5. Update System
- OTA model manifest updates
- Application version checking
- Download progress monitoring
- Installation success verification

## Communication Standards

### Status Update Format
```
[TIMESTAMP] [LEVEL] [AREA] - [MESSAGE]
Context: [User action or feature context]
Impact: [Effect on user experience]
Action: [Recommended next steps if applicable]
```

### Example Communications
```
[14:35:22] INFO MODEL - OpenRouter connection established
Context: User switching to GPT-4 model
Impact: Model selection now available
Action: None required

[14:36:15] WARNING FILE - Document processing taking longer than expected
Context: User uploaded 50MB PDF file
Impact: UI may appear unresponsive during processing
Action: Consider implementing progress indicator

[14:37:03] CRITICAL DB - Database connection lost
Context: User attempting to save chat history
Impact: Data loss risk, application may crash
Action: Restart application, check database file integrity
```

## Implementation Workflow

### Session Start
1. Launch terminal monitoring with `npm run dev`
2. Verify all services are running (Electron, React, Database)
3. Confirm model connections (OpenRouter, local services)
4. Report initial system status

### During Development
1. Monitor console output continuously
2. Parse and classify log messages
3. Provide real-time status updates
4. Alert immediately on critical issues
5. Maintain context awareness of user actions

### Session End
1. Generate session summary
2. Document any recurring issues
3. Recommend follow-up actions
4. Archive monitoring logs

## Success Metrics
- Time from issue occurrence to notification: <30 seconds
- Accuracy of issue classification: >95%
- User satisfaction with debugging communication
- Reduction in manual log analysis time
- Improved development workflow efficiency

## Continuous Improvement
- Track patterns in recurring issues
- Refine classification criteria based on experience
- Update monitoring priorities based on user feedback
- Enhance automation capabilities over time
